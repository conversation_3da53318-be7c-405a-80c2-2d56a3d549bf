import React, { useState } from 'react';
import { TermsAcceptanceModal } from './TermsAcceptanceModal';

interface TermsAcceptanceCheckboxProps {
  acceptedTerms: boolean;
  acceptedPrivacy: boolean;
  onTermsChange: (accepted: boolean) => void;
  onPrivacyChange: (accepted: boolean) => void;
  termsType: 'registration' | 'share_purchase' | 'general';
  showPrivacyPolicy?: boolean;
  requireBoth?: boolean;
  className?: string;
  error?: string;
}

export const TermsAcceptanceCheckbox: React.FC<TermsAcceptanceCheckboxProps> = ({
  acceptedTerms,
  acceptedPrivacy,
  onTermsChange,
  onPrivacyChange,
  termsType,
  showPrivacyPolicy = true,
  requireBoth = true,
  className = '',
  error
}) => {
  const [showModal, setShowModal] = useState(false);

  const handleModalAccept = (modalAcceptedTerms: boolean, modalAcceptedPrivacy: boolean) => {
    onTermsChange(modalAcceptedTerms);
    if (showPrivacyPolicy) {
      onPrivacyChange(modalAcceptedPrivacy);
    }
    setShowModal(false);
  };

  const openTermsModal = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowModal(true);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Terms & Conditions Checkbox */}
      <label className="flex items-start space-x-3 cursor-pointer">
        <input
          type="checkbox"
          checked={acceptedTerms}
          onChange={(e) => onTermsChange(e.target.checked)}
          className="mt-1 w-5 h-5 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold focus:ring-2"
        />
        <span className="text-sm text-gray-300 leading-relaxed">
          I have read and agree to the{' '}
          <button
            type="button"
            onClick={openTermsModal}
            className="text-gold hover:underline font-medium"
          >
            Terms & Conditions
          </button>
          {' '}(Version 2.0) <span className="text-red-400">*</span>
        </span>
      </label>

      {/* Privacy Policy Checkbox */}
      {showPrivacyPolicy && (
        <label className="flex items-start space-x-3 cursor-pointer">
          <input
            type="checkbox"
            checked={acceptedPrivacy}
            onChange={(e) => onPrivacyChange(e.target.checked)}
            className="mt-1 w-5 h-5 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold focus:ring-2"
          />
          <span className="text-sm text-gray-300 leading-relaxed">
            I have read and agree to the{' '}
            <button
              type="button"
              onClick={openTermsModal}
              className="text-gold hover:underline font-medium"
            >
              Privacy Policy
            </button>
            {' '}(Version 1.5) <span className="text-red-400">*</span>
          </span>
        </label>
      )}

      {/* Legal Notice */}
      <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
        <p className="text-xs text-blue-200">
          <strong>Legal Notice:</strong> By checking these boxes, you acknowledge that you have read, 
          understood, and agree to be legally bound by our terms. This constitutes a legally binding 
          agreement under South African law.
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3">
          <p className="text-sm text-red-300">
            ❌ {error}
          </p>
        </div>
      )}

      {/* Validation Helper */}
      {!acceptedTerms && (
        <div className="bg-amber-900/20 border border-amber-500/30 rounded-lg p-3">
          <p className="text-xs text-amber-200">
            ⚠️ You must accept the Terms & Conditions to continue.
            {showPrivacyPolicy && requireBoth && ' You must also accept the Privacy Policy.'}
          </p>
        </div>
      )}

      {/* Terms Acceptance Modal */}
      <TermsAcceptanceModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onAccept={handleModalAccept}
        termsType={termsType}
        showPrivacyPolicy={showPrivacyPolicy}
        requireBoth={requireBoth}
      />
    </div>
  );
};
