import React, { useState, useEffect } from 'react'
import { getServiceRoleClient } from '../../lib/supabase'

interface CommissionAuditRecord {
  user_id: number
  username: string
  email: string
  payment_amount: number
  payment_date: string
  expected_usdt_commission: number
  actual_usdt_commission: number
  expected_share_commission: number
  actual_share_commission: number
  status: 'complete' | 'incomplete' | 'missing'
  share_purchase_id: string
  phase_id: number
  phase_name: string
  sponsor_id?: number
  sponsor_username?: string
}

interface CommissionCorrection {
  user_id: number
  payment_amount: number
  usdt_commission: number
  share_commission: number
  correction_reason: string
}

const CommissionAuditDashboard: React.FC = () => {
  const [auditRecords, setAuditRecords] = useState<CommissionAuditRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info', text: string } | null>(null)
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    to: new Date().toISOString().split('T')[0] // today
  })
  const [filterUserId, setFilterUserId] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'complete' | 'incomplete' | 'missing'>('all')
  const [showCorrectionForm, setShowCorrectionForm] = useState(false)
  const [correctionData, setCorrectionData] = useState<CommissionCorrection>({
    user_id: 0,
    payment_amount: 0,
    usdt_commission: 0,
    share_commission: 0,
    correction_reason: ''
  })

  useEffect(() => {
    loadAuditData()
  }, [dateRange, filterUserId, filterStatus])

  const loadAuditData = async () => {
    setLoading(true)
    setMessage(null)

    try {
      const serviceClient = getServiceRoleClient()

      // Get all share purchases in date range
      let query = serviceClient
        .from('aureus_share_purchases')
        .select(`
          id,
          user_id,
          total_amount,
          shares_purchased,
          phase_id,
          purchase_date,
          created_at,
          users!inner(
            id,
            username,
            email
          ),
          investment_phases!inner(
            id,
            phase_name,
            price_per_share
          )
        `)
        .eq('status', 'active')
        .gte('purchase_date', `${dateRange.from}T00:00:00.000Z`)
        .lte('purchase_date', `${dateRange.to}T23:59:59.999Z`)
        .order('purchase_date', { ascending: false })

      if (filterUserId) {
        query = query.eq('user_id', parseInt(filterUserId))
      }

      const { data: purchases, error: purchaseError } = await query

      if (purchaseError) throw purchaseError

      // Get all commission transactions for these purchases
      const purchaseIds = purchases?.map(p => p.id) || []
      const { data: commissions, error: commissionError } = await serviceClient
        .from('commission_transactions')
        .select(`
          *,
          users!referrer_id(
            id,
            username
          )
        `)
        .in('share_purchase_id', purchaseIds)

      if (commissionError) throw commissionError

      // Get referral relationships
      const userIds = purchases?.map(p => p.user_id) || []
      const { data: referrals, error: referralError } = await serviceClient
        .from('referrals')
        .select(`
          referred_id,
          referrer_id,
          users!referrer_id(
            id,
            username
          )
        `)
        .in('referred_id', userIds)
        .eq('status', 'active')

      if (referralError) throw referralError

      // Process audit records
      const auditRecords: CommissionAuditRecord[] = (purchases || []).map(purchase => {
        const referral = referrals?.find(r => r.referred_id === purchase.user_id)
        const commission = commissions?.find(c => c.share_purchase_id === purchase.id)
        
        // Calculate expected commissions (15% for Pre Sale phase)
        const expectedUsdtCommission = purchase.total_amount * 0.15
        const expectedShareCommission = purchase.shares_purchased * 0.15

        // Get actual commissions
        const actualUsdtCommission = commission?.usdt_commission || 0
        const actualShareCommission = commission?.share_commission || 0

        // Determine status
        let status: 'complete' | 'incomplete' | 'missing' = 'missing'
        if (commission) {
          if (actualUsdtCommission === expectedUsdtCommission && actualShareCommission === expectedShareCommission) {
            status = 'complete'
          } else {
            status = 'incomplete'
          }
        }

        return {
          user_id: purchase.user_id,
          username: purchase.users.username,
          email: purchase.users.email,
          payment_amount: parseFloat(purchase.total_amount),
          payment_date: purchase.purchase_date || purchase.created_at,
          expected_usdt_commission: expectedUsdtCommission,
          actual_usdt_commission: actualUsdtCommission,
          expected_share_commission: expectedShareCommission,
          actual_share_commission: actualShareCommission,
          status,
          share_purchase_id: purchase.id,
          phase_id: purchase.phase_id,
          phase_name: purchase.investment_phases.phase_name,
          sponsor_id: referral?.referrer_id,
          sponsor_username: referral?.users?.username
        }
      })

      // Apply status filter
      const filteredRecords = filterStatus === 'all' 
        ? auditRecords 
        : auditRecords.filter(record => record.status === filterStatus)

      setAuditRecords(filteredRecords)

      const totalRecords = auditRecords.length
      const completeRecords = auditRecords.filter(r => r.status === 'complete').length
      const incompleteRecords = auditRecords.filter(r => r.status === 'incomplete').length
      const missingRecords = auditRecords.filter(r => r.status === 'missing').length

      setMessage({
        type: 'info',
        text: `Loaded ${totalRecords} records: ${completeRecords} complete, ${incompleteRecords} incomplete, ${missingRecords} missing commissions`
      })

    } catch (error) {
      console.error('Error loading audit data:', error)
      setMessage({
        type: 'error',
        text: `Error loading audit data: ${error.message}`
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCorrection = async (record: CommissionAuditRecord) => {
    setCorrectionData({
      user_id: record.user_id,
      payment_amount: record.payment_amount,
      usdt_commission: record.expected_usdt_commission - record.actual_usdt_commission,
      share_commission: record.expected_share_commission - record.actual_share_commission,
      correction_reason: `Missing commission correction for ${record.username}`
    })
    setShowCorrectionForm(true)
  }

  const submitCorrection = async () => {
    if (!correctionData.correction_reason.trim()) {
      setMessage({ type: 'error', text: 'Correction reason is required' })
      return
    }

    setLoading(true)
    try {
      const serviceClient = getServiceRoleClient()

      // Get referral relationship
      const { data: referral, error: referralError } = await serviceClient
        .from('referrals')
        .select('referrer_id')
        .eq('referred_id', correctionData.user_id)
        .eq('status', 'active')
        .single()

      if (referralError || !referral) {
        throw new Error('No active referral relationship found for this user')
      }

      // Get current commission balance for audit trail
      const { data: currentBalance, error: balanceQueryError } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', referral.referrer_id)
        .maybeSingle()

      const oldBalance = currentBalance || {
        usdt_balance: '0',
        share_balance: '0',
        total_earned_usdt: '0',
        total_earned_shares: '0'
      }

      // Create commission transaction
      const { data: commissionTransaction, error: commissionError } = await serviceClient
        .from('commission_transactions')
        .insert({
          referrer_id: referral.referrer_id,
          referred_id: correctionData.user_id,
          commission_rate: 15.00,
          share_purchase_amount: correctionData.payment_amount,
          usdt_commission: correctionData.usdt_commission,
          share_commission: correctionData.share_commission,
          phase_id: 1, // Pre Sale phase
          status: 'approved',
          payment_date: new Date().toISOString()
        })
        .select()
        .single()

      if (commissionError) throw commissionError

      // Calculate new balance values
      const newBalance = {
        user_id: referral.referrer_id,
        usdt_balance: parseFloat(oldBalance.usdt_balance || '0') + correctionData.usdt_commission,
        share_balance: parseFloat(oldBalance.share_balance || '0') + correctionData.share_commission,
        total_earned_usdt: parseFloat(oldBalance.total_earned_usdt || '0') + correctionData.usdt_commission,
        total_earned_shares: parseFloat(oldBalance.total_earned_shares || '0') + correctionData.share_commission,
        last_updated: new Date().toISOString()
      }

      // Update commission balance
      const { error: balanceError } = await serviceClient
        .from('commission_balances')
        .upsert(newBalance, { onConflict: 'user_id' })

      if (balanceError) throw balanceError

      // Create audit log entry
      const { error: auditError } = await serviceClient
        .from('audit_log')
        .insert({
          admin_id: 4, // TODO: Get actual admin ID from context
          action_type: 'COMMISSION_CORRECTION',
          target_user_id: correctionData.user_id,
          old_values: {
            commission_balance: oldBalance,
            commission_transaction: null
          },
          new_values: {
            commission_balance: newBalance,
            commission_transaction: commissionTransaction
          },
          reason: correctionData.correction_reason,
          rollback_data: {
            commission_transaction_id: commissionTransaction.id,
            referrer_id: referral.referrer_id,
            usdt_correction: correctionData.usdt_commission,
            share_correction: correctionData.share_commission,
            previous_balance: oldBalance
          }
        })

      if (auditError) {
        console.warn('Failed to create audit log entry:', auditError)
        // Don't fail the correction if audit logging fails
      }

      console.log(`[COMMISSION_CORRECTION] Admin: 4, Target: ${correctionData.user_id}, USDT: $${correctionData.usdt_commission}, Shares: ${correctionData.share_commission}, Reason: ${correctionData.correction_reason}, Timestamp: ${new Date().toISOString()}`)

      setMessage({
        type: 'success',
        text: `Commission correction applied successfully. Transaction ID: ${commissionTransaction.id}`
      })

      setShowCorrectionForm(false)
      setCorrectionData({
        user_id: 0,
        payment_amount: 0,
        usdt_commission: 0,
        share_commission: 0,
        correction_reason: ''
      })

      // Reload audit data
      await loadAuditData()

    } catch (error) {
      console.error('Error applying correction:', error)
      setMessage({
        type: 'error',
        text: `Error applying correction: ${error.message}`
      })
    } finally {
      setLoading(false)
    }
  }

  const correctAllMissingCommissions = async () => {
    if (!confirm('This will create commission transactions for ALL missing commissions. This action cannot be undone. Continue?')) {
      return
    }

    setLoading(true)
    try {
      const serviceClient = getServiceRoleClient()
      let correctionCount = 0
      const auditEntries = []

      // Get all missing commissions
      const missingCommissions = auditRecords.filter(record =>
        record.audit_status === 'MISSING_COMMISSION' &&
        record.expected_usdt_commission > 0
      )

      console.log(`Processing ${missingCommissions.length} missing commission transactions...`)

      for (const record of missingCommissions) {
        try {
          // Get referral relationship
          const { data: referral, error: referralError } = await serviceClient
            .from('referrals')
            .select('referrer_id')
            .eq('referred_id', record.user_id)
            .eq('status', 'active')
            .single()

          if (referralError || !referral) {
            console.warn(`No referral found for user ${record.user_id}`)
            continue
          }

          // Create commission transaction
          const { data: commissionTransaction, error: commissionError } = await serviceClient
            .from('commission_transactions')
            .insert({
              referrer_id: referral.referrer_id,
              referred_id: record.user_id,
              share_purchase_id: record.share_purchase_id,
              commission_rate: 15.00,
              share_purchase_amount: record.payment_amount,
              usdt_commission: record.expected_usdt_commission,
              share_commission: record.expected_share_commission,
              phase_id: 1,
              status: 'approved',
              payment_date: record.purchase_date
            })
            .select()
            .single()

          if (commissionError) {
            console.error(`Failed to create commission for user ${record.user_id}:`, commissionError)
            continue
          }

          // Update commission balance
          const { data: currentBalance } = await serviceClient
            .from('commission_balances')
            .select('*')
            .eq('user_id', referral.referrer_id)
            .maybeSingle()

          const oldBalance = currentBalance || {
            usdt_balance: '0',
            share_balance: '0',
            total_earned_usdt: '0',
            total_earned_shares: '0'
          }

          const newBalance = {
            user_id: referral.referrer_id,
            usdt_balance: parseFloat(oldBalance.usdt_balance || '0') + record.expected_usdt_commission,
            share_balance: parseFloat(oldBalance.share_balance || '0') + record.expected_share_commission,
            total_earned_usdt: parseFloat(oldBalance.total_earned_usdt || '0') + record.expected_usdt_commission,
            total_earned_shares: parseFloat(oldBalance.total_earned_shares || '0') + record.expected_share_commission,
            last_updated: new Date().toISOString()
          }

          await serviceClient
            .from('commission_balances')
            .upsert(newBalance, { onConflict: 'user_id' })

          correctionCount++
          auditEntries.push({
            admin_id: 4,
            action_type: 'BULK_COMMISSION_CORRECTION',
            target_user_id: record.user_id,
            old_values: { commission: 'missing' },
            new_values: {
              usdt_commission: record.expected_usdt_commission,
              share_commission: record.expected_share_commission,
              transaction_id: commissionTransaction.id
            },
            reason: `Bulk correction: Missing commission for $${record.payment_amount} purchase`,
            rollback_data: {
              commission_transaction_id: commissionTransaction.id,
              referrer_id: referral.referrer_id,
              usdt_correction: record.expected_usdt_commission,
              share_correction: record.expected_share_commission
            }
          })

          console.log(`✅ Created commission for ${record.username}: $${record.expected_usdt_commission} USDT + ${record.expected_share_commission} shares`)
        } catch (error) {
          console.error(`Error processing commission for user ${record.user_id}:`, error)
        }
      }

      // Log audit entries in batches
      if (auditEntries.length > 0) {
        const batchSize = 50
        for (let i = 0; i < auditEntries.length; i += batchSize) {
          const batch = auditEntries.slice(i, i + batchSize)
          await serviceClient.from('audit_log').insert(batch)
        }
      }

      setMessage({
        type: 'success',
        text: `Successfully corrected ${correctionCount} missing commissions out of ${missingCommissions.length} total`
      })

      await loadAuditData()
    } catch (error: any) {
      console.error('Bulk commission correction error:', error)
      setMessage({
        type: 'error',
        text: `Bulk correction failed: ${error.message}`
      })
    } finally {
      setLoading(false)
    }
  }

  const correctIncompleteCommissions = async () => {
    if (!confirm('This will update commission transactions that are missing share commissions. Continue?')) {
      return
    }

    setLoading(true)
    try {
      const serviceClient = getServiceRoleClient()
      let correctionCount = 0
      const auditEntries = []

      // Get all incomplete commissions (missing share commissions)
      const incompleteCommissions = auditRecords.filter(record =>
        record.audit_status === 'INCOMPLETE_COMMISSION' &&
        record.share_discrepancy > 0
      )

      console.log(`Processing ${incompleteCommissions.length} incomplete commission transactions...`)

      for (const record of incompleteCommissions) {
        try {
          // Get the existing commission transaction
          const { data: existingCommission, error: fetchError } = await serviceClient
            .from('commission_transactions')
            .select('*')
            .eq('share_purchase_id', record.share_purchase_id)
            .single()

          if (fetchError || !existingCommission) {
            console.warn(`No commission transaction found for share purchase ${record.share_purchase_id}`)
            continue
          }

          // Update commission transaction to add missing share commission
          const { error: updateError } = await serviceClient
            .from('commission_transactions')
            .update({
              share_commission: record.expected_share_commission
            })
            .eq('id', existingCommission.id)

          if (updateError) {
            console.error(`Failed to update commission for transaction ${existingCommission.id}:`, updateError)
            continue
          }

          // Update commission balance to add missing share commission
          const { data: currentBalance } = await serviceClient
            .from('commission_balances')
            .select('*')
            .eq('user_id', existingCommission.referrer_id)
            .single()

          if (currentBalance) {
            await serviceClient
              .from('commission_balances')
              .update({
                share_balance: parseFloat(currentBalance.share_balance || '0') + record.share_discrepancy,
                total_earned_shares: parseFloat(currentBalance.total_earned_shares || '0') + record.share_discrepancy,
                last_updated: new Date().toISOString()
              })
              .eq('user_id', existingCommission.referrer_id)
          }

          correctionCount++
          auditEntries.push({
            admin_id: 4,
            action_type: 'INCOMPLETE_COMMISSION_CORRECTION',
            target_user_id: record.user_id,
            old_values: {
              share_commission: record.actual_share_commission,
              transaction_id: existingCommission.id
            },
            new_values: {
              share_commission: record.expected_share_commission,
              transaction_id: existingCommission.id
            },
            reason: `Added missing ${record.share_discrepancy} share commission`,
            rollback_data: {
              commission_transaction_id: existingCommission.id,
              referrer_id: existingCommission.referrer_id,
              share_correction: record.share_discrepancy,
              original_share_commission: record.actual_share_commission
            }
          })

          console.log(`✅ Updated commission for ${record.username}: Added ${record.share_discrepancy} shares`)
        } catch (error) {
          console.error(`Error updating commission for user ${record.user_id}:`, error)
        }
      }

      // Log audit entries
      if (auditEntries.length > 0) {
        await serviceClient.from('audit_log').insert(auditEntries)
      }

      setMessage({
        type: 'success',
        text: `Successfully corrected ${correctionCount} incomplete commissions out of ${incompleteCommissions.length} total`
      })

      await loadAuditData()
    } catch (error: any) {
      console.error('Incomplete commission correction error:', error)
      setMessage({
        type: 'error',
        text: `Incomplete correction failed: ${error.message}`
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete': return 'text-green-600 bg-green-100'
      case 'incomplete': return 'text-yellow-600 bg-yellow-100'
      case 'missing': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Commission Audit Dashboard</h2>
        
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
            <input
              type="date"
              value={dateRange.from}
              onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
            <input
              type="date"
              value={dateRange.to}
              onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">User ID (Optional)</label>
            <input
              type="number"
              value={filterUserId}
              onChange={(e) => setFilterUserId(e.target.value)}
              placeholder="Filter by User ID"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status Filter</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Statuses</option>
              <option value="complete">Complete</option>
              <option value="incomplete">Incomplete</option>
              <option value="missing">Missing</option>
            </select>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={loadAuditData}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Refresh Audit Data'}
          </button>

          <button
            onClick={correctAllMissingCommissions}
            disabled={loading || auditRecords.filter(r => r.audit_status === 'MISSING_COMMISSION').length === 0}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            Fix All Missing Commissions ({auditRecords.filter(r => r.audit_status === 'MISSING_COMMISSION').length})
          </button>

          <button
            onClick={correctIncompleteCommissions}
            disabled={loading || auditRecords.filter(r => r.audit_status === 'INCOMPLETE_COMMISSION').length === 0}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50"
          >
            Fix Incomplete Commissions ({auditRecords.filter(r => r.audit_status === 'INCOMPLETE_COMMISSION').length})
          </button>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`mb-4 p-4 rounded-md ${
          message.type === 'success' ? 'bg-green-100 text-green-700' :
          message.type === 'error' ? 'bg-red-100 text-red-700' :
          'bg-blue-100 text-blue-700'
        }`}>
          {message.text}
        </div>
      )}

      {/* Audit Records Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">USDT Commission</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Share Commission</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sponsor</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {auditRecords.map((record, index) => (
              <tr key={`${record.user_id}-${record.share_purchase_id}`} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">ID: {record.user_id}</div>
                  <div className="text-sm text-gray-500">{record.username}</div>
                  <div className="text-xs text-gray-400">{record.email}</div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">${record.payment_amount.toFixed(2)}</div>
                  <div className="text-sm text-gray-500">{record.phase_name}</div>
                  <div className="text-xs text-gray-400">{new Date(record.payment_date).toLocaleDateString()}</div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    Expected: ${record.expected_usdt_commission.toFixed(2)}
                  </div>
                  <div className="text-sm text-gray-500">
                    Actual: ${record.actual_usdt_commission.toFixed(2)}
                  </div>
                  {record.expected_usdt_commission !== record.actual_usdt_commission && (
                    <div className="text-xs text-red-600">
                      Missing: ${(record.expected_usdt_commission - record.actual_usdt_commission).toFixed(2)}
                    </div>
                  )}
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    Expected: {record.expected_share_commission.toFixed(2)}
                  </div>
                  <div className="text-sm text-gray-500">
                    Actual: {record.actual_share_commission.toFixed(2)}
                  </div>
                  {record.expected_share_commission !== record.actual_share_commission && (
                    <div className="text-xs text-red-600">
                      Missing: {(record.expected_share_commission - record.actual_share_commission).toFixed(2)}
                    </div>
                  )}
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(record.status)}`}>
                    {record.status.toUpperCase()}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  {record.sponsor_username ? (
                    <div>
                      <div className="text-sm font-medium text-gray-900">ID: {record.sponsor_id}</div>
                      <div className="text-sm text-gray-500">{record.sponsor_username}</div>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-400">No Sponsor</span>
                  )}
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  {record.status !== 'complete' && record.sponsor_username && (
                    <button
                      onClick={() => handleCorrection(record)}
                      className="px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700"
                    >
                      Fix Commission
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {auditRecords.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          No audit records found for the selected criteria.
        </div>
      )}

      {/* Correction Form Modal */}
      {showCorrectionForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h3 className="text-lg font-bold text-gray-800 mb-4">Commission Correction</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">User ID</label>
                <input
                  type="number"
                  value={correctionData.user_id}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Payment Amount</label>
                <input
                  type="number"
                  value={correctionData.payment_amount}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">USDT Commission to Add</label>
                <input
                  type="number"
                  step="0.01"
                  value={correctionData.usdt_commission}
                  onChange={(e) => setCorrectionData(prev => ({ ...prev, usdt_commission: parseFloat(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Share Commission to Add</label>
                <input
                  type="number"
                  step="0.01"
                  value={correctionData.share_commission}
                  onChange={(e) => setCorrectionData(prev => ({ ...prev, share_commission: parseFloat(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Correction Reason</label>
                <textarea
                  value={correctionData.correction_reason}
                  onChange={(e) => setCorrectionData(prev => ({ ...prev, correction_reason: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Explain why this correction is needed..."
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCorrectionForm(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={submitCorrection}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Applying...' : 'Apply Correction'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CommissionAuditDashboard
