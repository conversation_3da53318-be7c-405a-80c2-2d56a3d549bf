import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../lib/supabase';

interface BasicProfileCompletionProps {
  user: any;
  onComplete: (userData: any) => void;
}

interface ProfileData {
  firstName: string;
  lastName: string;
  cellNumber: string;
  country: string;
}

const COUNTRIES = [
  { code: 'ZAF', name: 'South Africa', bankPayment: true },
  { code: 'NAM', name: 'Namibia', bankPayment: true },
  { code: 'SW<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>', bankPayment: true },
  { code: 'B<PERSON>', name: 'Botswana', bankPayment: false },
  { code: 'ZW<PERSON>', name: 'Zimbabwe', bankPayment: false },
  { code: 'MO<PERSON>', name: 'Mozambique', bankPayment: false },
  { code: 'L<PERSON>', name: 'Lesotho', bankPayment: false },
  { code: 'AGO', name: 'Angola', bankPayment: false },
  { code: '<PERSON><PERSON>', name: 'Zambia', bankPayment: false },
  { code: 'MW<PERSON>', name: 'Malawi', bankPayment: false },
  { code: 'TZ<PERSON>', name: 'Tanzania', bankPayment: false },
  { code: '<PERSON><PERSON>', name: 'Kenya', bankPayment: false },
  { code: 'UGA', name: 'Uganda', bankPayment: false },
  { code: 'RWA', name: 'Rwanda', bankPayment: false },
  { code: 'BDI', name: 'Burundi', bankPayment: false },
  { code: 'ETH', name: 'Ethiopia', bankPayment: false },
  { code: 'SOM', name: 'Somalia', bankPayment: false },
  { code: 'DJI', name: 'Djibouti', bankPayment: false },
  { code: 'ERI', name: 'Eritrea', bankPayment: false },
  { code: 'SSD', name: 'South Sudan', bankPayment: false },
  { code: 'SDN', name: 'Sudan', bankPayment: false },
  { code: 'EGY', name: 'Egypt', bankPayment: false },
  { code: 'LBY', name: 'Libya', bankPayment: false },
  { code: 'TUN', name: 'Tunisia', bankPayment: false },
  { code: 'DZA', name: 'Algeria', bankPayment: false },
  { code: 'MAR', name: 'Morocco', bankPayment: false },
  { code: 'NGA', name: 'Nigeria', bankPayment: false },
  { code: 'GHA', name: 'Ghana', bankPayment: false },
  { code: 'CIV', name: 'Côte d\'Ivoire', bankPayment: false },
  { code: 'SEN', name: 'Senegal', bankPayment: false },
  { code: 'MLI', name: 'Mali', bankPayment: false },
  { code: 'BFA', name: 'Burkina Faso', bankPayment: false },
  { code: 'NER', name: 'Niger', bankPayment: false },
  { code: 'TCD', name: 'Chad', bankPayment: false },
  { code: 'CMR', name: 'Cameroon', bankPayment: false },
  { code: 'CAF', name: 'Central African Republic', bankPayment: false },
  { code: 'COD', name: 'Democratic Republic of the Congo', bankPayment: false },
  { code: 'COG', name: 'Republic of the Congo', bankPayment: false },
  { code: 'GAB', name: 'Gabon', bankPayment: false },
  { code: 'GNQ', name: 'Equatorial Guinea', bankPayment: false },
  { code: 'STP', name: 'São Tomé and Príncipe', bankPayment: false },
  { code: 'USA', name: 'United States', bankPayment: false },
  { code: 'CAN', name: 'Canada', bankPayment: false },
  { code: 'GBR', name: 'United Kingdom', bankPayment: false },
  { code: 'DEU', name: 'Germany', bankPayment: false },
  { code: 'FRA', name: 'France', bankPayment: false },
  { code: 'ITA', name: 'Italy', bankPayment: false },
  { code: 'ESP', name: 'Spain', bankPayment: false },
  { code: 'NLD', name: 'Netherlands', bankPayment: false },
  { code: 'BEL', name: 'Belgium', bankPayment: false },
  { code: 'CHE', name: 'Switzerland', bankPayment: false },
  { code: 'AUT', name: 'Austria', bankPayment: false },
  { code: 'SWE', name: 'Sweden', bankPayment: false },
  { code: 'NOR', name: 'Norway', bankPayment: false },
  { code: 'DNK', name: 'Denmark', bankPayment: false },
  { code: 'FIN', name: 'Finland', bankPayment: false },
  { code: 'AUS', name: 'Australia', bankPayment: false },
  { code: 'NZL', name: 'New Zealand', bankPayment: false },
  { code: 'JPN', name: 'Japan', bankPayment: false },
  { code: 'KOR', name: 'South Korea', bankPayment: false },
  { code: 'CHN', name: 'China', bankPayment: false },
  { code: 'IND', name: 'India', bankPayment: false },
  { code: 'BRA', name: 'Brazil', bankPayment: false },
  { code: 'ARG', name: 'Argentina', bankPayment: false },
  { code: 'MEX', name: 'Mexico', bankPayment: false },
  { code: 'RUS', name: 'Russia', bankPayment: false },
  { code: 'TUR', name: 'Turkey', bankPayment: false },
  { code: 'SAU', name: 'Saudi Arabia', bankPayment: false },
  { code: 'ARE', name: 'United Arab Emirates', bankPayment: false },
  { code: 'ISR', name: 'Israel', bankPayment: false },
  { code: 'SGP', name: 'Singapore', bankPayment: false },
  { code: 'MYS', name: 'Malaysia', bankPayment: false },
  { code: 'THA', name: 'Thailand', bankPayment: false },
  { code: 'VNM', name: 'Vietnam', bankPayment: false },
  { code: 'PHL', name: 'Philippines', bankPayment: false },
  { code: 'IDN', name: 'Indonesia', bankPayment: false }
];

export const BasicProfileCompletion: React.FC<BasicProfileCompletionProps> = ({ user, onComplete }) => {
  const [profileData, setProfileData] = useState<ProfileData>({
    firstName: '',
    lastName: '',
    cellNumber: '',
    country: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Populate form with existing user data
  useEffect(() => {
    if (user) {
      console.log('🔍 Populating profile form with user data:', user);

      // Extract existing data from user object
      const existingFullName = user.database_user?.full_name || user.user_metadata?.full_name || '';
      const existingPhone = user.database_user?.phone || user.user_metadata?.phone || '';
      const existingCountry = user.database_user?.country_of_residence || user.user_metadata?.country_of_residence || '';

      // Split full name into first and last name
      const nameParts = existingFullName.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      setProfileData({
        firstName,
        lastName,
        cellNumber: existingPhone,
        country: existingCountry
      });

      console.log('✅ Profile form populated:', {
        firstName,
        lastName,
        cellNumber: existingPhone,
        country: existingCountry
      });
    }
  }, [user]);

  const selectedCountry = COUNTRIES.find(c => c.code === profileData.country);
  const hasBankPayment = selectedCountry?.bankPayment || false;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Updating user profile with basic information...');
      console.log('🔍 User object structure:', {
        userId: user.id,
        databaseUserId: user.database_user?.id,
        userType: typeof user.id,
        databaseUserType: typeof user.database_user?.id
      });

      // Use the correct user ID - either from database_user or direct user object
      const actualUserId = user.database_user?.id || user.id;
      console.log('🔧 Using user ID for update:', actualUserId);

      // Get service role client for admin operations
      const serviceClient = getServiceRoleClient();

      // Update user record with profile information using service client
      const { data: updatedUser, error: updateError } = await serviceClient
        .from('users')
        .update({
          full_name: `${profileData.firstName} ${profileData.lastName}`,
          phone: profileData.cellNumber,
          country_of_residence: profileData.country,
          country_selection_completed: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', actualUserId)
        .select()
        .single();

      if (updateError) {
        console.error('❌ Error updating user profile:', updateError);
        throw new Error(updateError.message || 'Failed to update profile');
      }

      console.log('✅ Profile updated successfully:', updatedUser);

      // Create updated user object with profile completion flags cleared
      const completedUser = {
        ...user,
        database_user: updatedUser,
        needsProfileCompletion: false,
        user_metadata: {
          ...user.user_metadata,
          profile_completion_required: false,
          full_name: updatedUser.full_name
        }
      };

      console.log('✅ Profile completion successful, payment options available:', {
        country: selectedCountry?.name,
        bankPayment: hasBankPayment,
        usdtPayment: true
      });

      // CRITICAL FIX: Update localStorage with completed profile data
      if (user.account_type === 'email') {
        localStorage.setItem('aureus_user', JSON.stringify(updatedUser));
        console.log('💾 Updated email user in localStorage with completed profile');
      } else if (user.account_type === 'telegram' || user.account_type === 'telegram_direct') {
        localStorage.setItem('aureus_telegram_user', JSON.stringify(completedUser));
        console.log('💾 Updated telegram user in localStorage with completed profile');
      }

      onComplete(completedUser);
    } catch (err: any) {
      console.error('❌ Profile completion error:', err);
      setError(err.message || 'Failed to complete profile');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center p-4">
      <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl font-bold text-slate-900">A</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">Complete Your Profile</h1>
          <p className="text-slate-400">Please provide your basic information to continue</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                First Name *
              </label>
              <input
                type="text"
                required
                value={profileData.firstName}
                onChange={(e) => setProfileData(prev => ({ ...prev, firstName: e.target.value }))}
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                placeholder="John"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Last Name *
              </label>
              <input
                type="text"
                required
                value={profileData.lastName}
                onChange={(e) => setProfileData(prev => ({ ...prev, lastName: e.target.value }))}
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                placeholder="Doe"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Cell Number *
            </label>
            <input
              type="tel"
              required
              value={profileData.cellNumber}
              onChange={(e) => setProfileData(prev => ({ ...prev, cellNumber: e.target.value }))}
              className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="+27 12 345 6789"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Country *
            </label>
            <select
              required
              value={profileData.country}
              onChange={(e) => setProfileData(prev => ({ ...prev, country: e.target.value }))}
              className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            >
              <option value="">Select your country</option>
              {COUNTRIES.map(country => (
                <option key={country.code} value={country.code}>
                  {country.name}
                </option>
              ))}
            </select>
          </div>

          {profileData.country && (
            <div className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-white mb-2">Payment Options Available:</h3>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-slate-300">USDT (Cryptocurrency)</span>
                </div>
                {hasBankPayment && (
                  <div className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-slate-300">Bank Transfer</span>
                  </div>
                )}
                {!hasBankPayment && (
                  <div className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-slate-500 rounded-full mr-2"></div>
                    <span className="text-slate-500">Bank Transfer (Not available in your country)</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-slate-900 font-semibold py-3 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Completing Profile...' : 'Complete Profile'}
          </button>
        </form>
      </div>
    </div>
  );
};
