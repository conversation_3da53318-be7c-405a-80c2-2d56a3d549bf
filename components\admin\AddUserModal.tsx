import React, { useState, useEffect } from 'react'
import { supabase, getServiceRoleClient, validateEmail } from '../../lib/supabase'
import { hashPassword, validatePasswordStrength } from '../../lib/passwordSecurity'
import { emailVerificationService } from '../../lib/emailVerificationService'
import { EmailVerificationModal } from '../EmailVerificationModal'
import { logAdminAction } from '../../lib/adminAuth'

interface AddUserModalProps {
  isOpen: boolean
  onClose: () => void
  onUserAdded: () => void
  adminUser?: any
}

interface FormData {
  email: string
  password: string
  confirmPassword: string
  fullName: string
  username: string
  phone: string
  countryOfResidence: string
  sponsorUsername: string
  isAdmin: boolean
  sendWelcomeEmail: boolean
}

interface FormErrors {
  [key: string]: string
}

export const AddUserModal: React.FC<AddUserModalProps> = ({ isOpen, onClose, onUserAdded, adminUser }) => {
  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    username: '',
    phone: '',
    countryOfResidence: 'ZA',
    sponsorUsername: '',
    isAdmin: false,
    sendWelcomeEmail: true
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  const [generalError, setGeneralError] = useState('')
  const [showEmailVerification, setShowEmailVerification] = useState(false)
  const [createdUserId, setCreatedUserId] = useState<number | null>(null)

  // Password visibility states (default to visible)
  const [showPassword, setShowPassword] = useState(true)
  const [showConfirmPassword, setShowConfirmPassword] = useState(true)
  const [emailVerified, setEmailVerified] = useState(false)
  const [sponsorValidation, setSponsorValidation] = useState<{
    isValidating: boolean
    isValid: boolean | null
    sponsorName: string | null
  }>({
    isValidating: false,
    isValid: null,
    sponsorName: null
  })

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        email: '',
        password: '',
        confirmPassword: '',
        fullName: '',
        username: '',
        phone: '',
        countryOfResidence: 'ZA',
        sponsorUsername: '',
        isAdmin: false,
        sendWelcomeEmail: true
      })
      setErrors({})
      setGeneralError('')
      setEmailVerified(false)
      setCreatedUserId(null)
      setSponsorValidation({
        isValidating: false,
        isValid: null,
        sponsorName: null
      })
    }
  }, [isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }

    // Auto-generate username from full name if username is empty
    if (name === 'fullName' && !formData.username) {
      const generatedUsername = value
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 20)

      if (generatedUsername) {
        setFormData(prev => ({ ...prev, username: generatedUsername }))
      }
    }

    // Validate sponsor username in real-time
    if (name === 'sponsorUsername') {
      const sponsorUsername = value.trim()

      if (sponsorUsername) {
        setSponsorValidation({ isValidating: true, isValid: null, sponsorName: null })

        // Debounce the validation
        setTimeout(async () => {
          try {
            const serviceRoleClient = getServiceRoleClient()
            const { data: sponsor, error } = await serviceRoleClient
              .from('users')
              .select('id, username, full_name')
              .eq('username', sponsorUsername.toLowerCase())
              .single()

            if (error || !sponsor) {
              setSponsorValidation({
                isValidating: false,
                isValid: false,
                sponsorName: null
              })
            } else {
              setSponsorValidation({
                isValidating: false,
                isValid: true,
                sponsorName: sponsor.full_name || sponsor.username
              })
            }
          } catch (error) {
            setSponsorValidation({
              isValidating: false,
              isValid: false,
              sponsorName: null
            })
          }
        }, 800) // 800ms debounce
      } else {
        setSponsorValidation({
          isValidating: false,
          isValid: null,
          sponsorName: null
        })
      }
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Email validation
    const emailValidation = validateEmail(formData.email)
    if (!emailValidation.valid) {
      newErrors.email = emailValidation.error || 'Invalid email'
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else {
      const passwordValidation = validatePasswordStrength(formData.password)
      if (!passwordValidation.isValid) {
        newErrors.password = passwordValidation.errors[0] || 'Password is too weak'
      }
    }

    // Confirm password
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    // Full name
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required'
    }

    // Username
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required'
    } else if (formData.username.length < 2) {
      newErrors.username = 'Username must be at least 2 characters'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'Username can only contain letters, numbers, and underscores'
    }

    // Phone
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    }

    // Country
    if (!formData.countryOfResidence) {
      newErrors.countryOfResidence = 'Country is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const checkForDuplicates = async (): Promise<boolean> => {
    try {
      const serviceRoleClient = getServiceRoleClient()
      
      const { data: existingUsers, error } = await serviceRoleClient
        .from('users')
        .select('email, username')
        .or(`email.eq.${formData.email.toLowerCase()},username.eq.${formData.username.toLowerCase()}`)

      if (error) {
        console.error('Error checking for duplicates:', error)
        setGeneralError('Failed to validate user information. Please try again.')
        return false
      }

      const duplicateErrors: FormErrors = {}
      
      if (existingUsers && existingUsers.length > 0) {
        existingUsers.forEach(user => {
          if (user.email.toLowerCase() === formData.email.toLowerCase()) {
            duplicateErrors.email = 'Email already exists'
          }
          if (user.username.toLowerCase() === formData.username.toLowerCase()) {
            duplicateErrors.username = 'Username already exists'
          }
        })
      }

      if (Object.keys(duplicateErrors).length > 0) {
        setErrors(prev => ({ ...prev, ...duplicateErrors }))
        return false
      }

      return true
    } catch (error) {
      console.error('Error checking duplicates:', error)
      setGeneralError('Failed to validate user information. Please try again.')
      return false
    }
  }

  const createUser = async (): Promise<number | null> => {
    try {
      const serviceRoleClient = getServiceRoleClient()

      // Hash the password
      const hashedPassword = await hashPassword(formData.password)

      // Create user record
      const { data: userData, error: userError } = await serviceRoleClient
        .from('users')
        .insert({
          email: formData.email.toLowerCase().trim(),
          password_hash: hashedPassword,
          full_name: formData.fullName.trim(),
          username: formData.username.toLowerCase().trim(),
          phone: formData.phone.trim(),
          country_of_residence: formData.countryOfResidence,
          is_admin: formData.isAdmin,
          is_active: true
        })
        .select('id')
        .single()

      if (userError) {
        console.error('Error creating user:', userError)
        setGeneralError(`Failed to create user: ${userError.message}`)
        return null
      }

      console.log('✅ User created successfully:', userData)

      // Create referral relationship if sponsor is provided
      if (formData.sponsorUsername.trim()) {
        try {
          // Find sponsor by username
          const { data: sponsor, error: sponsorError } = await serviceRoleClient
            .from('users')
            .select('id, username')
            .eq('username', formData.sponsorUsername.toLowerCase().trim())
            .single()

          if (sponsorError || !sponsor) {
            console.warn('⚠️ Sponsor not found:', formData.sponsorUsername)
            setGeneralError(`Warning: Sponsor "${formData.sponsorUsername}" not found. User created without referral relationship.`)
          } else {
            // Create referral relationship
            const referralCode = `${sponsor.username}_${userData.id}_${Date.now()}`

            const { error: referralError } = await serviceRoleClient
              .from('referrals')
              .insert({
                referrer_id: sponsor.id,
                referred_id: userData.id,
                referral_code: referralCode,
                campaign_source: 'admin_created',
                commission_rate: 0.10, // Default 10% commission rate
                status: 'active',
                created_at: new Date().toISOString()
              })

            if (referralError) {
              console.warn('⚠️ Failed to create referral relationship:', referralError)
              setGeneralError(`Warning: Failed to create referral relationship with "${formData.sponsorUsername}". User created successfully.`)
            } else {
              console.log('✅ Referral relationship created successfully')
            }
          }
        } catch (referralError) {
          console.warn('⚠️ Error processing referral relationship:', referralError)
          setGeneralError(`Warning: Error processing referral relationship. User created successfully.`)
        }
      }

      // Create initial commission balance record
      try {
        const { error: balanceError } = await serviceRoleClient
          .from('commission_balances')
          .insert({
            user_id: userData.id,
            usdt_balance: 0,
            share_balance: 0,
            total_earned_usdt: 0,
            total_earned_shares: 0,
            total_withdrawn_usdt: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (balanceError) {
          console.warn('⚠️ Failed to create initial commission balance:', balanceError)
        } else {
          console.log('✅ Initial commission balance created')
        }
      } catch (balanceError) {
        console.warn('⚠️ Error creating initial commission balance:', balanceError)
      }

      // Log admin action
      try {
        await logAdminAction(
          adminUser?.email || 'unknown',
          'CREATE_USER',
          'user',
          userData.id.toString(),
          {
            username: formData.username,
            email: formData.email,
            fullName: formData.fullName,
            isAdmin: formData.isAdmin,
            sponsorUsername: formData.sponsorUsername || null,
            emailVerificationRequested: formData.sendWelcomeEmail
          }
        )
      } catch (auditError) {
        console.warn('Failed to log admin action (user still created):', auditError)
      }

      return userData.id
    } catch (error) {
      console.error('Error creating user:', error)
      setGeneralError('Failed to create user. Please try again.')
      return null
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setGeneralError('')

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      // Check for duplicates
      const noDuplicates = await checkForDuplicates()
      if (!noDuplicates) {
        setLoading(false)
        return
      }

      // Create the user
      const userId = await createUser()
      if (!userId) {
        setLoading(false)
        return
      }

      setCreatedUserId(userId)

      // Send email verification if requested
      if (formData.sendWelcomeEmail) {
        setShowEmailVerification(true)
      } else {
        // Mark as complete without email verification
        await markUserAsVerified(userId)
        onUserAdded()
        onClose()
      }
    } catch (error) {
      console.error('Error in form submission:', error)
      setGeneralError('Failed to create user. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const markUserAsVerified = async (userId: number) => {
    try {
      const serviceRoleClient = getServiceRoleClient()
      await serviceRoleClient
        .from('users')
        .update({ email_verified: true })
        .eq('id', userId)
    } catch (error) {
      console.error('Error marking user as verified:', error)
    }
  }

  const handleEmailVerificationSuccess = async () => {
    if (createdUserId) {
      await markUserAsVerified(createdUserId)
      setEmailVerified(true)
      setShowEmailVerification(false)
      onUserAdded()
      onClose()
    }
  }

  const handleEmailVerificationSkip = () => {
    setShowEmailVerification(false)
    onUserAdded()
    onClose()
  }

  if (!isOpen) return null

  return (
    <>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900 rounded-xl border border-gray-700 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-white">➕ Add New User</h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>

            {generalError && (
              <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 text-sm">
                {generalError}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                    errors.email ? 'border-red-500' : 'border-gray-600'
                  }`}
                  placeholder="<EMAIL>"
                  required
                />
                {errors.email && <p className="text-red-400 text-xs mt-1">{errors.email}</p>}
              </div>

              {/* Password */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Password *
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 pr-10 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                        errors.password ? 'border-red-500' : 'border-gray-600'
                      }`}
                      placeholder="Strong password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {showPassword ? (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                      ) : (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                          <circle cx="12" cy="12" r="3" />
                        </svg>
                      )}
                    </button>
                  </div>
                  {errors.password && <p className="text-red-400 text-xs mt-1">{errors.password}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Confirm Password *
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 pr-10 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                        errors.confirmPassword ? 'border-red-500' : 'border-gray-600'
                      }`}
                      placeholder="Confirm password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      {showConfirmPassword ? (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                      ) : (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                          <circle cx="12" cy="12" r="3" />
                        </svg>
                      )}
                    </button>
                  </div>
                  {errors.confirmPassword && <p className="text-red-400 text-xs mt-1">{errors.confirmPassword}</p>}
                </div>
              </div>

              {/* Full Name and Username */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                      errors.fullName ? 'border-red-500' : 'border-gray-600'
                    }`}
                    placeholder="John Doe"
                    required
                  />
                  {errors.fullName && <p className="text-red-400 text-xs mt-1">{errors.fullName}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Username *
                  </label>
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                      errors.username ? 'border-red-500' : 'border-gray-600'
                    }`}
                    placeholder="johndoe"
                    required
                  />
                  {errors.username && <p className="text-red-400 text-xs mt-1">{errors.username}</p>}
                </div>
              </div>

              {/* Phone and Country */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                      errors.phone ? 'border-red-500' : 'border-gray-600'
                    }`}
                    placeholder="+27 12 345 6789"
                    required
                  />
                  {errors.phone && <p className="text-red-400 text-xs mt-1">{errors.phone}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Country *
                  </label>
                  <select
                    name="countryOfResidence"
                    value={formData.countryOfResidence}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                      errors.countryOfResidence ? 'border-red-500' : 'border-gray-600'
                    }`}
                    required
                  >
                    <option value="ZA">South Africa</option>
                    <option value="US">United States</option>
                    <option value="GB">United Kingdom</option>
                    <option value="CA">Canada</option>
                    <option value="AU">Australia</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                    <option value="IT">Italy</option>
                    <option value="ES">Spain</option>
                    <option value="NL">Netherlands</option>
                  </select>
                  {errors.countryOfResidence && <p className="text-red-400 text-xs mt-1">{errors.countryOfResidence}</p>}
                </div>
              </div>

              {/* Sponsor Username */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Sponsor Username (Optional)
                </label>
                <div className="relative">
                  <input
                    type="text"
                    name="sponsorUsername"
                    value={formData.sponsorUsername}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 ${
                      sponsorValidation.isValid === false ? 'border-red-500' :
                      sponsorValidation.isValid === true ? 'border-green-500' : 'border-gray-600'
                    }`}
                    placeholder="sponsor_username"
                  />
                  {sponsorValidation.isValidating && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-400"></div>
                    </div>
                  )}
                  {sponsorValidation.isValid === true && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-400">
                      ✓
                    </div>
                  )}
                  {sponsorValidation.isValid === false && formData.sponsorUsername.trim() && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-400">
                      ✗
                    </div>
                  )}
                </div>
                {sponsorValidation.isValid === true && sponsorValidation.sponsorName && (
                  <p className="text-green-400 text-xs mt-1">
                    ✓ Sponsor found: {sponsorValidation.sponsorName}
                  </p>
                )}
                {sponsorValidation.isValid === false && formData.sponsorUsername.trim() && (
                  <p className="text-red-400 text-xs mt-1">
                    ✗ Sponsor not found
                  </p>
                )}
              </div>

              {/* Checkboxes */}
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="isAdmin"
                    checked={formData.isAdmin}
                    onChange={handleInputChange}
                    className="mr-2 rounded border-gray-600 bg-gray-800 text-yellow-500 focus:ring-yellow-500"
                  />
                  <span className="text-sm text-gray-300">Grant admin privileges</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="sendWelcomeEmail"
                    checked={formData.sendWelcomeEmail}
                    onChange={handleInputChange}
                    className="mr-2 rounded border-gray-600 bg-gray-800 text-yellow-500 focus:ring-yellow-500"
                  />
                  <span className="text-sm text-gray-300">Send welcome email with verification</span>
                </label>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 bg-yellow-500 text-black font-medium rounded-lg hover:bg-yellow-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Creating...' : 'Create User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Email Verification Modal */}
      {showEmailVerification && createdUserId && (
        <EmailVerificationModal
          isOpen={showEmailVerification}
          onClose={handleEmailVerificationSkip}
          userId={createdUserId}
          email={formData.email}
          purpose="registration"
          onVerificationSuccess={handleEmailVerificationSuccess}
          title="Verify New User Email"
          description="A verification code has been sent to the user's email address."
        />
      )}
    </>
  )
}
