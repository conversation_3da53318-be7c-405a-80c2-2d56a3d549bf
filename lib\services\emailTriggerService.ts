import { getServiceRoleClient } from '../supabase';
import { EmailNotificationService, MessageNotificationData, CommissionNotificationData, ReferralNotificationData, ShareTransferNotificationData, SharePurchaseNotificationData } from './emailNotificationService';

export class EmailTriggerService {
  private static instance: EmailTriggerService;
  private emailService: EmailNotificationService;

  private constructor() {
    this.emailService = EmailNotificationService.getInstance();
  }

  public static getInstance(): EmailTriggerService {
    if (!EmailTriggerService.instance) {
      EmailTriggerService.instance = new EmailTriggerService();
    }
    return EmailTriggerService.instance;
  }

  // Trigger for new internal messages
  public async handleNewMessage(messageId: string): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get message details with sender and recipient info
      const { data: message, error } = await serviceClient
        .from('internal_messages')
        .select(`
          *,
          sender:sender_user_id(first_name, last_name, username),
          recipient:recipient_user_id(first_name, last_name, username, email)
        `)
        .eq('id', messageId)
        .single();

      if (error || !message) {
        console.error('Error fetching message for notification:', error);
        return;
      }

      // Check rate limit
      const canSend = await this.emailService.checkRateLimit(message.recipient_user_id);
      if (!canSend) {
        console.log('Rate limit exceeded for user:', message.recipient_user_id);
        return;
      }

      // Get recipient email from users table if not in message
      let recipientEmail = message.recipient?.email;
      if (!recipientEmail) {
        const { data: user } = await serviceClient
          .from('users')
          .select('email')
          .eq('id', message.recipient_user_id)
          .single();
        recipientEmail = user?.email;
      }

      if (!recipientEmail) {
        console.error('No email found for recipient:', message.recipient_user_id);
        return;
      }

      const notificationData: MessageNotificationData = {
        messageId: message.id,
        senderName: `${message.sender?.first_name} ${message.sender?.last_name}`,
        senderUsername: message.sender?.username || 'Unknown',
        subject: message.subject,
        messagePreview: message.message_content.substring(0, 150),
        recipientEmail,
        recipientId: message.recipient_user_id
      };

      await this.emailService.sendMessageNotification(notificationData);
    } catch (error) {
      console.error('Error handling new message notification:', error);
    }
  }

  // Trigger for new commission transactions
  public async handleNewCommission(transactionId: string): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get commission transaction details
      const { data: transaction, error } = await serviceClient
        .from('commission_transactions')
        .select(`
          *,
          referrer:referrer_id(first_name, last_name, username, email),
          source_user:source_user_id(first_name, last_name, username)
        `)
        .eq('id', transactionId)
        .eq('status', 'approved')
        .single();

      if (error || !transaction) {
        console.error('Error fetching commission transaction for notification:', error);
        return;
      }

      // Check rate limit
      const canSend = await this.emailService.checkRateLimit(transaction.referrer_id);
      if (!canSend) {
        console.log('Rate limit exceeded for user:', transaction.referrer_id);
        return;
      }

      // Get recipient email
      let recipientEmail = transaction.referrer?.email;
      if (!recipientEmail) {
        const { data: user } = await serviceClient
          .from('users')
          .select('email')
          .eq('id', transaction.referrer_id)
          .single();
        recipientEmail = user?.email;
      }

      if (!recipientEmail) {
        console.error('No email found for referrer:', transaction.referrer_id);
        return;
      }

      // Get updated balance from commission_balances
      const { data: balance } = await serviceClient
        .from('commission_balances')
        .select('usdt_balance, share_balance')
        .eq('user_id', transaction.referrer_id)
        .single();

      // Determine commission type and amounts
      const isUSDT = transaction.usdt_commission > 0;
      const commissionAmount = isUSDT ? transaction.usdt_commission : transaction.share_commission;
      const newBalance = isUSDT ? (balance?.usdt_balance || 0) : (balance?.share_balance || 0);

      const notificationData: CommissionNotificationData = {
        commissionType: isUSDT ? 'USDT' : 'shares',
        amount: commissionAmount,
        sourceUserName: `${transaction.source_user?.first_name} ${transaction.source_user?.last_name}`,
        sourceUsername: transaction.source_user?.username || 'Unknown',
        newBalance,
        recipientEmail,
        recipientId: transaction.referrer_id
      };

      await this.emailService.sendCommissionNotification(notificationData);
    } catch (error) {
      console.error('Error handling new commission notification:', error);
    }
  }

  // Trigger for new referrals
  public async handleNewReferral(referralId: string): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get referral details
      const { data: referral, error } = await serviceClient
        .from('referrals')
        .select(`
          *,
          referrer:referrer_id(first_name, last_name, username, email),
          referred_user:referred_user_id(first_name, last_name, username)
        `)
        .eq('id', referralId)
        .eq('status', 'active')
        .single();

      if (error || !referral) {
        console.error('Error fetching referral for notification:', error);
        return;
      }

      // Check rate limit
      const canSend = await this.emailService.checkRateLimit(referral.referrer_id);
      if (!canSend) {
        console.log('Rate limit exceeded for user:', referral.referrer_id);
        return;
      }

      // Get referrer email
      let referrerEmail = referral.referrer?.email;
      if (!referrerEmail) {
        const { data: user } = await serviceClient
          .from('users')
          .select('email')
          .eq('id', referral.referrer_id)
          .single();
        referrerEmail = user?.email;
      }

      if (!referrerEmail) {
        console.error('No email found for referrer:', referral.referrer_id);
        return;
      }

      const notificationData: ReferralNotificationData = {
        newReferralName: `${referral.referred_user?.first_name} ${referral.referred_user?.last_name}`,
        newReferralUsername: referral.referred_user?.username || 'Unknown',
        registrationDate: new Date(referral.created_at).toLocaleDateString(),
        newReferralId: referral.referred_user_id,
        referrerEmail,
        referrerId: referral.referrer_id
      };

      await this.emailService.sendReferralNotification(notificationData);
    } catch (error) {
      console.error('Error handling new referral notification:', error);
    }
  }

  // Trigger for share transfers
  public async handleShareTransfer(transferId: string): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get share transfer details
      const { data: transfer, error } = await serviceClient
        .from('share_transfers')
        .select(`
          *,
          sender:sender_user_id(first_name, last_name, username, email),
          recipient:recipient_user_id(first_name, last_name, username, email)
        `)
        .eq('id', transferId)
        .single();

      if (error || !transfer) {
        console.error('Error fetching share transfer for notification:', error);
        return;
      }

      // Send notification to sender
      if (transfer.sender?.email) {
        const canSendToSender = await this.emailService.checkRateLimit(transfer.sender_user_id);
        if (canSendToSender) {
          // Get sender's updated balance
          const { data: senderBalance } = await serviceClient
            .from('user_share_holdings')
            .select('total_shares')
            .eq('user_id', transfer.sender_user_id)
            .single();

          const senderNotificationData: ShareTransferNotificationData = {
            shareAmount: transfer.share_amount,
            otherPartyName: `${transfer.recipient?.first_name} ${transfer.recipient?.last_name}`,
            otherPartyUsername: transfer.recipient?.username || 'Unknown',
            transactionId: transfer.id,
            newBalance: senderBalance?.total_shares || 0,
            isRecipient: false,
            userEmail: transfer.sender.email,
            userId: transfer.sender_user_id
          };

          await this.emailService.sendShareTransferNotification(senderNotificationData);
        }
      }

      // Send notification to recipient
      if (transfer.recipient?.email) {
        const canSendToRecipient = await this.emailService.checkRateLimit(transfer.recipient_user_id);
        if (canSendToRecipient) {
          // Get recipient's updated balance
          const { data: recipientBalance } = await serviceClient
            .from('user_share_holdings')
            .select('total_shares')
            .eq('user_id', transfer.recipient_user_id)
            .single();

          const recipientNotificationData: ShareTransferNotificationData = {
            shareAmount: transfer.share_amount,
            otherPartyName: `${transfer.sender?.first_name} ${transfer.sender?.last_name}`,
            otherPartyUsername: transfer.sender?.username || 'Unknown',
            transactionId: transfer.id,
            newBalance: recipientBalance?.total_shares || 0,
            isRecipient: true,
            userEmail: transfer.recipient.email,
            userId: transfer.recipient_user_id
          };

          await this.emailService.sendShareTransferNotification(recipientNotificationData);
        }
      }
    } catch (error) {
      console.error('Error handling share transfer notification:', error);
    }
  }

  // Trigger for share purchases
  public async handleSharePurchase(purchaseId: string): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      
      // Get share purchase details
      const { data: purchase, error } = await serviceClient
        .from('aureus_share_purchases')
        .select(`
          *,
          user:user_id(first_name, last_name, username, email)
        `)
        .eq('id', purchaseId)
        .single();

      if (error || !purchase) {
        console.error('Error fetching share purchase for notification:', error);
        return;
      }

      // Check rate limit
      const canSend = await this.emailService.checkRateLimit(purchase.user_id);
      if (!canSend) {
        console.log('Rate limit exceeded for user:', purchase.user_id);
        return;
      }

      // Get user email
      let userEmail = purchase.user?.email;
      if (!userEmail) {
        const { data: user } = await serviceClient
          .from('users')
          .select('email')
          .eq('id', purchase.user_id)
          .single();
        userEmail = user?.email;
      }

      if (!userEmail) {
        console.error('No email found for user:', purchase.user_id);
        return;
      }

      // Check certificate status
      const { data: certificate } = await serviceClient
        .from('share_certificates')
        .select('status')
        .eq('user_id', purchase.user_id)
        .eq('purchase_id', purchase.id)
        .single();

      const certificateStatus = certificate?.status || 'pending';

      const notificationData: SharePurchaseNotificationData = {
        packageName: purchase.package_name || 'Share Package',
        shareQuantity: purchase.shares_purchased,
        totalAmount: purchase.total_amount,
        paymentMethod: purchase.payment_method || 'Unknown',
        referenceId: purchase.id,
        certificateStatus,
        userEmail,
        userId: purchase.user_id
      };

      await this.emailService.sendSharePurchaseConfirmation(notificationData);
    } catch (error) {
      console.error('Error handling share purchase notification:', error);
    }
  }
}
