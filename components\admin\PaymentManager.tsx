import React, { useState, useEffect } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'
import { notificationService } from '../../lib/notificationService'

interface Payment {
  id: string
  user_id: number
  amount: number
  shares_to_purchase: number
  network: string
  currency: string
  sender_wallet: string
  receiver_wallet: string
  transaction_hash: string
  screenshot_url: string
  status: 'pending' | 'approved' | 'rejected' | 'reversed'
  created_at: string
  approved_at?: string
  rejected_at?: string
  reversed_at?: string
  approved_by_admin_id?: number
  rejected_by_admin_id?: number
  reversed_by_admin_id?: number
  rejection_reason?: string
  reversal_reason?: string
  admin_notes?: string
  verification_status?: string
  users?: {
    id: number
    username: string
    email: string
    full_name?: string
  }
}

interface PaymentManagerProps {
  currentUser: any
}

export const PaymentManager: React.FC<PaymentManagerProps> = ({ currentUser }) => {
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)
  const [activeTab, setActiveTab] = useState<'pending' | 'approved' | 'rejected'>('pending')
  const [rejectionReason, setRejectionReason] = useState('')
  const [showRejectionModal, setShowRejectionModal] = useState(false)
  const [processingPayment, setProcessingPayment] = useState<string | null>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info', text: string } | null>(null)
  const [showScreenshotModal, setShowScreenshotModal] = useState(false)
  const [currentScreenshot, setCurrentScreenshot] = useState<string | null>(null)

  // Payment Reversal State
  const [showReversalModal, setShowReversalModal] = useState(false)
  const [reversalReason, setReversalReason] = useState('')
  const [reversalConfirmation, setReversalConfirmation] = useState(false)
  const [reversalConfirmText, setReversalConfirmText] = useState('')
  const [processingReversal, setProcessingReversal] = useState(false)

  useEffect(() => {
    loadPayments()
  }, [activeTab])

  const loadPayments = async () => {
    setLoading(true)
    setMessage(null)

    try {
      // Use service role client for admin operations
      const serviceClient = getServiceRoleClient()

      const { data, error } = await serviceClient
        .from('crypto_payment_transactions')
        .select(`
          *,
          users!inner(
            id,
            username,
            email,
            full_name,
            phone_number,
            telegram_users(
              username,
              first_name,
              last_name
            )
          )
        `)
        .eq('status', activeTab)
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) {
        console.error('Supabase error:', error)
        throw new Error(`Database error: ${error.message}`)
      }

      // Process payments and add calculated shares if missing
      const processedPayments = (data || []).map(payment => {
        // Ensure all required fields have fallback values
        return {
          ...payment,
          amount: payment.amount || 0,
          shares_to_purchase: payment.shares_to_purchase || Math.floor((payment.amount || 0) / 5.0),
          network: payment.network || 'Unknown',
          currency: payment.currency || 'USDT',
          transaction_hash: payment.transaction_hash || '',
          users: payment.users || { username: 'Unknown', email: 'No email', full_name: 'Unknown User' }
        }
      })

      setPayments(processedPayments)

      if (processedPayments.length === 0) {
        setMessage({ type: 'info', text: `No ${activeTab} payments found` })
      }

    } catch (error: any) {
      console.error('Error loading payments:', error)
      setMessage({
        type: 'error',
        text: error.message || 'Failed to load payments. Please check your connection and try again.'
      })
      setPayments([]) // Clear payments on error
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Less than 1 hour ago'
    if (diffInHours < 24) return `${diffInHours} hours ago`
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} days ago`
  }

  const getNetworkIcon = (network: string) => {
    switch (network.toLowerCase()) {
      case 'bsc': return '🟡'
      case 'polygon': return '🟣'
      case 'tron': return '🔴'
      case 'bank_transfer': return '🏦'
      default: return '💎'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-500/20'
      case 'approved': return 'text-green-400 bg-green-500/20'
      case 'rejected': return 'text-red-400 bg-red-500/20'
      case 'reversed': return 'text-orange-400 bg-orange-500/20'
      default: return 'text-gray-400 bg-gray-500/20'
    }
  }

  const getScreenshotUrl = (screenshotPath: string) => {
    if (!screenshotPath) return null

    console.log('Processing screenshot path:', screenshotPath)

    // If it's already a full URL, return as is
    if (screenshotPath.startsWith('http')) {
      return screenshotPath
    }

    // Handle different path formats
    let cleanPath = screenshotPath

    // Remove leading slash if present
    if (cleanPath.startsWith('/')) {
      cleanPath = cleanPath.substring(1)
    }

    // Remove 'proof/' prefix if present (in case it's duplicated)
    if (cleanPath.startsWith('proof/')) {
      cleanPath = cleanPath.substring(6)
    }

    console.log('Clean path for storage:', cleanPath)

    // Convert Supabase storage path to public URL
    const { data } = supabase.storage
      .from('proof')
      .getPublicUrl(cleanPath)

    console.log('Generated public URL:', data.publicUrl)
    return data.publicUrl
  }

  const handleViewScreenshot = async (payment: Payment) => {
    if (!payment.screenshot_url) {
      setMessage({ type: 'error', text: 'No screenshot available for this payment' })
      return
    }

    console.log('Attempting to view screenshot for payment:', payment.id)
    console.log('Screenshot URL from database:', payment.screenshot_url)

    try {
      // First try to get the public URL
      let screenshotUrl = getScreenshotUrl(payment.screenshot_url)

      if (!screenshotUrl) {
        setMessage({ type: 'error', text: 'Unable to generate screenshot URL' })
        return
      }

      console.log('Testing URL accessibility:', screenshotUrl)

      // Test if the URL is accessible
      try {
        const response = await fetch(screenshotUrl, { method: 'HEAD' })
        if (response.ok) {
          console.log('Public URL is accessible')
          setCurrentScreenshot(screenshotUrl)
          setShowScreenshotModal(true)
          return
        }
      } catch (fetchError) {
        console.log('Public URL fetch failed:', fetchError)
      }

      // If public URL fails, try downloading from storage
      console.log('Public URL failed, attempting storage download...')

      // Try different path variations
      const pathVariations = [
        payment.screenshot_url,
        payment.screenshot_url.replace(/^\/+/, ''), // Remove leading slashes
        payment.screenshot_url.replace(/^proof\//, ''), // Remove proof/ prefix
        `payment_${payment.user_id}_${payment.screenshot_url}` // Try with user prefix
      ]

      let fileData = null
      let downloadError = null

      // Use service role client for admin operations
      const serviceClient = getServiceRoleClient()

      for (const pathVariation of pathVariations) {
        console.log('Trying storage path:', pathVariation)
        const result = await serviceClient.storage
          .from('proof')
          .download(pathVariation)

        if (!result.error) {
          fileData = result.data
          console.log('Successfully downloaded from path:', pathVariation)
          break
        } else {
          console.log('Failed to download from path:', pathVariation, result.error)
          downloadError = result.error
        }
      }

      if (!fileData) {
        throw new Error(`Storage download failed: ${downloadError?.message || 'File not found in any path variation'}`)
      }

      // Create blob URL for viewing
      const blob = new Blob([fileData], { type: 'image/jpeg' })
      screenshotUrl = URL.createObjectURL(blob)

      console.log('Created blob URL for viewing')
      setCurrentScreenshot(screenshotUrl)
      setShowScreenshotModal(true)

    } catch (error: any) {
      console.error('Error loading screenshot:', error)
      setMessage({
        type: 'error',
        text: `Failed to load screenshot: ${error.message}. Please check the browser console for more details.`
      })
    }
  }

  const approvePayment = async (payment: Payment) => {
    if (!currentUser?.database_user?.id) {
      setMessage({ type: 'error', text: 'Admin user not found' })
      return
    }

    setProcessingPayment(payment.id)
    setMessage(null)

    try {
      // Use service role client for admin operations
      const serviceClient = getServiceRoleClient()

      // Get current phase for share calculation
      const { data: currentPhase, error: phaseError } = await serviceClient
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single()

      if (phaseError || !currentPhase) {
        throw new Error('No active investment phase found')
      }

      // Calculate shares based on current phase price
      const phasePrice = parseFloat(currentPhase.price_per_share) || 5.0 // Fallback to $5 if price is invalid
      const sharesAmount = Math.floor(payment.amount / phasePrice)

      if (sharesAmount <= 0) {
        throw new Error('Invalid share calculation - amount too small or phase price invalid')
      }

      // Start transaction
      const { error: updateError } = await serviceClient
        .from('crypto_payment_transactions')
        .update({
          status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by_admin_id: currentUser.database_user.id,
          verification_status: 'verified'
        })
        .eq('id', payment.id)

      if (updateError) {
        throw updateError
      }

      // Create share purchase record using service role client to bypass RLS
      const { error: shareError } = await serviceClient
        .from('aureus_share_purchases')
        .insert({
          user_id: payment.user_id,
          package_name: `${currentPhase.phase_name || 'Pre Sale'} Purchase`,
          shares_purchased: sharesAmount,
          total_amount: payment.amount,
          price_per_share: parseFloat(currentPhase.price_per_share),
          phase_id: currentPhase.id,
          payment_method: `${payment.network} (${payment.currency})`,
          transaction_reference: payment.transaction_hash,
          status: 'active',
          purchase_date: new Date().toISOString()
        })

      if (shareError) {
        throw shareError
      }

      // Update phase sold count
      const { error: phaseUpdateError } = await serviceClient
        .from('investment_phases')
        .update({
          shares_sold: (currentPhase.shares_sold || 0) + sharesAmount,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentPhase.id)

      if (phaseUpdateError) {
        throw phaseUpdateError
      }

      // Ensure shareholder has TTTFOUNDER as sponsor (for commission purposes)
      await ensureShareholderSponsor(payment.user_id)

      // Process referral commissions using bulletproof safeguard system
      console.log('🛡️  USING BULLETPROOF COMMISSION SYSTEM')
      const { CommissionSafeguardService } = await import('../../lib/services/commissionSafeguardService')

      const commissionResult = await CommissionSafeguardService.processCommissionWithSafeguards({
        userId: payment.user_id,
        amount: payment.amount,
        shares: sharesAmount,
        currentPhase: currentPhase,
        transactionId: payment.id,
        adminProcessed: true
      })

      if (!commissionResult.success && commissionResult.error !== 'No referrer found') {
        console.error('🛡️  BULLETPROOF COMMISSION SYSTEM FAILED:', commissionResult.error)
        console.error('🛡️  Validation errors:', commissionResult.validationErrors)
        throw new Error(`Commission processing failed: ${commissionResult.error}`)
      }

      if (commissionResult.success && commissionResult.commissionTransactionId) {
        console.log('🛡️  BULLETPROOF COMMISSION SYSTEM SUCCESS:')
        console.log(`🛡️  Commission ID: ${commissionResult.commissionTransactionId}`)
        console.log(`🛡️  USDT Commission: $${commissionResult.usdtCommission?.toFixed(2)}`)
        console.log(`🛡️  Share Commission: ${commissionResult.shareCommission?.toFixed(4)} shares`)
      }

      // Legacy fallback (will be removed after testing)
      const commissionData = commissionResult.success ? {
        referrerId: commissionResult.referrerId,
        commissionId: commissionResult.commissionTransactionId,
        usdtCommission: commissionResult.usdtCommission,
        shareCommission: commissionResult.shareCommission
      } : null

      // Send payment approval notification to user
      await notificationService.sendPaymentApprovalNotification(
        payment.user_id,
        {
          payment_id: payment.id,
          amount: payment.amount,
          shares: sharesAmount,
          price_per_share: phasePrice,
          network: payment.network,
          processed_date: new Date().toISOString()
        }
      )

      // Send commission notification to referrer if commission was earned
      if (commissionData && commissionData.referrerId) {
        await notificationService.sendCommissionEarnedNotification(
          commissionData.referrerId,
          {
            commission_id: commissionData.commissionId,
            referred_username: payment.users?.username || 'Unknown User',
            investment_amount: payment.amount,
            shares_purchased: sharesAmount,
            usdt_commission: commissionData.usdtCommission,
            share_commission: commissionData.shareCommission,
            total_usdt_balance: commissionData.totalUsdtBalance,
            total_share_balance: commissionData.totalShareBalance,
            earned_date: new Date().toISOString()
          }
        )
      }

      setMessage({
        type: 'success',
        text: `Payment approved successfully! ${sharesAmount} shares allocated to ${payment.users?.username}. Notifications sent.`
      })

      // Reload payments
      await loadPayments()
      setSelectedPayment(null)

    } catch (error: any) {
      console.error('Error approving payment:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to approve payment' })
    } finally {
      setProcessingPayment(null)
    }
  }

  const ensureShareholderSponsor = async (userId: number) => {
    try {
      console.log('🤝 Ensuring shareholder has TTTFOUNDER sponsor for user:', userId)

      // Use service role client for admin operations
      const serviceClient = getServiceRoleClient()

      // Check if user already has an active referral relationship
      const { data: existingReferral, error: referralCheckError } = await serviceClient
        .from('referrals')
        .select('id, referrer_id')
        .eq('referred_id', userId)
        .eq('status', 'active')
        .single()

      if (!referralCheckError && existingReferral) {
        console.log('✅ User already has active sponsor:', existingReferral.referrer_id)
        return
      }

      // Get TTTFOUNDER user ID
      const { data: tttfounder, error: founderError } = await serviceClient
        .from('users')
        .select('id, username')
        .eq('username', 'TTTFOUNDER')
        .eq('is_active', true)
        .single()

      if (founderError || !tttfounder) {
        console.error('❌ TTTFOUNDER not found:', founderError)
        return
      }

      // Create referral relationship with TTTFOUNDER
      const referralCode = `TTTFOUNDER_${userId}_${Date.now()}`

      const { error: referralError } = await serviceClient
        .from('referrals')
        .insert({
          referrer_id: tttfounder.id,
          referred_id: userId,
          referral_code: referralCode,
          commission_rate: 15.00,
          status: 'active',
          campaign_source: 'shareholder_default',
          created_at: new Date().toISOString()
        })

      if (referralError) {
        console.error('❌ Failed to create TTTFOUNDER referral:', referralError)
      } else {
        console.log('✅ TTTFOUNDER referral relationship created for shareholder')
      }

    } catch (error) {
      console.error('❌ Error ensuring shareholder sponsor:', error)
    }
  }

  const processReferralCommissions = async (userId: number, paymentAmount: number, sharesAmount: number, currentPhase: any) => {
    try {
      // Use service role client for admin operations
      const serviceClient = getServiceRoleClient()

      // Get user's referrer
      const { data: referralData, error: referralError } = await serviceClient
        .from('referrals')
        .select('referrer_id, referred_id, commission_rate')
        .eq('referred_id', userId)
        .eq('status', 'active')
        .single()

      if (referralError || !referralData) {
        console.log('No referrer found for user:', userId)
        return null
      }

      // Calculate commissions based on phase rules
      // Phase 1 (Pre Sale): 15% USDT + 15% shares
      // Future phases may have different commission structures
      const commissionRate = 0.15 // 15% for Pre Sale phase
      const usdtCommission = paymentAmount * commissionRate
      const shareCommission = sharesAmount * commissionRate

      console.log(`💰 COMMISSION PROCESSING: Processing commissions for User ${userId}:`)
      console.log(`💰 COMMISSION PROCESSING: Payment: $${paymentAmount}, Shares: ${sharesAmount}`)
      console.log(`💰 COMMISSION PROCESSING: USDT Commission: $${usdtCommission.toFixed(2)}`)
      console.log(`💰 COMMISSION PROCESSING: Share Commission: ${shareCommission.toFixed(2)} shares`)
      console.log(`💰 COMMISSION PROCESSING: Phase: ${currentPhase?.id} (${currentPhase?.phase_name})`)
      console.log(`💰 COMMISSION PROCESSING: Referrer ID: ${referralData.referrer_id}`)
      console.log(`💰 COMMISSION PROCESSING: Commission Rate: ${commissionRate * 100}%`)

      // Create commission transaction with phase tracking
      console.log(`💰 COMMISSION PROCESSING: Creating commission transaction...`)
      const commissionData = {
        referrer_id: referralData.referrer_id,
        referred_id: referralData.referred_id,
        share_purchase_id: null, // Will be updated if needed
        commission_rate: commissionRate * 100, // Store as percentage
        share_purchase_amount: paymentAmount,
        usdt_commission: usdtCommission,
        share_commission: shareCommission,
        phase_id: currentPhase?.id || null,
        status: 'approved',
        payment_date: new Date().toISOString()
      }
      console.log(`💰 COMMISSION PROCESSING: Commission data:`, commissionData)

      const { data: commissionTransaction, error: commissionError } = await serviceClient
        .from('commission_transactions')
        .insert(commissionData)
        .select()
        .single()

      console.log(`💰 COMMISSION PROCESSING: Commission transaction result:`, commissionTransaction)
      console.log(`💰 COMMISSION PROCESSING: Commission error:`, commissionError)

      if (commissionError) {
        console.error('❌ COMMISSION PROCESSING: Failed to create commission transaction:', commissionError)
        throw new Error(`Commission creation failed: ${commissionError.message}`)
      }

      if (!commissionTransaction) {
        console.error('❌ COMMISSION PROCESSING: No commission transaction returned')
        throw new Error('Commission transaction creation failed - no data returned')
      }

      console.log(`✅ COMMISSION PROCESSING: Commission transaction created successfully with ID: ${commissionTransaction.id}`)

      // Trigger email notification for commission
      if (!commissionError && commissionTransaction?.id) {
        try {
          const { emailNotificationTriggers } = await import('../../lib/services/emailNotificationTriggers')
          await emailNotificationTriggers.triggerCommissionNotification(commissionTransaction.id)
        } catch (emailError) {
          console.warn('Failed to trigger commission email notification:', emailError)
          // Don't fail the payment if email fails
        }
      }

      if (commissionError) {
        console.error('Error creating commission:', commissionError)
        return null
      }

      // Update commission balance
      const { data: existingBalance, error: balanceError } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', referralData.referrer_id)
        .single()

      const currentUSDT = existingBalance ? parseFloat(existingBalance.usdt_balance || '0') : 0
      const currentShares = existingBalance ? parseFloat(existingBalance.share_balance || '0') : 0
      const totalEarnedUSDT = existingBalance ? parseFloat(existingBalance.total_earned_usdt || '0') : 0
      const totalEarnedShares = existingBalance ? parseFloat(existingBalance.total_earned_shares || '0') : 0

      const newUsdtBalance = currentUSDT + usdtCommission
      const newShareBalance = currentShares + shareCommission
      const newTotalEarnedUSDT = totalEarnedUSDT + usdtCommission
      const newTotalEarnedShares = totalEarnedShares + shareCommission

      console.log(`📊 Updating commission balance for referrer ${referralData.referrer_id}:`)
      console.log(`   Previous: $${currentUSDT.toFixed(2)} USDT, ${currentShares.toFixed(2)} shares`)
      console.log(`   Adding: $${usdtCommission.toFixed(2)} USDT, ${shareCommission.toFixed(2)} shares`)
      console.log(`   New Balance: $${newUsdtBalance.toFixed(2)} USDT, ${newShareBalance.toFixed(2)} shares`)

      const balanceUpdateData = {
        user_id: referralData.referrer_id,
        usdt_balance: newUsdtBalance,
        share_balance: newShareBalance,
        total_earned_usdt: newTotalEarnedUSDT,
        total_earned_shares: newTotalEarnedShares,
        last_updated: new Date().toISOString()
      }

      const { error: updateBalanceError } = await serviceClient
        .from('commission_balances')
        .upsert(balanceUpdateData, { onConflict: 'user_id' })

      if (updateBalanceError) {
        console.error('Error updating commission balance:', updateBalanceError)
        return null
      }

      // Return commission data for notification
      return {
        referrerId: referralData.referrer_id,
        commissionId: commissionTransaction.id,
        usdtCommission,
        shareCommission,
        totalUsdtBalance: newUsdtBalance,
        totalShareBalance: newShareBalance
      }

    } catch (error) {
      console.error('Error processing referral commissions:', error)
      return null
    }
  }

  const rejectPayment = async (payment: Payment, reason: string) => {
    if (!currentUser?.database_user?.id) {
      setMessage({ type: 'error', text: 'Admin user not found' })
      return
    }

    setProcessingPayment(payment.id)
    setMessage(null)

    try {
      // Use service role client for admin operations
      const serviceClient = getServiceRoleClient()

      const { error: updateError } = await serviceClient
        .from('crypto_payment_transactions')
        .update({
          status: 'rejected',
          rejected_at: new Date().toISOString(),
          rejected_by_admin_id: currentUser.database_user.id,
          rejection_reason: reason.trim(),
          verification_status: 'rejected'
        })
        .eq('id', payment.id)

      if (updateError) {
        throw updateError
      }

      // Send payment rejection notification to user
      await notificationService.sendPaymentRejectionNotification(
        payment.user_id,
        {
          payment_id: payment.id,
          amount: payment.amount,
          network: payment.network,
          tx_hash: payment.transaction_hash || '',
          rejection_reason: reason.trim(),
          rejected_date: new Date().toISOString()
        }
      )

      setMessage({
        type: 'success',
        text: `Payment rejected successfully. User ${payment.users?.username} has been notified.`
      })

      // Reload payments
      await loadPayments()
      setSelectedPayment(null)
      setShowRejectionModal(false)
      setRejectionReason('')

    } catch (error: any) {
      console.error('Error rejecting payment:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to reject payment' })
    } finally {
      setProcessingPayment(null)
    }
  }

  // Payment Reversal System
  const reversePayment = async (payment: Payment, reason: string) => {
    const timestamp = new Date().toISOString()
    console.log(`[PAYMENT_REVERSAL] ${timestamp} - Starting reversal process for payment ${payment.id}`)

    setProcessingReversal(true)
    setMessage(null)

    try {
      // Use service role client for admin operations
      const serviceClient = getServiceRoleClient()

      // Validation: Check payment status
      if (payment.status !== 'approved') {
        throw new Error('Payment not found or not in approved status')
      }

      if (payment.status === 'reversed') {
        throw new Error('Payment has already been reversed')
      }

      console.log(`[PAYMENT_REVERSAL] ${timestamp} - Validation passed, beginning database transaction`)

      // Step 1: Update payment transaction status
      console.log(`[PAYMENT_REVERSAL] ${timestamp} - Updating payment transaction status`)
      const { error: updateError } = await serviceClient
        .from('crypto_payment_transactions')
        .update({
          status: 'reversed',
          reversed_at: new Date().toISOString(),
          reversed_by_admin_id: currentUser.database_user.id,
          reversal_reason: reason.trim()
        })
        .eq('id', payment.id)

      if (updateError) {
        console.error(`[PAYMENT_REVERSAL] ${timestamp} - Failed to update payment status:`, updateError)
        throw new Error(`Failed to update payment status: ${updateError.message}`)
      }

      console.log(`[PAYMENT_REVERSAL] ${timestamp} - Payment status updated successfully`)

      // Step 2: Find and remove related share purchases
      console.log(`[PAYMENT_REVERSAL] ${timestamp} - Searching for related share purchases`)

      // Search by transaction_reference first
      let { data: sharePurchases, error: shareQueryError } = await serviceClient
        .from('aureus_share_purchases')
        .select('*')
        .eq('transaction_reference', payment.transaction_hash)
        .eq('status', 'active')

      // If no results, try searching by investment_id in payment table
      if (!shareQueryError && (!sharePurchases || sharePurchases.length === 0)) {
        console.log(`[PAYMENT_REVERSAL] ${timestamp} - No shares found by transaction_reference, checking investment_id`)

        // Get payment with investment_id
        const { data: paymentData, error: paymentError } = await serviceClient
          .from('crypto_payment_transactions')
          .select('investment_id')
          .eq('id', payment.id)
          .single()

        if (!paymentError && paymentData?.investment_id) {
          const { data: sharesByInvestmentId, error: sharesByIdError } = await serviceClient
            .from('aureus_share_purchases')
            .select('*')
            .eq('id', paymentData.investment_id)
            .eq('status', 'active')

          if (!sharesByIdError && sharesByInvestmentId) {
            sharePurchases = sharesByInvestmentId
          }
        }
      }

      if (shareQueryError) {
        console.error(`[PAYMENT_REVERSAL] ${timestamp} - Error querying share purchases:`, shareQueryError)
        throw new Error(`Failed to query share purchases: ${shareQueryError.message}`)
      }

      let totalSharesRemoved = 0
      let phaseUpdates: { [key: number]: number } = {}

      if (sharePurchases && sharePurchases.length > 0) {
        console.log(`[PAYMENT_REVERSAL] ${timestamp} - Found ${sharePurchases.length} share purchase(s) to reverse`)

        // Calculate total shares and phase impacts
        for (const purchase of sharePurchases) {
          totalSharesRemoved += purchase.shares_purchased
          if (purchase.phase_id) {
            phaseUpdates[purchase.phase_id] = (phaseUpdates[purchase.phase_id] || 0) + purchase.shares_purchased
          }
        }

        // Delete share purchase records
        const shareIds = sharePurchases.map(sp => sp.id)
        const { error: deleteShareError } = await serviceClient
          .from('aureus_share_purchases')
          .delete()
          .in('id', shareIds)

        if (deleteShareError) {
          console.error(`[PAYMENT_REVERSAL] ${timestamp} - Failed to delete share purchases:`, deleteShareError)
          throw new Error(`Failed to remove share purchases: ${deleteShareError.message}`)
        }

        console.log(`[PAYMENT_REVERSAL] ${timestamp} - Removed ${totalSharesRemoved} shares from ${sharePurchases.length} purchase record(s)`)

        // Update investment phases
        for (const [phaseId, sharesToSubtract] of Object.entries(phaseUpdates)) {
          console.log(`[PAYMENT_REVERSAL] ${timestamp} - Updating phase ${phaseId}, subtracting ${sharesToSubtract} shares`)

          // Get current shares_sold for this phase
          const { data: currentPhaseData, error: phaseQueryError } = await serviceClient
            .from('investment_phases')
            .select('shares_sold')
            .eq('id', parseInt(phaseId))
            .single()

          if (phaseQueryError) {
            console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: Could not query phase ${phaseId}:`, phaseQueryError)
            continue
          }

          const currentSharesSold = currentPhaseData?.shares_sold || 0
          const newSharesSold = Math.max(0, currentSharesSold - parseInt(sharesToSubtract))

          const { error: phaseUpdateError } = await serviceClient
            .from('investment_phases')
            .update({
              shares_sold: newSharesSold,
              updated_at: new Date().toISOString()
            })
            .eq('id', parseInt(phaseId))

          if (phaseUpdateError) {
            console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: Failed to update phase ${phaseId}:`, phaseUpdateError)
            // Continue with reversal even if phase update fails
          }
        }
      } else {
        console.log(`[PAYMENT_REVERSAL] ${timestamp} - No share purchases found to reverse`)
      }

      // Step 3: Handle commission reversal
      console.log(`[PAYMENT_REVERSAL] ${timestamp} - Searching for related commission transactions`)

      const { data: commissionTransactions, error: commissionQueryError } = await serviceClient
        .from('commission_transactions')
        .select('*')
        .eq('referred_id', payment.user_id)
        .eq('share_purchase_amount', payment.amount)
        .eq('status', 'approved')
        .gte('payment_date', new Date(new Date(payment.created_at).getTime() - 24 * 60 * 60 * 1000).toISOString()) // Within 24 hours
        .lte('payment_date', new Date(new Date(payment.created_at).getTime() + 24 * 60 * 60 * 1000).toISOString())

      if (commissionQueryError) {
        console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: Error querying commissions:`, commissionQueryError)
      }

      if (commissionTransactions && commissionTransactions.length > 0) {
        console.log(`[PAYMENT_REVERSAL] ${timestamp} - Found ${commissionTransactions.length} commission transaction(s) to reverse`)

        for (const commission of commissionTransactions) {
          // Mark commission as reversed
          const { error: commissionUpdateError } = await serviceClient
            .from('commission_transactions')
            .update({
              status: 'reversed',
              updated_at: new Date().toISOString()
            })
            .eq('id', commission.id)

          if (commissionUpdateError) {
            console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: Failed to reverse commission ${commission.id}:`, commissionUpdateError)
            continue
          }

          // Update commission balances
          const { data: currentBalance, error: balanceQueryError } = await serviceClient
            .from('commission_balances')
            .select('*')
            .eq('user_id', commission.referrer_id)
            .single()

          if (balanceQueryError) {
            console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: Could not find commission balance for user ${commission.referrer_id}`)
            continue
          }

          const newUsdtBalance = Math.max(0, (parseFloat(currentBalance.usdt_balance || '0') - commission.usdt_commission))
          const newShareBalance = Math.max(0, (parseFloat(currentBalance.share_balance || '0') - commission.share_commission))
          const newTotalEarnedUSDT = Math.max(0, (parseFloat(currentBalance.total_earned_usdt || '0') - commission.usdt_commission))
          const newTotalEarnedShares = Math.max(0, (parseFloat(currentBalance.total_earned_shares || '0') - commission.share_commission))

          // Log if balances would go negative
          if (newUsdtBalance === 0 && parseFloat(currentBalance.usdt_balance || '0') > commission.usdt_commission) {
            console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: USDT balance for user ${commission.referrer_id} would go negative, set to 0`)
          }
          if (newShareBalance === 0 && parseFloat(currentBalance.share_balance || '0') > commission.share_commission) {
            console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: Share balance for user ${commission.referrer_id} would go negative, set to 0`)
          }

          const { error: balanceUpdateError } = await serviceClient
            .from('commission_balances')
            .update({
              usdt_balance: newUsdtBalance,
              share_balance: newShareBalance,
              total_earned_usdt: newTotalEarnedUSDT,
              total_earned_shares: newTotalEarnedShares,
              last_updated: new Date().toISOString()
            })
            .eq('user_id', commission.referrer_id)

          if (balanceUpdateError) {
            console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: Failed to update commission balance for user ${commission.referrer_id}:`, balanceUpdateError)
          } else {
            console.log(`[PAYMENT_REVERSAL] ${timestamp} - Updated commission balance for user ${commission.referrer_id}`)
          }
        }
      } else {
        console.log(`[PAYMENT_REVERSAL] ${timestamp} - No commission transactions found to reverse`)
      }

      // Step 4: Send notification to user
      console.log(`[PAYMENT_REVERSAL] ${timestamp} - Sending reversal notification to user`)
      try {
        await notificationService.sendPaymentReversalNotification(
          payment.user_id,
          {
            payment_id: payment.id,
            amount: payment.amount,
            network: payment.network,
            tx_hash: payment.transaction_hash || '',
            reversal_reason: reason.trim(),
            reversed_date: new Date().toISOString(),
            shares_removed: totalSharesRemoved
          }
        )
        console.log(`[PAYMENT_REVERSAL] ${timestamp} - Notification sent successfully`)
      } catch (notificationError) {
        console.warn(`[PAYMENT_REVERSAL] ${timestamp} - Warning: Failed to send notification:`, notificationError)
        // Don't fail the reversal if notification fails
      }

      console.log(`[PAYMENT_REVERSAL] ${timestamp} - Payment reversal completed successfully`)

      setMessage({
        type: 'success',
        text: `Payment reversed successfully. ${totalSharesRemoved} shares removed. User ${payment.users?.username} has been notified.`
      })

      // Reload payments and close modal
      await loadPayments()
      setSelectedPayment(null)
      setShowReversalModal(false)
      setReversalReason('')
      setReversalConfirmation(false)
      setReversalConfirmText('')

    } catch (error: any) {
      console.error(`[PAYMENT_REVERSAL] ${timestamp} - Reversal failed:`, error)
      setMessage({
        type: 'error',
        text: error.message || 'Failed to reverse payment. Please check logs for details.'
      })
    } finally {
      setProcessingReversal(false)
    }
  }

  const handleReverseClick = (payment: Payment) => {
    if (payment.status !== 'approved') {
      setMessage({ type: 'error', text: 'Only approved payments can be reversed' })
      return
    }

    if (payment.status === 'reversed') {
      setMessage({ type: 'error', text: 'Payment has already been reversed' })
      return
    }

    setSelectedPayment(payment)
    setShowReversalModal(true)
    setReversalReason('')
    setReversalConfirmation(false)
    setReversalConfirmText('')
  }

  const handleReversalSubmit = () => {
    if (!selectedPayment) {
      setMessage({ type: 'error', text: 'No payment selected' })
      return
    }

    if (!reversalReason.trim() || reversalReason.trim().length < 10) {
      setMessage({ type: 'error', text: 'Reversal reason must be at least 10 characters long' })
      return
    }

    if (!reversalConfirmation) {
      setMessage({ type: 'error', text: 'Please confirm that you understand this action cannot be undone' })
      return
    }

    if (reversalConfirmText.toUpperCase() !== 'REVERSE') {
      setMessage({ type: 'error', text: 'Please type "REVERSE" to confirm the reversal' })
      return
    }

    reversePayment(selectedPayment, reversalReason)
  }

  const handleRejectClick = (payment: Payment) => {
    setSelectedPayment(payment)
    setShowRejectionModal(true)
    setRejectionReason('')
  }

  const handleRejectSubmit = () => {
    if (!selectedPayment || !rejectionReason.trim()) {
      setMessage({ type: 'error', text: 'Please provide a rejection reason' })
      return
    }

    if (rejectionReason.trim().length < 5) {
      setMessage({ type: 'error', text: 'Rejection reason must be at least 5 characters long' })
      return
    }

    rejectPayment(selectedPayment, rejectionReason)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Payment Management</h2>
        <button
          onClick={loadPayments}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg border ${
          message.type === 'success' ? 'bg-green-900/20 border-green-500/30 text-green-300' :
          message.type === 'error' ? 'bg-red-900/20 border-red-500/30 text-red-300' :
          'bg-blue-900/20 border-blue-500/30 text-blue-300'
        }`}>
          {message.text}
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        {(['pending', 'approved', 'rejected'] as const).map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab
                ? 'bg-yellow-500 text-black'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            }`}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)} Payments
          </button>
        ))}
      </div>

      {/* Payments List */}
      <div className="bg-gray-800 rounded-lg border border-gray-700">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading payments...</p>
          </div>
        ) : payments.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-400">No {activeTab} payments found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {payments.map((payment) => (
              <div key={payment.id} className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
                {/* Header with amount and status */}
                <div className="bg-gray-750 px-6 py-4 border-b border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getNetworkIcon(payment.network)}</span>
                      <div>
                        <h3 className="text-xl font-bold text-white">
                          {formatCurrency(payment.amount)}
                        </h3>
                        <p className="text-sm text-gray-400">
                          {(payment.shares_to_purchase || 0).toLocaleString()} shares
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(payment.status)}`}>
                        {payment.status.toUpperCase()}
                      </span>
                      <span className="text-sm text-gray-400">
                        {getTimeAgo(payment.created_at)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Main content */}
                <div className="p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* User Information */}
                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-white mb-3">👤 User Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Name:</span>
                          <span className="text-white font-medium">
                            {payment.users?.full_name || payment.users?.username || 'Unknown User'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Email:</span>
                          <span className="text-white font-mono text-xs">
                            {payment.users?.email || 'No email'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">User ID:</span>
                          <span className="text-white font-mono">
                            {payment.user_id}
                          </span>
                        </div>

                        {/* Telegram Username */}
                        {payment.users?.telegram_users?.[0]?.username && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Telegram:</span>
                            <a
                              href={`https://t.me/${payment.users.telegram_users[0].username}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-400 hover:text-blue-300 font-mono text-xs underline transition-colors"
                              title="Contact user on Telegram"
                            >
                              @{payment.users.telegram_users[0].username}
                            </a>
                          </div>
                        )}

                        {/* Phone Number */}
                        {payment.users?.phone_number && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Phone:</span>
                            <a
                              href={`tel:${payment.users.phone_number}`}
                              className="text-green-400 hover:text-green-300 font-mono text-xs underline transition-colors"
                              title="Call user"
                            >
                              {payment.users.phone_number}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Payment Details */}
                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-white mb-3">💳 Payment Details</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Network:</span>
                          <span className="text-white font-medium">
                            {payment.network || 'Unknown'} ({payment.currency || 'Unknown'})
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-400">Payment ID:</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-white font-mono text-xs break-all max-w-xs">
                              {payment.id}
                            </span>
                            <button
                              onClick={() => navigator.clipboard.writeText(payment.id)}
                              className="p-1 text-gray-400 hover:text-white transition-colors"
                              title="Copy Payment ID"
                            >
                              📋
                            </button>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Created:</span>
                          <span className="text-white">
                            {formatDate(payment.created_at)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Wallet Information */}
                  <div className="mt-6 p-4 bg-gray-900 rounded-lg border border-gray-600">
                    <h4 className="text-lg font-semibold text-white mb-3">🔗 Wallet & Transaction Details</h4>
                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="text-gray-400 block mb-1">Sender Wallet:</span>
                        <div className="bg-gray-800 p-2 rounded border border-gray-600">
                          <span className="text-green-400 font-mono text-xs break-all">
                            {payment.sender_wallet || 'Not provided'}
                          </span>
                          {payment.sender_wallet && (
                            <button
                              onClick={() => navigator.clipboard.writeText(payment.sender_wallet)}
                              className="ml-2 text-blue-400 hover:text-blue-300 text-xs"
                              title="Copy to clipboard"
                            >
                              📋
                            </button>
                          )}
                        </div>
                      </div>

                      <div>
                        <span className="text-gray-400 block mb-1">Receiver Wallet:</span>
                        <div className="bg-gray-800 p-2 rounded border border-gray-600">
                          <span className="text-blue-400 font-mono text-xs break-all">
                            {payment.receiver_wallet || 'Not provided'}
                          </span>
                          {payment.receiver_wallet && (
                            <button
                              onClick={() => navigator.clipboard.writeText(payment.receiver_wallet)}
                              className="ml-2 text-blue-400 hover:text-blue-300 text-xs"
                              title="Copy to clipboard"
                            >
                              📋
                            </button>
                          )}
                        </div>
                      </div>

                      {payment.transaction_hash && payment.network !== 'BANK_TRANSFER' ? (
                        <div>
                          <span className="text-gray-400 block mb-1">Transaction Hash:</span>
                          <div className="bg-gray-800 p-2 rounded border border-gray-600">
                            <span className="text-yellow-400 font-mono text-xs break-all">
                              {payment.transaction_hash}
                            </span>
                            <button
                              onClick={() => navigator.clipboard.writeText(payment.transaction_hash)}
                              className="ml-2 text-blue-400 hover:text-blue-300 text-xs"
                              title="Copy to clipboard"
                            >
                              📋
                            </button>
                          </div>
                        </div>
                      ) : payment.network === 'BANK_TRANSFER' ? (
                        <div className="text-center py-2">
                          <span className="text-orange-400 text-sm">🏦 Bank Transfer - No transaction hash</span>
                        </div>
                      ) : (
                        <div className="text-center py-2">
                          <span className="text-red-400 text-sm">⚠️ No transaction hash provided</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Rejection Reason */}
                  {payment.rejection_reason && (
                    <div className="mt-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                      <h4 className="text-red-300 font-semibold mb-2">❌ Rejection Reason:</h4>
                      <p className="text-red-300 text-sm">{payment.rejection_reason}</p>
                    </div>
                  )}

                  {/* Reversal Reason */}
                  {payment.reversal_reason && (
                    <div className="mt-4 p-4 bg-orange-900/20 border border-orange-500/30 rounded-lg">
                      <h4 className="text-orange-300 font-semibold mb-2">🔄 Reversal Reason:</h4>
                      <p className="text-orange-300 text-sm">{payment.reversal_reason}</p>
                      {payment.reversed_at && (
                        <p className="text-orange-400 text-xs mt-2">
                          Reversed on: {new Date(payment.reversed_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="mt-6 flex flex-wrap gap-3">
                    {payment.status === 'pending' && (
                      <>
                        <button
                          onClick={() => approvePayment(payment)}
                          disabled={processingPayment === payment.id}
                          className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50 font-medium"
                        >
                          {processingPayment === payment.id ? (
                            <span className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                              Processing...
                            </span>
                          ) : (
                            '✅ Approve Payment'
                          )}
                        </button>
                        <button
                          onClick={() => handleRejectClick(payment)}
                          disabled={processingPayment === payment.id}
                          className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 font-medium"
                        >
                          ❌ Reject Payment
                        </button>
                      </>
                    )}

                    {/* Reverse Payment Button for Approved Payments */}
                    {payment.status === 'approved' && (
                      <button
                        onClick={() => handleReverseClick(payment)}
                        disabled={processingReversal}
                        className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 font-medium"
                      >
                        {processingReversal ? (
                          <span className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                            Reversing...
                          </span>
                        ) : (
                          '🔄 Reverse Payment'
                        )}
                      </button>
                    )}

                    {payment.screenshot_url && payment.network !== 'BANK_TRANSFER' ? (
                      <button
                        onClick={() => handleViewScreenshot(payment)}
                        className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                      >
                        📷 View Screenshot
                      </button>
                    ) : payment.network === 'BANK_TRANSFER' ? (
                      <div className="px-6 py-3 bg-gray-600 text-gray-300 rounded-lg font-medium">
                        🏦 Bank Transfer - No screenshot required
                      </div>
                    ) : (
                      <div className="px-6 py-3 bg-orange-600 text-white rounded-lg font-medium">
                        ⚠️ No screenshot provided
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Rejection Modal */}
      {showRejectionModal && selectedPayment && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-bold text-white mb-4">Reject Payment</h3>
            
            <div className="mb-4">
              <p className="text-gray-300 mb-2">
                <strong>Payment:</strong> {formatCurrency(selectedPayment.amount || 0)}
              </p>
              <p className="text-gray-300 mb-4">
                <strong>User:</strong> {selectedPayment.users?.full_name || selectedPayment.users?.username || 'Unknown User'}
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Rejection Reason *
              </label>
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Please provide a detailed reason for rejecting this payment..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
                rows={4}
              />
              <p className="text-xs text-gray-400 mt-1">
                This message will be sent to the user. Minimum 5 characters.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleRejectSubmit}
                disabled={processingPayment === selectedPayment.id || !rejectionReason.trim()}
                className="flex-1 py-2 px-4 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {processingPayment === selectedPayment.id ? 'Processing...' : 'Confirm Rejection'}
              </button>
              <button
                onClick={() => {
                  setShowRejectionModal(false)
                  setSelectedPayment(null)
                  setRejectionReason('')
                }}
                disabled={processingPayment === selectedPayment.id}
                className="flex-1 py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Screenshot Modal */}
      {showScreenshotModal && currentScreenshot && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
          <div className="relative max-w-4xl max-h-[90vh] mx-4">
            <button
              onClick={() => {
                setShowScreenshotModal(false)
                setCurrentScreenshot(null)
                // Clean up blob URL if it was created
                if (currentScreenshot.startsWith('blob:')) {
                  URL.revokeObjectURL(currentScreenshot)
                }
              }}
              className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors"
            >
              ✕
            </button>
            <img
              src={currentScreenshot}
              alt="Payment Screenshot"
              className="max-w-full max-h-full object-contain rounded-lg"
              onError={() => {
                setMessage({ type: 'error', text: 'Failed to load screenshot image' })
                setShowScreenshotModal(false)
                setCurrentScreenshot(null)
              }}
            />
            <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-2 rounded-lg text-sm">
              Payment Proof Screenshot
            </div>
          </div>
        </div>
      )}

      {/* Payment Reversal Modal */}
      {showReversalModal && selectedPayment && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-bold text-white mb-4">🔄 Reverse Payment</h3>

            {/* Warning */}
            <div className="mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
              <h4 className="text-red-300 font-semibold mb-2">⚠️ Warning</h4>
              <p className="text-red-300 text-sm">
                This will permanently remove shares and reverse commissions. This action cannot be undone.
              </p>
            </div>

            {/* Payment Details Summary */}
            <div className="mb-6 p-4 bg-gray-900 rounded-lg border border-gray-600">
              <h4 className="text-white font-semibold mb-3">Payment Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Payment ID:</span>
                  <span className="text-white font-mono text-xs">{selectedPayment.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Amount:</span>
                  <span className="text-white font-medium">{formatCurrency(selectedPayment.amount || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">User:</span>
                  <span className="text-white">{selectedPayment.users?.full_name || selectedPayment.users?.username || 'Unknown User'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Shares to Remove:</span>
                  <span className="text-white">{(selectedPayment.shares_to_purchase || 0).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Network:</span>
                  <span className="text-white">{selectedPayment.network} ({selectedPayment.currency})</span>
                </div>
              </div>
            </div>

            {/* Reversal Reason */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Reversal Reason *
              </label>
              <textarea
                value={reversalReason}
                onChange={(e) => setReversalReason(e.target.value)}
                placeholder="Please provide a detailed reason for reversing this payment..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
                rows={4}
              />
              <div className="flex justify-between items-center mt-1">
                <p className="text-xs text-gray-400">
                  This message will be sent to the user. Minimum 10 characters.
                </p>
                <span className={`text-xs ${reversalReason.length >= 10 ? 'text-green-400' : 'text-red-400'}`}>
                  {reversalReason.length}/10
                </span>
              </div>
            </div>

            {/* Confirmation Checkbox */}
            <div className="mb-4">
              <label className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={reversalConfirmation}
                  onChange={(e) => setReversalConfirmation(e.target.checked)}
                  className="mt-1 w-4 h-4 text-orange-600 bg-gray-700 border-gray-600 rounded focus:ring-orange-500 focus:ring-2"
                />
                <span className="text-sm text-gray-300">
                  I understand this action cannot be undone and will permanently remove shares and reverse commissions.
                </span>
              </label>
            </div>

            {/* Secondary Confirmation */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Type "REVERSE" to confirm *
              </label>
              <input
                type="text"
                value={reversalConfirmText}
                onChange={(e) => setReversalConfirmText(e.target.value)}
                placeholder="Type REVERSE here"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-400 mt-1">
                This is a final confirmation step to prevent accidental reversals.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                onClick={handleReversalSubmit}
                disabled={
                  processingReversal ||
                  !reversalReason.trim() ||
                  reversalReason.length < 10 ||
                  !reversalConfirmation ||
                  reversalConfirmText.toUpperCase() !== 'REVERSE'
                }
                className="flex-1 py-3 px-4 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 font-medium"
              >
                {processingReversal ? (
                  <span className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Reversing Payment...
                  </span>
                ) : (
                  '🔄 Confirm Reversal'
                )}
              </button>
              <button
                onClick={() => {
                  setShowReversalModal(false)
                  setSelectedPayment(null)
                  setReversalReason('')
                  setReversalConfirmation(false)
                  setReversalConfirmText('')
                }}
                disabled={processingReversal}
                className="flex-1 py-3 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors disabled:opacity-50 font-medium"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
