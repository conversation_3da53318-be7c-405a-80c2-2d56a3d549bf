// API endpoint to create crypto payment transactions
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🚀 CRYPTO PAYMENT API CALLED');
    console.log('===============================');
    console.log('📝 Request body:', req.body);
    console.log('🔑 Authorization header:', req.headers.authorization);

    const {
      amount,
      shares_to_purchase,
      network,
      currency,
      sender_wallet,
      receiver_wallet,
      transaction_hash,
      screenshot_url,
      status = 'pending',
      transaction_notes
    } = req.body;

    // Validate required fields
    if (!amount || !shares_to_purchase || !network || !currency || !sender_wallet || !receiver_wallet || !transaction_hash || !screenshot_url) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Get user ID from authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }

    const userAuthId = authHeader.replace('Bearer ', '');
    console.log('🔍 Looking up user with auth ID:', userAuthId);

    // Look up user in database using different methods
    let userData = null;
    let userError = null;

    // Try direct ID lookup first (for db_ prefixed IDs)
    if (userAuthId.startsWith('db_')) {
      const dbUserId = parseInt(userAuthId.replace('db_', ''));
      console.log('✅ Using db_ prefixed ID:', dbUserId);
      userData = { id: dbUserId };
    } else if (/^\d+$/.test(userAuthId)) {
      // Direct numeric user ID
      console.log('✅ Using direct numeric ID:', userAuthId);
      userData = { id: parseInt(userAuthId) };
    } else {
      // Try auth_user_id lookup (standard Supabase auth UUID)
      console.log('✅ Trying auth_user_id lookup with UUID:', userAuthId);
      const result = await supabase
        .from('users')
        .select('id')
        .eq('auth_user_id', userAuthId)
        .single();
      userData = result.data;
      userError = result.error;

      if (userError) {
        console.error('❌ auth_user_id lookup failed, trying direct UUID lookup:', userError);
        // Fallback: try looking up by UUID directly in id field (in case it's stored there)
        const fallbackResult = await supabase
          .from('users')
          .select('id')
          .eq('id', userAuthId)
          .single();
        userData = fallbackResult.data;
        userError = fallbackResult.error;
      }
    }

    if (userError || !userData) {
      console.error('❌ User lookup failed:', userError);
      console.error('❌ Auth ID provided:', userAuthId);
      return res.status(401).json({
        error: 'User not found',
        details: userError?.message || 'No user data returned',
        authId: userAuthId
      });
    }

    const userId = userData.id;
    console.log('✅ User ID resolved:', userId);

    // Get current active phase
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (phaseError || !currentPhase) {
      return res.status(400).json({ error: 'No active investment phase found' });
    }

    // Create payment transaction record
    const paymentData = {
      user_id: userId,
      amount: parseFloat(amount),
      shares_to_purchase: parseInt(shares_to_purchase),
      network: network,
      currency: currency,
      sender_wallet: sender_wallet,
      receiver_wallet: receiver_wallet,
      transaction_hash: transaction_hash,
      screenshot_url: screenshot_url,
      status: status,
      transaction_notes: transaction_notes || null,
      created_at: new Date().toISOString()
    };

    console.log('💾 Creating payment with data:', paymentData);

    const { data: payment, error: paymentError } = await supabase
      .from('crypto_payment_transactions')
      .insert([paymentData])
      .select()
      .single();

    console.log('💾 Payment creation result:', { payment, paymentError });

    if (paymentError) {
      console.error('❌ Payment creation error:', paymentError);
      console.error('❌ Payment data that failed:', paymentData);
      return res.status(500).json({
        error: 'Failed to create payment transaction',
        details: paymentError.message,
        code: paymentError.code,
        hint: paymentError.hint
      });
    }

    // If this is a bank transfer, also create a record in aureus_share_purchases with pending status
    if (network === 'BANK_TRANSFER') {
      const sharesPurchaseData = {
        user_id: userId,
        phase_id: currentPhase.id,
        shares_purchased: parseInt(shares_to_purchase),
        amount_paid: parseFloat(amount),
        payment_method: 'bank_transfer',
        payment_status: 'pending',
        transaction_reference: payment.id,
        created_at: new Date().toISOString()
      };

      const { error: sharesError } = await supabase
        .from('aureus_share_purchases')
        .insert([sharesPurchaseData]);

      if (sharesError) {
        console.error('Error creating shares purchase record:', sharesError);
        // Don't fail the whole transaction, just log the error
      }
    }

    res.status(201).json({
      success: true,
      payment: payment,
      message: 'Payment transaction created successfully'
    });

  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
