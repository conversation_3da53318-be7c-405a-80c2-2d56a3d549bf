live:83 The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.
f429f1d4-aeb5-456b-acd2-b9ca2669c371:7 Refused to connect to 'https://overbridgenet.com/jsv8/offer' because it violates the following Content Security Policy directive: "connect-src 'self' http://localhost:* https://*.supabase.co https://api.stripe.com https://checkout.stripe.com https://cdn.jsdelivr.net wss://*.supabase.co https://www.google-analytics.com https://*.google-analytics.com https://*.googletagmanager.com".

_0x2affa8 @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
yPIMP @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
_0x3e6c6d @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
bEUNY @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:10
 XHR failed loading: POST "https://overbridgenet.com/jsv8/offer".
_0x2affa8 @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
yPIMP @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
_0x3e6c6d @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
bEUNY @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:10
 🚀 Loading universal error fixes...
 🚨 Global error caught: Illegal invocation
 Fetch failed loading: POST "https://www.google-analytics.com/mp/collect?measurement_id=G-03XW3FWG7L&api_secret=Px06eCtvQLS0hVSB2MPj_g".
NPWgj @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
window.<computed> @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:10
 🚨 Global error caught: Unexpected token '<'
 🔍 Raw Environment Variables:
 import.meta.env.VITE_SUPABASE_URL: https://fgubaqoftdeefcakejwu.supabase.co
 import.meta.env.VITE_SUPABASE_ANON_KEY exists: true
 All VITE_ vars: (24) ['VITE_RESEND_API_KEY', 'VITE_RESEND_FROM_EMAIL', 'VITE_RESEND_FROM_NAME', 'VITE_SUPABASE_ANON_KEY', 'VITE_SUPABASE_SERVICE_ROLE_KEY', 'VITE_SUPABASE_URL', 'VITE_VERCEL_BRANCH_URL', 'VITE_VERCEL_DEPLOYMENT_ID', 'VITE_VERCEL_ENV', 'VITE_VERCEL_GIT_COMMIT_AUTHOR_LOGIN', 'VITE_VERCEL_GIT_COMMIT_AUTHOR_NAME', 'VITE_VERCEL_GIT_COMMIT_MESSAGE', 'VITE_VERCEL_GIT_COMMIT_REF', 'VITE_VERCEL_GIT_COMMIT_SHA', 'VITE_VERCEL_GIT_PREVIOUS_SHA', 'VITE_VERCEL_GIT_PROVIDER', 'VITE_VERCEL_GIT_PULL_REQUEST_ID', 'VITE_VERCEL_GIT_REPO_ID', 'VITE_VERCEL_GIT_REPO_OWNER', 'VITE_VERCEL_GIT_REPO_SLUG', 'VITE_VERCEL_PROJECT_ID', 'VITE_VERCEL_PROJECT_PRODUCTION_URL', 'VITE_VERCEL_TARGET_ENV', 'VITE_VERCEL_URL']
 🔍 Supabase Config Check (FORCED):
 URL: https://fgubaqoftdeefcakejwu.supabase.co
 Anon Key exists: true
 Anon Key length: 208
 ✅ Supabase configuration looks good
 ✅ Supabase client created successfully
 🔧 Service role key available (FORCED): true
 🔧 Service role key length: 219
 🔧 Service role key starts with: eyJhbGciOiJIUzI1NiIs...
 🔧 Creating service role client with URL (FORCED): https://fgubaqoftdeefcakejwu.supabase.co
 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
overrideMethod @ installHook.js:1
he @ supabase-CcLtyElS-1758536206320.js:7
Cr @ supabase-CcLtyElS-1758536206320.js:8
_initSupabaseAuthClient @ supabase-CcLtyElS-1758536206320.js:8
Ur @ supabase-CcLtyElS-1758536206320.js:8
Fr @ supabase-CcLtyElS-1758536206320.js:8
ot @ index-Y3uU2Jna-1758536206320.js:46
_$ @ index-Y3uU2Jna-1758536206320.js:761
(anonymous) @ index-Y3uU2Jna-1758536206320.js:792
 🔧 Service role client created successfully
 🔧 Testing service role client...
 ✅ Support ticket notification service initialized
 ✅ Resend email service initialized
 ✅ KYC Notification Service initialized with existing email service
 🔧 API Configuration (FORCED VERCEL DETECTION): {hostname: 'www.aureus.africa', isVercelProduction: false, isViteProd: true, isProduction: true, VITE_API_BASE_URL: undefined, …}
 🚨 EMERGENCY API URL FIX - BUILD TIMESTAMP: 2025-01-15-14:30: {hostname: 'www.aureus.africa', originalAPI_BASE_URL: '', FORCE_API_BASE_URL: '', isVercel: false, buildTime: '2025-01-15-14:30'}
 🔄 URL routing useEffect triggered {path: '/live', currentSection: 'home', isMobile: false}
 ℹ️ No referral information found in URL
 ℹ️ No active referral session
 🔍 Checking path for affiliate landing: {path: '/live', username: 'live', hasSlash: false, length: 4, startsWithStatic: false}
 🎯 Potential affiliate landing page for username: live
 💰 Using simulated gold price: $109,828/kg
 🔍 Checking for existing session...
 🔍 [DEBUG] localStorage aureus_telegram_user: null
 🔍 [DEBUG] localStorage aureus_test_user: null
 📧 Using email user from localStorage
 🔄 Using cached profile picture URL (recent update detected)
 🔍 getUserType: Analyzing user: {id: 335, email: '<EMAIL>', role: 'user', telegram_id: null, account_type: 'email'}
 ⚠️ getUserType: Could not determine user type, defaulting to shareholder
 ✅ Email user loaded from localStorage: {id: 'db_335', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
 Email processing service started - checking every 30 seconds
 ✅ Email processing service initialized
 🔄 Loading site content...
 🔍 [DEBUG] getCurrentUser result: {id: 'db_335', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
 ✅ Found existing session: {id: 'db_335', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
 🔍 [DEBUG] currentUser.needsProfileCompletion: false
 🔍 [DEBUG] currentUser.user_metadata?.profile_completion_required: false
 🔍 [DEBUG] currentUser.database_user?.telegram_id: null
 🔍 Current path during session check: /live
 ✅ Profile complete, setting section to dashboard
 🔍 Dashboard route guard check: {needsProfileCompletion: false, profile_completion_required: false, currentSection: 'dashboard', userEmail: '<EMAIL>', hasDatabase: true, …}
 ✅ Dashboard access granted - user authenticated
jquery-3.4.1.min.js:2 Error: <path> attribute d: Expected number, "…               tc0.2,0,0.4-0.2,0…".
xe @ jquery-3.4.1.min.js:2
He @ jquery-3.4.1.min.js:2
append @ jquery-3.4.1.min.js:2
(anonymous) @ translateContent.js:1
e @ jquery-3.4.1.min.js:2
t @ jquery-3.4.1.min.js:2
setTimeout
(anonymous) @ jquery-3.4.1.min.js:2
c @ jquery-3.4.1.min.js:2
fireWith @ jquery-3.4.1.min.js:2
fire @ jquery-3.4.1.min.js:2
c @ jquery-3.4.1.min.js:2
fireWith @ jquery-3.4.1.min.js:2
ready @ jquery-3.4.1.min.js:2
setTimeout
(anonymous) @ jquery-3.4.1.min.js:2
(anonymous) @ jquery-3.4.1.min.js:2
(anonymous) @ jquery-3.4.1.min.js:2
 ✅ Using db_ prefixed ID for legal documents: 335
 🔄 Loading current user...
 ✅ Using user prop from login: {id: 'db_335', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
 📊 User ID from prop: 335
 🔍 DEBUG: User prop structure: {database_user_id: 335, user_metadata_user_id: 335, database_user: {…}, user_metadata: {…}}
 🔍 DEBUG: Checking user data conditions: {hasCurrentUser: true, hasDatabaseUser: true, hasUserMetadata: true, userId: 335, userIdType: 'number'}
 ✅ User data set from session: {telegram_id: null, username: 'live', full_name: 'Jean Pierre Rademeyer'}
 ✅ Default commission data set
 ✅ Default share purchases set
 🔍 DEBUG: About to check userId and currentUser: {hasUserId: true, userId: 335, hasCurrentUser: true, userIdType: 'number'}
 🔄 Setting user state and loading dashboard data for user ID: 335 Type: number
 ✅ Using numeric user ID for database operations: 335
 🚀 About to call data loading functions...
 🔄 Loading dashboard data for user: 335 number
 ✅ Using validated user ID: 335 Type: number
 🔔 Loading notification preferences for user: 335
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
 🛡️ Initializing comprehensive error monitoring...
 ✅ Error monitoring system initialized
 ✅ Error monitoring system loaded
live:21 Error loading notification preferences: SyntaxError: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
console.error @ live:21
console.error @ live:153
overrideMethod @ hook.js:608
console.error @ live:703
console.error @ errorMonitoring.js:124
ta @ UserDashboard.tsx:1415
await in ta
F.useEffect.Nl @ UserDashboard.tsx:999
$m @ react-dom-client.production.js:8292
bS @ react-dom-client.production.js:9771
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9774
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9765
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9765
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9774
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9782
MS @ react-dom-client.production.js:11313
(anonymous) @ react-dom-client.production.js:11048
k @ scheduler.production.js:151
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
 🔧 Service role client test successful, user count: 223
 📄 Site content loaded: (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 Fetch failed loading: HEAD "https://fgubaqoftdeefcakejwu.supabase.co/rest/v1/users?select=count".
window.fetch @ live:665
(anonymous) @ supabase-CcLtyElS-1758536206320.js:6
(anonymous) @ supabase-CcLtyElS-1758536206320.js:6
a @ supabase-CcLtyElS-1758536206320.js:6
Promise.then
c @ supabase-CcLtyElS-1758536206320.js:6
(anonymous) @ supabase-CcLtyElS-1758536206320.js:6
Is @ supabase-CcLtyElS-1758536206320.js:6
(anonymous) @ supabase-CcLtyElS-1758536206320.js:6
then @ supabase-CcLtyElS-1758536206320.js:1
ot @ index-Y3uU2Jna-1758536206320.js:46
_$ @ index-Y3uU2Jna-1758536206320.js:761
(anonymous) @ index-Y3uU2Jna-1758536206320.js:792
 No pending email notifications to process
 📊 Current share price: 5
 🔍 Fetching shares for user ID: 335 number
 🔍 Dashboard route guard check: {needsProfileCompletion: false, profile_completion_required: false, currentSection: 'dashboard', userEmail: '<EMAIL>', hasDatabase: true, …}
 ✅ Dashboard access granted - user authenticated
 ✅ Shares data: []
 🔍 Fetching commission balance for user ID: 335 number
 XHR finished loading: POST "https://backenster.com/api/app/config".
send @ jquery-3.4.1.min.js:2
ajax @ jquery-3.4.1.min.js:2
readBackensterParams @ translate.js:1
(anonymous) @ translate.js:1
(anonymous) @ piwik.js:1
 🔍 Testing database connection...
 ✅ Commission balance data: {usdt_balance: 0, share_balance: 0, total_earned_usdt: 0, total_earned_shares: 0, escrowed_amount: 0, …}
 🔍 Fetching referrals for user ID: 335
 ✅ Database connection test successful, found 1 users
 ✅ Referrals data: []
 📊 Share calculation: {purchasedShares: 0, availableShares: 0, totalShares: 0, currentSharePrice: 5}
 ✅ Dashboard data loaded: {totalShares: 0, shareValue: 0, futureDividends: 0, usdtCommissions: {…}, shareCommissions: {…}, …}
UserDashboard.tsx:1667 ✅ loadDashboardData completed
UserDashboard.tsx:1351 🔐 Loading security settings for user: 335
UserDashboard.tsx:1366 ✅ Security settings loaded: {two_factor_enabled: false, email_verified: true}
UserDashboard.tsx:1670 ✅ loadUserSecuritySettings completed
UserDashboard.tsx:2045 🔍 IMMEDIATE FIX: Setting Telegram connection from authenticated user data
UserDashboard.tsx:2054 🔍 DEBUG: User object structure: {hasUser: true, userKeys: Array(7), hasDatabaseUser: true, hasUserMetadata: true, userMetadataKeys: Array(10)}
UserDashboard.tsx:2079 ❌ No Telegram connection found in session
UserDashboard.tsx:1673 ✅ checkTelegramConnection completed
UserDashboard.tsx:1763 🔄 Loading share purchases for user: 335
UserDashboard.tsx:1777 ✅ Share purchases loaded: []
UserDashboard.tsx:1676 ✅ loadUserSharePurchases completed
UserDashboard.tsx:1679 ✅ loadUnreadMessageCount completed
live:665 Fetch failed loading: HEAD "https://fgubaqoftdeefcakejwu.supabase.co/rest/v1/internal_messages?select=*&recipient_user_id=eq.335&is_read=eq.false".
window.fetch @ live:665
(anonymous) @ fetch.js:23
(anonymous) @ fetch.js:44
a @ fetch.js:4
Promise.then
c @ fetch.js:6
(anonymous) @ fetch.js:7
Is @ fetch.js:3
(anonymous) @ fetch.js:34
then @ PostgrestBuilder.js:65
UserDashboard.tsx:2089 🔍 Loading KYC status for user via API: 335
live:21 ❌ Error loading KYC status: SyntaxError: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
console.error @ live:21
console.error @ live:153
overrideMethod @ hook.js:608
console.error @ live:703
console.error @ errorMonitoring.js:124
$n @ UserDashboard.tsx:2111
await in $n
Qn @ UserDashboard.tsx:1681
await in Qn
(anonymous) @ UserDashboard.tsx:993
$m @ react-dom-client.production.js:8292
bS @ react-dom-client.production.js:9771
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9774
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9765
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9765
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9774
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9782
MS @ react-dom-client.production.js:11313
(anonymous) @ react-dom-client.production.js:11048
k @ scheduler.production.js:151
UserDashboard.tsx:1682 ✅ loadKYCStatus completed
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:2757 🔍 UserDashboard overview - Always showing shareholder content
UserDashboard.tsx:2922 🔍 UserDashboard additional content - Always showing shareholder content
NotificationBadge.tsx:32 🔔 Loading notification count for user: 335
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:2757 🔍 UserDashboard overview - Always showing shareholder content
UserDashboard.tsx:2922 🔍 UserDashboard additional content - Always showing shareholder content
live:21 Error getting notification stats: SyntaxError: Unexpected token '<', "
<!DOCTYPE "... is not valid JSON
console.error @ live:21
console.error @ live:153
overrideMethod @ hook.js:608
console.error @ live:703
console.error @ errorMonitoring.js:124
getNotificationStats @ notificationService.ts:420
await in getNotificationStats
c @ NotificationBadge.tsx:35
(anonymous) @ NotificationBadge.tsx:22
$m @ react-dom-client.production.js:8292
bS @ react-dom-client.production.js:9771
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9765
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9774
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9765
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9765
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9774
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9878
Jl @ react-dom-client.production.js:9746
bS @ react-dom-client.production.js:9782
MS @ react-dom-client.production.js:11313
(anonymous) @ react-dom-client.production.js:11048
k @ scheduler.production.js:151
NotificationBadge.tsx:37 ✅ Notification count loaded: 0
svgCertificateGenerator.ts:88 ✅ SVG template loaded successfully from Supabase storage
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:1125 🔄 Updating profile for user: 335
UserDashboard.tsx:1126 📝 Profile data: {full_name: 'Jean Pierre Rademeyer', phone: '0783699799', profile_description: 'This is a test'}
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
UserDashboard.tsx:1145 ✅ Profile updated successfully in database
live:665 Fetch finished loading: PATCH "https://fgubaqoftdeefcakejwu.supabase.co/rest/v1/users?id=eq.335".
window.fetch @ live:665
(anonymous) @ fetch.js:23
(anonymous) @ fetch.js:44
a @ fetch.js:4
Promise.then
c @ fetch.js:6
(anonymous) @ fetch.js:7
Is @ fetch.js:3
(anonymous) @ fetch.js:34
then @ PostgrestBuilder.js:65
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
f429f1d4-aeb5-456b-acd2-b9ca2669c371:7 Refused to connect to 'https://overbridgenet.com/jsv8/offer' because it violates the following Content Security Policy directive: "connect-src 'self' http://localhost:* https://*.supabase.co https://api.stripe.com https://checkout.stripe.com https://cdn.jsdelivr.net wss://*.supabase.co https://www.google-analytics.com https://*.google-analytics.com https://*.googletagmanager.com".

_0x2affa8 @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
yPIMP @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
_0x3e6c6d @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
MALlJ @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
_0x24b884 @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
setInterval
CkGZH @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:10
f429f1d4-aeb5-456b-acd2-b9ca2669c371:7 XHR failed loading: POST "https://overbridgenet.com/jsv8/offer".
_0x2affa8 @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
yPIMP @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
_0x3e6c6d @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
MALlJ @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
_0x24b884 @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
setInterval
CkGZH @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:7
(anonymous) @ f429f1d4-aeb5-456b-acd2-b9ca2669c371:10
UserDashboard.tsx:777 🔍 UserDashboard - Always showing shareholder navigation items
