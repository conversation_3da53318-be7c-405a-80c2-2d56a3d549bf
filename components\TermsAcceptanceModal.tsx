import React, { useState } from 'react';
import { TermsAndConditions } from './TermsAndConditions';
import { PrivacyPolicy } from './PrivacyPolicy';

interface TermsAcceptanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: (acceptedTerms: boolean, acceptedPrivacy: boolean) => void;
  termsType: 'registration' | 'share_purchase' | 'general';
  title?: string;
  showPrivacyPolicy?: boolean;
  requireBoth?: boolean;
  loading?: boolean;
}

export const TermsAcceptanceModal: React.FC<TermsAcceptanceModalProps> = ({
  isOpen,
  onClose,
  onAccept,
  termsType,
  title,
  showPrivacyPolicy = true,
  requireBoth = true,
  loading = false
}) => {
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [acceptedPrivacy, setAcceptedPrivacy] = useState(false);
  const [activeTab, setActiveTab] = useState<'terms' | 'privacy'>('terms');

  if (!isOpen) return null;

  const getTitle = () => {
    if (title) return title;
    
    switch (termsType) {
      case 'registration':
        return 'Accept Terms & Conditions to Complete Registration';
      case 'share_purchase':
        return 'Accept Terms & Conditions to Purchase Shares';
      default:
        return 'Terms & Conditions Acceptance Required';
    }
  };

  const getDescription = () => {
    switch (termsType) {
      case 'registration':
        return 'Please review and accept our Terms & Conditions and Privacy Policy to create your account.';
      case 'share_purchase':
        return 'Please review and accept our Terms & Conditions to proceed with your share purchase.';
      default:
        return 'Please review and accept our Terms & Conditions to continue.';
    }
  };

  const canProceed = requireBoth 
    ? (acceptedTerms && (showPrivacyPolicy ? acceptedPrivacy : true))
    : (acceptedTerms || acceptedPrivacy);

  const handleAccept = () => {
    if (canProceed) {
      onAccept(acceptedTerms, acceptedPrivacy);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl max-w-4xl w-full max-h-[90vh] flex flex-col border border-gray-700">
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold text-gold mb-2">
                {getTitle()}
              </h2>
              <p className="text-gray-300 text-sm">
                {getDescription()}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
              disabled={loading}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Tabs */}
        {showPrivacyPolicy && (
          <div className="flex border-b border-gray-700">
            <button
              onClick={() => setActiveTab('terms')}
              className={`px-6 py-3 font-medium transition-colors ${
                activeTab === 'terms'
                  ? 'text-gold border-b-2 border-gold bg-gray-800'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Terms & Conditions
            </button>
            <button
              onClick={() => setActiveTab('privacy')}
              className={`px-6 py-3 font-medium transition-colors ${
                activeTab === 'privacy'
                  ? 'text-gold border-b-2 border-gold bg-gray-800'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Privacy Policy
            </button>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'terms' ? (
            <TermsAndConditions />
          ) : (
            <PrivacyPolicy />
          )}
        </div>

        {/* Footer with Checkboxes and Actions */}
        <div className="p-6 border-t border-gray-700 bg-gray-800">
          <div className="space-y-4">
            {/* Terms Checkbox */}
            <label className="flex items-start space-x-3 cursor-pointer">
              <input
                type="checkbox"
                checked={acceptedTerms}
                onChange={(e) => setAcceptedTerms(e.target.checked)}
                className="mt-1 w-5 h-5 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold focus:ring-2"
                disabled={loading}
              />
              <span className="text-sm text-gray-300">
                I have read and agree to the{' '}
                <button
                  onClick={() => setActiveTab('terms')}
                  className="text-gold hover:underline"
                >
                  Terms & Conditions
                </button>
                {' '}(Version 2.0)
              </span>
            </label>

            {/* Privacy Policy Checkbox */}
            {showPrivacyPolicy && (
              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={acceptedPrivacy}
                  onChange={(e) => setAcceptedPrivacy(e.target.checked)}
                  className="mt-1 w-5 h-5 text-gold bg-gray-700 border-gray-600 rounded focus:ring-gold focus:ring-2"
                  disabled={loading}
                />
                <span className="text-sm text-gray-300">
                  I have read and agree to the{' '}
                  <button
                    onClick={() => setActiveTab('privacy')}
                    className="text-gold hover:underline"
                  >
                    Privacy Policy
                  </button>
                  {' '}(Version 1.5)
                </span>
              </label>
            )}

            {/* Legal Notice */}
            <div className="bg-amber-900/20 border border-amber-500/30 rounded-lg p-3">
              <p className="text-xs text-amber-200">
                <strong>Legal Notice:</strong> By accepting these terms, you acknowledge that you have read, 
                understood, and agree to be legally bound by all terms and conditions. This constitutes a 
                legally binding agreement under South African law.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-2">
              <button
                onClick={onClose}
                disabled={loading}
                className="flex-1 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                onClick={handleAccept}
                disabled={!canProceed || loading}
                className="flex-2 px-6 py-3 bg-gold text-black font-semibold rounded-lg hover:bg-yellow-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                    Processing...
                  </>
                ) : (
                  'Accept & Continue'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
