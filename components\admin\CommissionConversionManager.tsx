import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface ConversionRequest {
  id: string;
  user_id: number;
  shares_requested: number;
  usdt_amount: number;
  share_price: number;
  phase_id: number;
  phase_number: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  approved_at?: string;
  rejected_at?: string;
  admin_notes?: string;
  approved_by_admin_id?: number;
  users: {
    id: number;
    username: string;
    full_name: string;
    email: string;
  };
}

interface CommissionConversionManagerProps {
  currentUser: any;
}

export const CommissionConversionManager: React.FC<CommissionConversionManagerProps> = ({
  currentUser
}) => {
  const [conversionRequests, setConversionRequests] = useState<ConversionRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [selectedRequest, setSelectedRequest] = useState<ConversionRequest | null>(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');

  useEffect(() => {
    loadConversionRequests();
  }, []);

  const loadConversionRequests = async () => {
    try {
      setLoading(true);
      const serviceClient = getServiceRoleClient();

      const { data, error } = await serviceClient
        .from('commission_conversions')
        .select(`
          *,
          users!inner(id, username, full_name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setConversionRequests(data || []);
    } catch (error) {
      console.error('Error loading conversion requests:', error);
      alert('Failed to load conversion requests');
    } finally {
      setLoading(false);
    }
  };

  const handleActionClick = (request: ConversionRequest, action: 'approve' | 'reject') => {
    setSelectedRequest(request);
    setActionType(action);
    setAdminNotes('');
    setShowModal(true);
  };

  const processConversionRequest = async () => {
    if (!selectedRequest) return;

    setProcessingId(selectedRequest.id);
    try {
      const serviceClient = getServiceRoleClient();

      if (actionType === 'approve') {
        // Approve the conversion
        await approveConversion(selectedRequest, serviceClient);
      } else {
        // Reject the conversion
        await rejectConversion(selectedRequest, serviceClient);
      }

      await loadConversionRequests();
      setShowModal(false);
      setSelectedRequest(null);
      setAdminNotes('');
    } catch (error) {
      console.error('Error processing conversion:', error);
      alert(`Failed to ${actionType} conversion: ${error.message}`);
    } finally {
      setProcessingId(null);
    }
  };

  const approveConversion = async (request: ConversionRequest, serviceClient: any) => {
    // 1. Update conversion status
    const { error: updateError } = await serviceClient
      .from('commission_conversions')
      .update({
        status: 'approved',
        approved_at: new Date().toISOString(),
        approved_by_admin_id: currentUser?.adminUser?.id,
        admin_notes: adminNotes
      })
      .eq('id', request.id);

    if (updateError) throw updateError;

    // 2. Get current commission balance
    const { data: currentBalance, error: balanceError } = await serviceClient
      .from('commission_balances')
      .select('share_balance, total_earned_shares')
      .eq('user_id', request.user_id)
      .single();

    if (balanceError) throw balanceError;

    // 3. Add shares to user's balance
    const newShareBalance = (currentBalance.share_balance || 0) + request.shares_requested;
    const newTotalEarnedShares = (currentBalance.total_earned_shares || 0) + request.shares_requested;

    const { error: updateBalanceError } = await serviceClient
      .from('commission_balances')
      .update({
        share_balance: newShareBalance,
        total_earned_shares: newTotalEarnedShares,
        last_updated: new Date().toISOString()
      })
      .eq('user_id', request.user_id);

    if (updateBalanceError) throw updateBalanceError;

    // 4. Log admin action
    await logAdminAction(
      currentUser?.adminUser?.id || 'unknown',
      'conversion_approved',
      `Approved conversion of $${request.usdt_amount} to ${request.shares_requested} shares for user ${request.user_id}`,
      { 
        conversionId: request.id,
        userId: request.user_id,
        usdtAmount: request.usdt_amount,
        sharesApproved: request.shares_requested,
        adminNotes
      }
    );

    console.log(`✅ Conversion approved: $${request.usdt_amount} → ${request.shares_requested} shares for user ${request.user_id}`);
  };

  const rejectConversion = async (request: ConversionRequest, serviceClient: any) => {
    // 1. Update conversion status
    const { error: updateError } = await serviceClient
      .from('commission_conversions')
      .update({
        status: 'rejected',
        rejected_at: new Date().toISOString(),
        approved_by_admin_id: currentUser?.adminUser?.id,
        admin_notes: adminNotes
      })
      .eq('id', request.id);

    if (updateError) throw updateError;

    // 2. Refund USDT to user's balance
    const { data: currentBalance, error: balanceError } = await serviceClient
      .from('commission_balances')
      .select('usdt_balance')
      .eq('user_id', request.user_id)
      .single();

    if (balanceError) throw balanceError;

    const refundedBalance = (currentBalance.usdt_balance || 0) + request.usdt_amount;

    const { error: refundError } = await serviceClient
      .from('commission_balances')
      .update({
        usdt_balance: refundedBalance,
        last_updated: new Date().toISOString()
      })
      .eq('user_id', request.user_id);

    if (refundError) throw refundError;

    // 3. Log admin action
    await logAdminAction(
      currentUser?.adminUser?.id || 'unknown',
      'conversion_rejected',
      `Rejected conversion of $${request.usdt_amount} for user ${request.user_id} and refunded USDT`,
      { 
        conversionId: request.id,
        userId: request.user_id,
        usdtRefunded: request.usdt_amount,
        adminNotes
      }
    );

    console.log(`❌ Conversion rejected and refunded: $${request.usdt_amount} for user ${request.user_id}`);
  };

  const logAdminAction = async (adminId: string, action: string, description: string, metadata: any) => {
    try {
      const serviceClient = getServiceRoleClient();
      await serviceClient
        .from('admin_audit_log')
        .insert({
          admin_id: adminId,
          action,
          description,
          metadata,
          timestamp: new Date().toISOString()
        });
    } catch (error) {
      console.warn('Failed to log admin action:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-900/20';
      case 'approved': return 'text-green-400 bg-green-900/20';
      case 'rejected': return 'text-red-400 bg-red-900/20';
      default: return 'text-gray-400 bg-gray-900/20';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-white">Loading conversion requests...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Commission Conversion Management</h2>
        <button
          onClick={loadConversionRequests}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Refresh
        </button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-yellow-400">
            {conversionRequests.filter(r => r.status === 'pending').length}
          </div>
          <div className="text-sm text-gray-400">Pending Requests</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-green-400">
            {conversionRequests.filter(r => r.status === 'approved').length}
          </div>
          <div className="text-sm text-gray-400">Approved</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-red-400">
            {conversionRequests.filter(r => r.status === 'rejected').length}
          </div>
          <div className="text-sm text-gray-400">Rejected</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-blue-400">
            ${conversionRequests
              .filter(r => r.status === 'pending')
              .reduce((sum, r) => sum + r.usdt_amount, 0)
              .toFixed(2)}
          </div>
          <div className="text-sm text-gray-400">Pending Value</div>
        </div>
      </div>

      {/* Conversion Requests Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="p-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Conversion Requests</h3>
        </div>

        {conversionRequests.length === 0 ? (
          <div className="p-8 text-center text-gray-400">
            No conversion requests found
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    USDT Amount
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Shares Requested
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Share Price
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {conversionRequests.map((request) => (
                  <tr key={request.id} className="hover:bg-gray-700/50">
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm text-white font-medium">
                        {request.users.username}
                      </div>
                      <div className="text-xs text-gray-400">
                        {request.users.full_name || request.users.email}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-green-400">
                        ${request.usdt_amount.toFixed(2)}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-yellow-400">
                        {request.shares_requested.toLocaleString()}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">
                        ${request.share_price.toFixed(2)}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(request.status)}`}>
                        {request.status.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-400">
                      {formatDate(request.created_at)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                      {request.status === 'pending' ? (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleActionClick(request, 'approve')}
                            disabled={processingId === request.id}
                            className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {processingId === request.id ? 'Processing...' : 'Approve'}
                          </button>
                          <button
                            onClick={() => handleActionClick(request, 'reject')}
                            disabled={processingId === request.id}
                            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Reject
                          </button>
                        </div>
                      ) : (
                        <div className="text-gray-500">
                          {request.status === 'approved' ? 'Approved' : 'Rejected'}
                          {request.approved_at && (
                            <div className="text-xs">
                              {formatDate(request.approved_at)}
                            </div>
                          )}
                          {request.rejected_at && (
                            <div className="text-xs">
                              {formatDate(request.rejected_at)}
                            </div>
                          )}
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      {showModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-4">
              {actionType === 'approve' ? 'Approve' : 'Reject'} Conversion Request
            </h3>

            <div className="space-y-3 mb-4">
              <div className="text-sm text-gray-300">
                <strong>User:</strong> {selectedRequest.users.username} ({selectedRequest.users.full_name})
              </div>
              <div className="text-sm text-gray-300">
                <strong>USDT Amount:</strong> ${selectedRequest.usdt_amount.toFixed(2)}
              </div>
              <div className="text-sm text-gray-300">
                <strong>Shares Requested:</strong> {selectedRequest.shares_requested.toLocaleString()}
              </div>
              <div className="text-sm text-gray-300">
                <strong>Share Price:</strong> ${selectedRequest.share_price.toFixed(2)}
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Admin Notes {actionType === 'reject' ? '(Required)' : '(Optional)'}
              </label>
              <textarea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder={`Enter notes for ${actionType}ing this conversion...`}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={processConversionRequest}
                disabled={actionType === 'reject' && !adminNotes.trim()}
                className={`px-4 py-2 rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed ${
                  actionType === 'approve'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-red-600 hover:bg-red-700'
                }`}
              >
                {actionType === 'approve' ? 'Approve' : 'Reject'} Conversion
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
