/**
 * RESEND EMAIL SERVICE
 * 
 * Comprehensive email service using Resend API for:
 * - Email verification codes
 * - Account security notifications
 * - Newsletter and bulk email functionality
 * - Password reset emails
 * - Account change confirmations
 */

import { Resend } from 'resend';
import { supabase } from './supabase';
import { emailPreferencesService } from './emailPreferencesService';

// Environment configuration
const RESEND_API_KEY = import.meta.env.VITE_RESEND_API_KEY || process.env.RESEND_API_KEY;
const RESEND_FROM_EMAIL = import.meta.env.VITE_RESEND_FROM_EMAIL || process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = import.meta.env.VITE_RESEND_FROM_NAME || process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';

// Initialize Resend client
let resend: Resend | null = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Resend email service initialized');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - email service disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize Resend service:', error);
}

export interface EmailVerificationData {
  email: string;
  code: string;
  purpose: 'registration' | 'account_update' | 'withdrawal' | 'password_reset' | 'telegram_connection';
  userName?: string;
  expiryMinutes?: number;
}

export interface BulkEmailData {
  recipients: string[];
  subject: string;
  htmlContent: string;
  textContent?: string;
  templateData?: Record<string, any>;
}

export interface WelcomeEmailData {
  email: string;
  fullName: string;
  username: string;
  sponsorUsername?: string;
}

export interface ConversionNotificationData {
  email: string;
  fullName: string;
  usdtAmount: number;
  sharesReceived: number;
  sharePrice: number;
  transactionId: string;
}

export interface SharePurchaseConfirmationData {
  email: string;
  fullName: string;
  sharesPurchased: number;
  totalAmount: number;
  sharePrice: number;
  phaseName: string;
  transactionId: string;
}

export interface CommissionEarnedData {
  email: string;
  fullName: string;
  usdtCommission: number;
  shareCommission: number;
  referredUserName: string;
  purchaseAmount: number;
  transactionId: string;
}

export interface WithdrawalNotificationData {
  email: string;
  fullName: string;
  withdrawalAmount: number;
  currency: string;
  status: 'requested' | 'approved' | 'completed' | 'rejected';
  walletAddress?: string;
  transactionId: string;
}

export interface ShareTransferNotificationData {
  email: string;
  fullName: string;
  type: 'sender' | 'recipient';
  shares: number;
  transferFee: number;
  senderName?: string;
  recipientName?: string;
  transactionId: string;
}

export interface NewReferralNotificationData {
  email: string;
  fullName: string;
  newUserName: string;
  newUserEmail: string;
  registrationDate: string;
  referralLink?: string;
}

export interface MigrationConfirmationData {
  email: string;
  fullName: string;
  username: string;
  telegramUsername: string;
}

export interface EmailDeliveryResult {
  success: boolean;
  messageId?: string;
  error?: string;
  deliveryStatus?: 'sent' | 'delivered' | 'bounced' | 'failed';
}

export interface NewsletterSubscription {
  email: string;
  categories: string[];
  preferences: Record<string, any>;
  unsubscribeToken: string;
}

class ResendEmailService {
  private isConfigured(): boolean {
    return resend !== null && !!RESEND_API_KEY;
  }

  /**
   * Send email verification code
   */
  async sendVerificationCode(data: EmailVerificationData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured()) {
      console.error('❌ Resend service not configured');
      return { success: false, error: 'Email service not configured' };
    }

    try {
      const { email, code, purpose, userName, expiryMinutes = 15 } = data;

      // Get user ID for preference checking
      const { data: userData } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single();

      const userId = userData?.id;

      // Generate email content based on purpose
      const emailContent = this.generateVerificationEmailContent(code, purpose, userName, expiryMinutes);

      const result = await resend!.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'verification' },
          { name: 'purpose', value: purpose }
        ]
      });

      if (result.error) {
        console.error('❌ Resend API error:', result.error);
        return { success: false, error: result.error.message };
      }

      // Log email delivery
      await this.logEmailDelivery({
        email,
        emailType: `verification_${purpose}`,
        resendMessageId: result.data?.id,
        status: 'sent',
        subject: emailContent.subject,
        userId
      });

      console.log(`✅ Verification email sent to ${email} (${purpose})`);
      return { 
        success: true, 
        messageId: result.data?.id,
        deliveryStatus: 'sent'
      };

    } catch (error) {
      console.error('❌ Error sending verification email:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(email: string, resetToken: string, userName?: string): Promise<EmailDeliveryResult> {
    if (!this.isConfigured()) {
      return { success: false, error: 'Email service not configured' };
    }

    try {
      const resetLink = `${window.location.origin}/reset-password?token=${resetToken}`;
      const emailContent = this.generatePasswordResetEmailContent(resetLink, userName);

      const result = await resend!.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'password_reset' }
        ]
      });

      if (result.error) {
        return { success: false, error: result.error.message };
      }

      await this.logEmailDelivery({
        email,
        emailType: 'password_reset',
        resendMessageId: result.data?.id,
        status: 'sent',
        subject: emailContent.subject
      });

      return { success: true, messageId: result.data?.id };

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Send account change notification
   */
  async sendAccountChangeNotification(
    email: string, 
    changes: Record<string, any>, 
    userName?: string
  ): Promise<EmailDeliveryResult> {
    if (!this.isConfigured()) {
      return { success: false, error: 'Email service not configured' };
    }

    try {
      const emailContent = this.generateAccountChangeEmailContent(changes, userName);

      const result = await resend!.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        tags: [
          { name: 'category', value: 'account_change' }
        ]
      });

      if (result.error) {
        return { success: false, error: result.error.message };
      }

      await this.logEmailDelivery({
        email,
        emailType: 'account_change',
        resendMessageId: result.data?.id,
        status: 'sent',
        subject: emailContent.subject
      });

      return { success: true, messageId: result.data?.id };

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Send bulk email (newsletter/announcements)
   */
  async sendBulkEmail(data: BulkEmailData): Promise<EmailDeliveryResult[]> {
    if (!this.isConfigured()) {
      return [{ success: false, error: 'Email service not configured' }];
    }

    const results: EmailDeliveryResult[] = [];
    const { recipients, subject, htmlContent, textContent } = data;

    // Send in batches to avoid rate limits
    const batchSize = 50;
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);

      try {
        const result = await resend!.emails.send({
          from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
          to: batch,
          subject,
          html: htmlContent,
          text: textContent || this.stripHtml(htmlContent),
          tags: [
            { name: 'category', value: 'bulk_email' },
            { name: 'batch', value: `${Math.floor(i / batchSize) + 1}` }
          ]
        });

        if (result.error) {
          results.push({ success: false, error: result.error.message });
        } else {
          results.push({ success: true, messageId: result.data?.id });

          // Log bulk email delivery
          for (const email of batch) {
            await this.logEmailDelivery({
              email,
              emailType: 'bulk_email',
              resendMessageId: result.data?.id,
              status: 'sent',
              subject
            });
          }
        }

        // Rate limiting delay
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        results.push({ 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    return results;
  }

  /**
   * Generate verification email content
   */
  private generateVerificationEmailContent(
    code: string, 
    purpose: string, 
    userName?: string,
    expiryMinutes: number = 15
  ) {
    const greeting = userName ? `Hello ${userName}` : 'Hello';
    const purposeText = {
      registration: 'complete your account registration',
      account_update: 'confirm your account changes',
      withdrawal: 'authorize your withdrawal request',
      password_reset: 'reset your password',
      telegram_connection: 'connect your Telegram account'
    }[purpose] || 'verify your email';

    const subject = `Your Aureus Alliance verification code: ${code}`;

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Email Verification</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #D4AF37;">Aureus Alliance Holdings</h1>
            </div>
            
            <h2>Email Verification Required</h2>
            
            <p>${greeting},</p>
            
            <p>You need to verify your email address to ${purposeText}.</p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;">
              <h3 style="margin: 0; color: #D4AF37;">Your Verification Code</h3>
              <div style="font-size: 32px; font-weight: bold; letter-spacing: 8px; margin: 15px 0; color: #333;">
                ${code}
              </div>
              <p style="margin: 0; color: #666; font-size: 14px;">
                This code expires in ${expiryMinutes} minutes
              </p>
            </div>
            
            <p><strong>Security Notice:</strong></p>
            <ul>
              <li>Never share this code with anyone</li>
              <li>Aureus Alliance will never ask for this code via phone or email</li>
              <li>If you didn't request this verification, please ignore this email</li>
            </ul>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            
            <p style="font-size: 12px; color: #666; text-align: center;">
              This email was sent by Aureus Alliance Holdings. If you have questions, 
              please contact our support team.
            </p>
          </div>
        </body>
      </html>
    `;

    const text = `
      Aureus Alliance Holdings - Email Verification
      
      ${greeting},
      
      You need to verify your email address to ${purposeText}.
      
      Your verification code: ${code}
      
      This code expires in ${expiryMinutes} minutes.
      
      Security Notice:
      - Never share this code with anyone
      - Aureus Alliance will never ask for this code via phone or email
      - If you didn't request this verification, please ignore this email
      
      This email was sent by Aureus Alliance Holdings.
    `;

    return { subject, html, text };
  }

  /**
   * Generate password reset email content
   */
  private generatePasswordResetEmailContent(resetLink: string, userName?: string) {
    const greeting = userName ? `Hello ${userName}` : 'Hello';
    
    const subject = 'Reset your Aureus Alliance password';

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Password Reset</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #D4AF37;">Aureus Alliance Holdings</h1>
            </div>
            
            <h2>Password Reset Request</h2>
            
            <p>${greeting},</p>
            
            <p>We received a request to reset your password. Click the button below to create a new password:</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" 
                 style="background: #D4AF37; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                Reset Password
              </a>
            </div>
            
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666;">${resetLink}</p>
            
            <p><strong>Security Notice:</strong></p>
            <ul>
              <li>This link expires in 24 hours</li>
              <li>If you didn't request this reset, please ignore this email</li>
              <li>Your password will not change until you create a new one</li>
            </ul>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            
            <p style="font-size: 12px; color: #666; text-align: center;">
              This email was sent by Aureus Alliance Holdings. If you have questions, 
              please contact our support team.
            </p>
          </div>
        </body>
      </html>
    `;

    const text = `
      Aureus Alliance Holdings - Password Reset
      
      ${greeting},
      
      We received a request to reset your password. 
      
      Reset your password by visiting this link: ${resetLink}
      
      Security Notice:
      - This link expires in 24 hours
      - If you didn't request this reset, please ignore this email
      - Your password will not change until you create a new one
      
      This email was sent by Aureus Alliance Holdings.
    `;

    return { subject, html, text };
  }

  /**
   * Generate account change notification content
   */
  private generateAccountChangeEmailContent(changes: Record<string, any>, userName?: string) {
    const greeting = userName ? `Hello ${userName}` : 'Hello';
    const changesList = Object.entries(changes)
      .map(([field, value]) => `<li><strong>${field}:</strong> ${value}</li>`)
      .join('');

    const subject = 'Your Aureus Alliance account has been updated';

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Account Update Notification</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #D4AF37;">Aureus Alliance Holdings</h1>
            </div>
            
            <h2>Account Update Notification</h2>
            
            <p>${greeting},</p>
            
            <p>Your account information has been successfully updated. Here are the changes:</p>
            
            <ul style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
              ${changesList}
            </ul>
            
            <p><strong>Security Notice:</strong></p>
            <ul>
              <li>If you didn't make these changes, please contact support immediately</li>
              <li>Review your account regularly for unauthorized changes</li>
              <li>Keep your login credentials secure</li>
            </ul>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            
            <p style="font-size: 12px; color: #666; text-align: center;">
              This email was sent by Aureus Alliance Holdings. If you have questions, 
              please contact our support team.
            </p>
          </div>
        </body>
      </html>
    `;

    const text = `
      Aureus Alliance Holdings - Account Update
      
      ${greeting},
      
      Your account information has been successfully updated.
      
      Changes made:
      ${Object.entries(changes).map(([field, value]) => `- ${field}: ${value}`).join('\n')}
      
      Security Notice:
      - If you didn't make these changes, please contact support immediately
      - Review your account regularly for unauthorized changes
      - Keep your login credentials secure
      
      This email was sent by Aureus Alliance Holdings.
    `;

    return { subject, html, text };
  }

  /**
   * Log email delivery to database (using existing email_delivery_log table)
   */
  private async logEmailDelivery(data: {
    email: string;
    emailType: string;
    resendMessageId?: string;
    status: string;
    userId?: number;
    subject?: string;
  }) {
    try {
      await supabase
        .from('email_delivery_log')
        .insert({
          user_id: data.userId,
          email_address: data.email, // Note: existing table uses 'email_address'
          email_type: data.emailType,
          resend_message_id: data.resendMessageId,
          delivery_status: data.status, // Note: existing table uses 'delivery_status'
          subject: data.subject,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging email delivery:', error);
    }
  }

  /**
   * Send welcome email to new users
   */
  async sendWelcomeEmail(data: WelcomeEmailData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured) {
      console.warn('⚠️ Resend not configured - welcome email not sent');
      return {
        success: false,
        messageId: null,
        error: 'Email service not configured'
      };
    }

    try {
      const subject = `Welcome to Aureus Alliance Holdings, ${data.fullName}!`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png" alt="Aureus Alliance Holdings" style="height: 60px;">
            <h1 style="color: #d4af37; margin: 20px 0;">Welcome to Aureus Alliance Holdings!</h1>
          </div>

          <div style="background-color: #2a2a2a; padding: 25px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #d4af37; margin-top: 0;">Hello ${data.fullName},</h2>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              Welcome to Aureus Alliance Holdings! Your account has been successfully created with username: <strong style="color: #d4af37;">${data.username}</strong>
            </p>

            ${data.sponsorUsername ? `
            <p style="line-height: 1.6; margin-bottom: 15px;">
              You've been sponsored by: <strong style="color: #d4af37;">${data.sponsorUsername}</strong>
            </p>
            ` : ''}

            <p style="line-height: 1.6; margin-bottom: 15px;">
              You can now access your dashboard to:
            </p>
            <ul style="line-height: 1.8; margin-bottom: 20px; padding-left: 20px;">
              <li>Purchase shares in our gold mining operations</li>
              <li>Complete your KYC verification</li>
              <li>Access your share certificates</li>
              <li>Track your dividend projections</li>
              <li>View operational updates and gallery</li>
            </ul>

            <div style="text-align: center; margin: 25px 0;">
              <a href="https://aureus.africa/dashboard" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Access Your Dashboard
              </a>
            </div>
          </div>

          <div style="text-align: center; font-size: 14px; color: #888888; margin-top: 30px;">
            <p>Real Gold • Real Shares • Real Ownership</p>
            <p>Aureus Alliance Holdings (Pty) Ltd</p>
            <p>Building Gold-Backed Impact Ventures Across Africa</p>
          </div>
        </div>
      `;

      const textContent = `
Welcome to Aureus Alliance Holdings!

Hello ${data.fullName},

Your account has been successfully created with username: ${data.username}
${data.sponsorUsername ? `You've been sponsored by: ${data.sponsorUsername}` : ''}

You can now access your dashboard to:
- Purchase shares in our gold mining operations
- Complete your KYC verification
- Access your share certificates
- Track your dividend projections
- View operational updates and gallery

Access your dashboard: https://aureus.africa/dashboard

Real Gold • Real Shares • Real Ownership
Aureus Alliance Holdings (Pty) Ltd
Building Gold-Backed Impact Ventures Across Africa
      `.trim();

      const result = await this.sendEmail({
        to: data.email,
        subject,
        htmlContent,
        textContent,
        emailType: 'welcome'
      });

      console.log('✅ Welcome email sent successfully to:', data.email);
      return result;

    } catch (error) {
      console.error('❌ Error sending welcome email:', error);
      return {
        success: false,
        messageId: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send share purchase confirmation email
   */
  async sendSharePurchaseConfirmation(data: SharePurchaseConfirmationData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured) {
      console.warn('⚠️ Resend not configured - share purchase confirmation not sent');
      return {
        success: false,
        messageId: null,
        error: 'Email service not configured'
      };
    }

    try {
      const subject = `Share Purchase Confirmed - ${data.sharesPurchased} Shares Purchased`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png" alt="Aureus Alliance Holdings" style="height: 60px;">
            <h1 style="color: #d4af37; margin: 20px 0;">Share Purchase Confirmed!</h1>
          </div>

          <div style="background-color: #2a2a2a; padding: 25px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #d4af37; margin-top: 0;">Congratulations ${data.fullName}!</h2>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              Your share purchase has been successfully processed and confirmed. Welcome to the Aureus Alliance Holdings family!
            </p>

            <div style="background-color: #1a1a1a; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #d4af37; margin-top: 0; margin-bottom: 15px;">Purchase Details</h3>
              <table style="width: 100%; color: #ffffff;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Investment Phase:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold;">${data.phaseName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Shares Purchased:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold; color: #d4af37;">${data.sharesPurchased}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Share Price:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right;">$${data.sharePrice.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Total Investment:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold;">$${data.totalAmount.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;">Transaction ID:</td>
                  <td style="padding: 8px 0; text-align: right; font-family: monospace; font-size: 12px;">${data.transactionId}</td>
                </tr>
              </table>
            </div>

            <p style="line-height: 1.6; margin-bottom: 15px;">
              Your shares are now active in your portfolio and will be eligible for future dividend distributions when mining operations commence.
            </p>

            <div style="background-color: #0f3460; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #d4af37;">
              <h4 style="color: #d4af37; margin-top: 0; margin-bottom: 10px;">What's Next?</h4>
              <ul style="margin: 0; padding-left: 20px; color: #ffffff;">
                <li style="margin-bottom: 8px;">View your shares in your dashboard portfolio</li>
                <li style="margin-bottom: 8px;">Download your share certificate (available after KYC completion)</li>
                <li style="margin-bottom: 8px;">Track mining progress and dividend projections</li>
                <li style="margin-bottom: 8px;">Refer friends to earn 15% USDT + 15% share commissions</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 25px 0;">
              <a href="https://aureus.africa/dashboard" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;">
                View Dashboard
              </a>
              <a href="https://aureus.africa/dashboard#portfolio" style="background-color: #0f3460; color: #ffffff; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                View Portfolio
              </a>
            </div>
          </div>

          <div style="text-align: center; font-size: 14px; color: #888888; margin-top: 30px;">
            <p>Real Gold • Real Shares • Real Ownership</p>
            <p>Aureus Alliance Holdings (Pty) Ltd</p>
            <p>Building Gold-Backed Impact Ventures Across Africa</p>
          </div>
        </div>
      `;

      const textContent = `
Share Purchase Confirmed!

Congratulations ${data.fullName}!

Your share purchase has been successfully processed and confirmed. Welcome to the Aureus Alliance Holdings family!

Purchase Details:
- Investment Phase: ${data.phaseName}
- Shares Purchased: ${data.sharesPurchased}
- Share Price: $${data.sharePrice.toFixed(2)}
- Total Investment: $${data.totalAmount.toFixed(2)}
- Transaction ID: ${data.transactionId}

Your shares are now active in your portfolio and will be eligible for future dividend distributions when mining operations commence.

What's Next?
- View your shares in your dashboard portfolio
- Download your share certificate (available after KYC completion)
- Track mining progress and dividend projections
- Refer friends to earn 15% USDT + 15% share commissions

View your dashboard: https://aureus.africa/dashboard

Real Gold • Real Shares • Real Ownership
Aureus Alliance Holdings (Pty) Ltd
Building Gold-Backed Impact Ventures Across Africa
      `.trim();

      const result = await this.sendEmail({
        to: data.email,
        subject,
        htmlContent,
        textContent,
        emailType: 'share_purchase'
      });

      console.log('✅ Share purchase confirmation sent successfully to:', data.email);
      return result;

    } catch (error) {
      console.error('❌ Error sending share purchase confirmation:', error);
      return {
        success: false,
        messageId: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send USDT to Shares conversion notification
   */
  async sendConversionNotification(data: ConversionNotificationData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured) {
      console.warn('⚠️ Resend not configured - conversion notification not sent');
      return {
        success: false,
        messageId: null,
        error: 'Email service not configured'
      };
    }

    try {
      const subject = `USDT to Shares Conversion Completed - ${data.sharesReceived} Shares Received`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png" alt="Aureus Alliance Holdings" style="height: 60px;">
            <h1 style="color: #d4af37; margin: 20px 0;">Conversion Completed Successfully!</h1>
          </div>

          <div style="background-color: #2a2a2a; padding: 25px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #d4af37; margin-top: 0;">Hello ${data.fullName},</h2>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              Your USDT to Shares conversion has been completed successfully!
            </p>

            <div style="background-color: #1a1a1a; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #d4af37; margin-top: 0; margin-bottom: 15px;">Conversion Details</h3>
              <table style="width: 100%; color: #ffffff;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">USDT Converted:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold;">$${data.usdtAmount.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Share Price:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right;">$${data.sharePrice.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Shares Received:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold; color: #d4af37;">${data.sharesReceived}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;">Transaction ID:</td>
                  <td style="padding: 8px 0; text-align: right; font-family: monospace; font-size: 12px;">${data.transactionId}</td>
                </tr>
              </table>
            </div>

            <p style="line-height: 1.6; margin-bottom: 15px;">
              Your new shares have been added to your commission balance and are now available in your dashboard.
            </p>

            <div style="text-align: center; margin: 25px 0;">
              <a href="https://aureus.africa/dashboard" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                View Your Dashboard
              </a>
            </div>
          </div>

          <div style="text-align: center; font-size: 14px; color: #888888; margin-top: 30px;">
            <p>Real Gold • Real Shares • Real Ownership</p>
            <p>Aureus Alliance Holdings (Pty) Ltd</p>
            <p>Building Gold-Backed Impact Ventures Across Africa</p>
          </div>
        </div>
      `;

      const textContent = `
USDT to Shares Conversion Completed

Hello ${data.fullName},

Your USDT to Shares conversion has been completed successfully!

Conversion Details:
- USDT Converted: $${data.usdtAmount.toFixed(2)}
- Share Price: $${data.sharePrice.toFixed(2)}
- Shares Received: ${data.sharesReceived}
- Transaction ID: ${data.transactionId}

Your new shares have been added to your commission balance and are now available in your dashboard.

View your dashboard: https://aureus.africa/dashboard

Real Gold • Real Shares • Real Ownership
Aureus Alliance Holdings (Pty) Ltd
Building Gold-Backed Impact Ventures Across Africa
      `.trim();

      const result = await this.sendEmail({
        to: data.email,
        subject,
        htmlContent,
        textContent,
        emailType: 'conversion'
      });

      console.log('✅ Conversion notification sent successfully to:', data.email);
      return result;

    } catch (error) {
      console.error('❌ Error sending conversion notification:', error);
      return {
        success: false,
        messageId: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send commission earned notification
   */
  async sendCommissionEarnedNotification(data: CommissionEarnedData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured) {
      console.warn('⚠️ Resend not configured - commission earned notification not sent');
      return {
        success: false,
        messageId: null,
        error: 'Email service not configured'
      };
    }

    try {
      const totalCommissionValue = data.usdtCommission + (data.shareCommission * 5); // Estimate share value
      const subject = `Commission Earned - $${data.usdtCommission.toFixed(2)} USDT + ${data.shareCommission} Shares`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png" alt="Aureus Alliance Holdings" style="height: 60px;">
            <h1 style="color: #d4af37; margin: 20px 0;">Commission Earned! 🎉</h1>
          </div>

          <div style="background-color: #2a2a2a; padding: 25px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #d4af37; margin-top: 0;">Great news, ${data.fullName}!</h2>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              You've earned a commission from a successful referral! ${data.referredUserName} just purchased shares, and you've received your 15% USDT + 15% share commission.
            </p>

            <div style="background-color: #1a1a1a; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #d4af37; margin-top: 0; margin-bottom: 15px;">Commission Details</h3>
              <table style="width: 100%; color: #ffffff;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Referred User:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold;">${data.referredUserName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Purchase Amount:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right;">$${data.purchaseAmount.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">USDT Commission (15%):</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold; color: #3b82f6;">$${data.usdtCommission.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Share Commission (15%):</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold; color: #d4af37;">${data.shareCommission} shares</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Total Commission Value:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold; color: #10b981;">≈$${totalCommissionValue.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;">Transaction ID:</td>
                  <td style="padding: 8px 0; text-align: right; font-family: monospace; font-size: 12px;">${data.transactionId}</td>
                </tr>
              </table>
            </div>

            <div style="background-color: #0f3460; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #d4af37;">
              <h4 style="color: #d4af37; margin-top: 0; margin-bottom: 10px;">Your Commission Benefits</h4>
              <ul style="margin: 0; padding-left: 20px; color: #ffffff;">
                <li style="margin-bottom: 8px;"><strong>USDT Commission:</strong> Available for withdrawal or conversion to shares</li>
                <li style="margin-bottom: 8px;"><strong>Share Commission:</strong> Eligible for future dividend distributions</li>
                <li style="margin-bottom: 8px;"><strong>Compound Growth:</strong> Convert USDT to more shares for higher dividends</li>
                <li style="margin-bottom: 8px;"><strong>Passive Income:</strong> Shares generate dividends from mining operations</li>
              </ul>
            </div>

            <p style="line-height: 1.6; margin-bottom: 15px;">
              Keep referring friends and family to build your passive income stream. Every successful referral earns you 15% USDT + 15% shares!
            </p>

            <div style="text-align: center; margin: 25px 0;">
              <a href="https://aureus.africa/affiliate-dashboard#commissions" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;">
                View Commissions
              </a>
              <a href="https://aureus.africa/affiliate-dashboard#referrals" style="background-color: #0f3460; color: #ffffff; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Get Referral Link
              </a>
            </div>
          </div>

          <div style="text-align: center; font-size: 14px; color: #888888; margin-top: 30px;">
            <p>Real Gold • Real Shares • Real Ownership</p>
            <p>Aureus Alliance Holdings (Pty) Ltd</p>
            <p>Building Gold-Backed Impact Ventures Across Africa</p>
          </div>
        </div>
      `;

      const textContent = `
Commission Earned! 🎉

Great news, ${data.fullName}!

You've earned a commission from a successful referral! ${data.referredUserName} just purchased shares, and you've received your 15% USDT + 15% share commission.

Commission Details:
- Referred User: ${data.referredUserName}
- Purchase Amount: $${data.purchaseAmount.toFixed(2)}
- USDT Commission (15%): $${data.usdtCommission.toFixed(2)}
- Share Commission (15%): ${data.shareCommission} shares
- Total Commission Value: ≈$${totalCommissionValue.toFixed(2)}
- Transaction ID: ${data.transactionId}

Your Commission Benefits:
- USDT Commission: Available for withdrawal or conversion to shares
- Share Commission: Eligible for future dividend distributions
- Compound Growth: Convert USDT to more shares for higher dividends
- Passive Income: Shares generate dividends from mining operations

Keep referring friends and family to build your passive income stream. Every successful referral earns you 15% USDT + 15% shares!

View your commissions: https://aureus.africa/affiliate-dashboard#commissions

Real Gold • Real Shares • Real Ownership
Aureus Alliance Holdings (Pty) Ltd
Building Gold-Backed Impact Ventures Across Africa
      `.trim();

      const result = await this.sendEmail({
        to: data.email,
        subject,
        htmlContent,
        textContent,
        emailType: 'commission_earned'
      });

      console.log('✅ Commission earned notification sent successfully to:', data.email);
      return result;

    } catch (error) {
      console.error('❌ Error sending commission earned notification:', error);
      return {
        success: false,
        messageId: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send share transfer notification
   */
  async sendShareTransferNotification(data: ShareTransferNotificationData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured) {
      console.warn('⚠️ Resend not configured - share transfer notification not sent');
      return {
        success: false,
        messageId: null,
        error: 'Email service not configured'
      };
    }

    try {
      const isSender = data.type === 'sender';
      const subject = isSender
        ? `Share Transfer Sent - ${data.shares} Shares Transferred`
        : `Share Transfer Received - ${data.shares} Shares Received`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png" alt="Aureus Alliance Holdings" style="height: 60px;">
            <h1 style="color: #d4af37; margin: 20px 0;">
              ${isSender ? 'Share Transfer Sent' : 'Share Transfer Received'} 🔄
            </h1>
          </div>

          <div style="background-color: #2a2a2a; padding: 25px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #d4af37; margin-top: 0;">Hello ${data.fullName}!</h2>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              ${isSender
                ? `You have successfully transferred ${data.shares} shares to ${data.recipientName}.`
                : `You have received ${data.shares} shares from ${data.senderName}.`
              }
            </p>

            <div style="background-color: #1a1a1a; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #d4af37; margin-top: 0; margin-bottom: 15px;">Transfer Details</h3>
              <table style="width: 100%; color: #ffffff;">
                ${isSender ? `
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Recipient:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold;">${data.recipientName}</td>
                </tr>
                ` : `
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Sender:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold;">${data.senderName}</td>
                </tr>
                `}
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Shares ${isSender ? 'Transferred' : 'Received'}:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold; color: #d4af37;">${data.shares}</td>
                </tr>
                ${isSender && data.transferFee > 0 ? `
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Transfer Fee (2%):</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; color: #f59e0b;">${data.transferFee}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Total Deducted:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold; color: #ef4444;">${data.shares + data.transferFee}</td>
                </tr>
                ` : ''}
                <tr>
                  <td style="padding: 8px 0;">Transaction ID:</td>
                  <td style="padding: 8px 0; text-align: right; font-family: monospace; font-size: 12px;">${data.transactionId}</td>
                </tr>
              </table>
            </div>

            <div style="background-color: #0f3460; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #d4af37;">
              <h4 style="color: #d4af37; margin-top: 0; margin-bottom: 10px;">
                ${isSender ? 'Transfer Completed' : 'Shares Added to Your Portfolio'}
              </h4>
              <p style="margin: 0; color: #ffffff; line-height: 1.5;">
                ${isSender
                  ? `Your shares have been successfully transferred. The recipient has been notified and the shares are now in their portfolio.`
                  : `The transferred shares have been added to your portfolio and are now eligible for future dividend distributions.`
                }
              </p>
            </div>

            <div style="text-align: center; margin: 25px 0;">
              <a href="https://aureus.africa/affiliate-dashboard#commissions" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                View Your Portfolio
              </a>
            </div>
          </div>

          <div style="text-align: center; font-size: 14px; color: #888888; margin-top: 30px;">
            <p>Real Gold • Real Shares • Real Ownership</p>
            <p>Aureus Alliance Holdings (Pty) Ltd</p>
            <p>Building Gold-Backed Impact Ventures Across Africa</p>
          </div>
        </div>
      `;

      const textContent = `
${isSender ? 'Share Transfer Sent' : 'Share Transfer Received'}

Hello ${data.fullName}!

${isSender
  ? `You have successfully transferred ${data.shares} shares to ${data.recipientName}.`
  : `You have received ${data.shares} shares from ${data.senderName}.`
}

Transfer Details:
${isSender ? `- Recipient: ${data.recipientName}` : `- Sender: ${data.senderName}`}
- Shares ${isSender ? 'Transferred' : 'Received'}: ${data.shares}
${isSender && data.transferFee > 0 ? `- Transfer Fee (2%): ${data.transferFee}\n- Total Deducted: ${data.shares + data.transferFee}` : ''}
- Transaction ID: ${data.transactionId}

${isSender
  ? 'Your shares have been successfully transferred. The recipient has been notified and the shares are now in their portfolio.'
  : 'The transferred shares have been added to your portfolio and are now eligible for future dividend distributions.'
}

View your portfolio: https://aureus.africa/affiliate-dashboard#commissions

Real Gold • Real Shares • Real Ownership
Aureus Alliance Holdings (Pty) Ltd
Building Gold-Backed Impact Ventures Across Africa
      `.trim();

      const result = await this.sendEmail({
        to: data.email,
        subject,
        htmlContent,
        textContent,
        emailType: 'share_transfer'
      });

      console.log('✅ Share transfer notification sent successfully to:', data.email);
      return result;

    } catch (error) {
      console.error('❌ Error sending share transfer notification:', error);
      return {
        success: false,
        messageId: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send migration confirmation email
   */
  async sendMigrationConfirmationEmail(data: MigrationConfirmationData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured) {
      console.warn('⚠️ Resend not configured - migration confirmation not sent');
      return {
        success: false,
        messageId: null,
        error: 'Email service not configured'
      };
    }

    try {
      const subject = `Account Migration Completed - Welcome to Aureus Alliance Holdings Website!`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png" alt="Aureus Alliance Holdings" style="height: 60px;">
            <h1 style="color: #d4af37; margin: 20px 0;">Account Migration Successful! 🎉</h1>
          </div>

          <div style="background-color: #2a2a2a; padding: 25px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #d4af37; margin-top: 0;">Welcome to the Website, ${data.fullName}!</h2>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              Your Telegram account has been successfully migrated to website access! You can now access your Aureus Alliance Holdings account through both Telegram and our website.
            </p>

            <div style="background-color: #1a1a1a; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #d4af37; margin-top: 0; margin-bottom: 15px;">Your Account Details</h3>
              <table style="width: 100%; color: #ffffff;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Website Username:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold;">${data.username}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Telegram Username:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right;">${data.telegramUsername}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Email Address:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right;">${data.email}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;">Account Status:</td>
                  <td style="padding: 8px 0; text-align: right; color: #10b981; font-weight: bold;">✅ Fully Migrated</td>
                </tr>
              </table>
            </div>

            <div style="background-color: #0f3460; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #d4af37;">
              <h4 style="color: #d4af37; margin-top: 0; margin-bottom: 10px;">What's Available Now</h4>
              <ul style="margin: 0; padding-left: 20px; color: #ffffff;">
                <li style="margin-bottom: 8px;"><strong>Website Access:</strong> Login with your username and password</li>
                <li style="margin-bottom: 8px;"><strong>Telegram Bot:</strong> Continue using the bot as before</li>
                <li style="margin-bottom: 8px;"><strong>Commission Management:</strong> View and withdraw commissions on the website</li>
                <li style="margin-bottom: 8px;"><strong>Share Purchases:</strong> Buy shares through the website interface</li>
                <li style="margin-bottom: 8px;"><strong>Portfolio Tracking:</strong> Monitor your investments and dividends</li>
                <li style="margin-bottom: 8px;"><strong>KYC Verification:</strong> Complete verification for withdrawals</li>
              </ul>
            </div>

            <div style="background-color: #065f46; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981;">
              <h4 style="color: #10b981; margin-top: 0; margin-bottom: 10px;">Your Data is Safe</h4>
              <p style="margin: 0; color: #ffffff; line-height: 1.5;">
                All your existing commission balances, referral relationships, and transaction history have been preserved and are now accessible through both platforms.
              </p>
            </div>

            <div style="text-align: center; margin: 25px 0;">
              <a href="https://aureus.africa/login" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;">
                Login to Website
              </a>
              <a href="https://aureus.africa/dashboard" style="background-color: #0f3460; color: #ffffff; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                View Dashboard
              </a>
            </div>
          </div>

          <div style="background-color: #2a2a2a; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="color: #d4af37; margin-top: 0;">Need Help?</h3>
            <p style="color: #ffffff; margin-bottom: 10px;">
              If you have any questions about your migrated account or need assistance:
            </p>
            <ul style="color: #ffffff; margin: 0; padding-left: 20px;">
              <li style="margin-bottom: 5px;">Contact our support team</li>
              <li style="margin-bottom: 5px;">Visit our help center</li>
              <li style="margin-bottom: 5px;">Continue using the Telegram bot for quick access</li>
            </ul>
          </div>

          <div style="text-align: center; font-size: 14px; color: #888888; margin-top: 30px;">
            <p>Real Gold • Real Shares • Real Ownership</p>
            <p>Aureus Alliance Holdings (Pty) Ltd</p>
            <p>Building Gold-Backed Impact Ventures Across Africa</p>
          </div>
        </div>
      `;

      const textContent = `
Account Migration Successful!

Welcome to the Website, ${data.fullName}!

Your Telegram account has been successfully migrated to website access! You can now access your Aureus Alliance Holdings account through both Telegram and our website.

Your Account Details:
- Website Username: ${data.username}
- Telegram Username: ${data.telegramUsername}
- Email Address: ${data.email}
- Account Status: ✅ Fully Migrated

What's Available Now:
- Website Access: Login with your username and password
- Telegram Bot: Continue using the bot as before
- Commission Management: View and withdraw commissions on the website
- Share Purchases: Buy shares through the website interface
- Portfolio Tracking: Monitor your investments and dividends
- KYC Verification: Complete verification for withdrawals

Your Data is Safe:
All your existing commission balances, referral relationships, and transaction history have been preserved and are now accessible through both platforms.

Login to website: https://aureus.africa/login
View your dashboard: https://aureus.africa/dashboard

Need Help?
If you have any questions about your migrated account or need assistance:
- Contact our support team
- Visit our help center
- Continue using the Telegram bot for quick access

Real Gold • Real Shares • Real Ownership
Aureus Alliance Holdings (Pty) Ltd
Building Gold-Backed Impact Ventures Across Africa
      `.trim();

      const result = await this.sendEmail({
        to: data.email,
        subject,
        htmlContent,
        textContent,
        emailType: 'migration_confirmation'
      });

      console.log('✅ Migration confirmation email sent successfully to:', data.email);
      return result;

    } catch (error) {
      console.error('❌ Error sending migration confirmation email:', error);
      return {
        success: false,
        messageId: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send new referral notification to sponsor
   */
  async sendNewReferralNotification(data: NewReferralNotificationData): Promise<EmailDeliveryResult> {
    if (!this.isConfigured) {
      console.warn('⚠️ Resend not configured - new referral notification not sent');
      return {
        success: false,
        messageId: null,
        error: 'Email service not configured'
      };
    }

    try {
      const subject = `🎉 New Referral Signup - ${data.newUserName} Joined Your Team!`;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #ffffff;">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png" alt="Aureus Alliance Holdings" style="height: 60px;">
            <h1 style="color: #d4af37; margin: 20px 0;">New Team Member! 🎉</h1>
          </div>

          <div style="background-color: #2a2a2a; padding: 25px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #d4af37; margin-top: 0;">Congratulations, ${data.fullName}!</h2>
            <p style="line-height: 1.6; margin-bottom: 15px;">
              Great news! A new user has joined Aureus Alliance Holdings using your referral link.
            </p>

            <div style="background-color: #1a1a1a; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #d4af37; margin-top: 0; margin-bottom: 15px;">New Team Member Details</h3>
              <table style="width: 100%; color: #ffffff;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Name:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right; font-weight: bold;">${data.newUserName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444;">Email:</td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #444; text-align: right;">${data.newUserEmail}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;">Registration Date:</td>
                  <td style="padding: 8px 0; text-align: right;">${new Date(data.registrationDate).toLocaleDateString()}</td>
                </tr>
              </table>
            </div>

            <p style="line-height: 1.6; margin-bottom: 15px;">
              <strong>What happens next?</strong><br>
              When ${data.newUserName} makes their first share purchase, you'll automatically earn:
            </p>

            <div style="background-color: #0f3d0f; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #22c55e;">
              <p style="margin: 0; color: #22c55e; font-weight: bold;">
                💰 15% USDT Commission + 15% Share Commission
              </p>
            </div>

            <p style="line-height: 1.6; margin-bottom: 15px;">
              Keep building your network and earning passive income through our referral program!
            </p>

            <div style="text-align: center; margin: 25px 0;">
              <a href="https://aureus.africa/dashboard?section=referrals" style="background-color: #d4af37; color: #000000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                View Your Referrals
              </a>
            </div>
          </div>

          <div style="text-align: center; font-size: 14px; color: #888888; margin-top: 30px;">
            <p>Real Gold • Real Shares • Real Ownership</p>
            <p>Aureus Alliance Holdings (Pty) Ltd</p>
            <p>Building Gold-Backed Impact Ventures Across Africa</p>
          </div>
        </div>
      `;

      const textContent = `
New Team Member!

Congratulations, ${data.fullName}!

Great news! A new user has joined Aureus Alliance Holdings using your referral link.

New Team Member Details:
- Name: ${data.newUserName}
- Email: ${data.newUserEmail}
- Registration Date: ${new Date(data.registrationDate).toLocaleDateString()}

What happens next?
When ${data.newUserName} makes their first share purchase, you'll automatically earn:
💰 15% USDT Commission + 15% Share Commission

Keep building your network and earning passive income through our referral program!

View your referrals: https://aureus.africa/dashboard?section=referrals

Real Gold • Real Shares • Real Ownership
Aureus Alliance Holdings (Pty) Ltd
Building Gold-Backed Impact Ventures Across Africa
      `.trim();

      const result = await this.sendEmail({
        to: data.email,
        subject,
        htmlContent,
        textContent,
        emailType: 'new_referral'
      });

      console.log('✅ New referral notification sent successfully to:', data.email);
      return result;

    } catch (error) {
      console.error('❌ Error sending new referral notification:', error);
      return {
        success: false,
        messageId: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send withdrawal request notification email
   */
  async sendWithdrawalRequestNotification(
    email: string,
    userName: string,
    amount: number,
    currency: string,
    walletAddress: string,
    network?: string
  ): Promise<EmailResult> {
    try {
      console.log('📧 Sending withdrawal request notification to:', email);

      if (!resend) {
        throw new Error('Resend service not initialized');
      }

      const subject = `Withdrawal Request Submitted - ${amount.toFixed(2)} ${currency}`;

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Withdrawal Request Submitted</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #ffc107, #ff8f00); color: white; padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0; margin: -20px -20px 20px -20px; }
            .header h1 { margin: 0; font-size: 28px; }
            .content { padding: 20px 0; }
            .withdrawal-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107; }
            .detail-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 8px 0; border-bottom: 1px solid #eee; }
            .detail-label { font-weight: bold; color: #666; }
            .detail-value { color: #333; }
            .status-badge { background: #fff3cd; color: #856404; padding: 8px 16px; border-radius: 20px; font-weight: bold; text-align: center; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 8px; margin: 20px 0; }
            .button { display: inline-block; background: #ffc107; color: #000; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>💸 Withdrawal Request Submitted</h1>
              <p>Your withdrawal request has been received</p>
            </div>

            <div class="content">
              <p>Hello <strong>${userName}</strong>,</p>

              <p>We have received your withdrawal request and it is now being processed by our team.</p>

              <div class="withdrawal-details">
                <h3>📋 Withdrawal Details</h3>
                <div class="detail-row">
                  <span class="detail-label">Amount:</span>
                  <span class="detail-value">${amount.toFixed(2)} ${currency}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Wallet Address:</span>
                  <span class="detail-value" style="font-family: monospace; font-size: 12px;">${walletAddress}</span>
                </div>
                ${network ? `
                <div class="detail-row">
                  <span class="detail-label">Network:</span>
                  <span class="detail-value">${network}</span>
                </div>
                ` : ''}
                <div class="detail-row">
                  <span class="detail-label">Request Date:</span>
                  <span class="detail-value">${new Date().toLocaleDateString()}</span>
                </div>
              </div>

              <div class="status-badge">
                ⏳ Status: PENDING APPROVAL
              </div>

              <div class="warning">
                <strong>⚠️ Important:</strong> Your withdrawal request is now pending admin approval. You will receive another email once your request has been processed. Processing typically takes 1-3 business days.
              </div>

              <p>If you have any questions about your withdrawal, please contact our support team.</p>
            </div>

            <div class="footer">
              <p>Best regards,<br><strong>Aureus Alliance Holdings Team</strong></p>
              <p style="font-size: 12px; color: #999;">
                This is an automated message. Please do not reply to this email.
              </p>
            </div>
          </div>
        </body>
        </html>
      `;

      const textContent = `
        Withdrawal Request Submitted

        Hello ${userName},

        We have received your withdrawal request and it is now being processed by our team.

        Withdrawal Details:
        - Amount: ${amount.toFixed(2)} ${currency}
        - Wallet Address: ${walletAddress}
        ${network ? `- Network: ${network}` : ''}
        - Request Date: ${new Date().toLocaleDateString()}

        Status: PENDING APPROVAL

        Your withdrawal request is now pending admin approval. You will receive another email once your request has been processed. Processing typically takes 1-3 business days.

        If you have any questions about your withdrawal, please contact our support team.

        Best regards,
        Aureus Alliance Holdings Team
      `;

      const result = await resend.emails.send({
        from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
        to: [email],
        subject,
        html: htmlContent,
        text: textContent,
      });

      console.log('✅ Withdrawal request notification sent successfully:', result.data?.id);

      return {
        success: true,
        messageId: result.data?.id || null,
        error: null
      };

    } catch (error) {
      console.error('❌ Error sending withdrawal request notification:', error);
      return {
        success: false,
        messageId: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Strip HTML tags for text content
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}

// Export singleton instance
export const resendEmailService = new ResendEmailService();
export default resendEmailService;
