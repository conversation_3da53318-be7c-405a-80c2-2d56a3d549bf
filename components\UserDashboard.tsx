import React, { useState, useEffect, useCallback } from 'react'
import { signOut, getCurrentUser, supabase, getServiceRoleClient, getUserType } from '../lib/supabase'
import SharePurchaseFlow from './SharePurchaseFlow'

// Note: Using main supabase client instead of creating additional instances
// to avoid "Multiple GoTrueClient instances" warning
import TelegramConnectionModal from './TelegramConnectionModal'
import TelegramAccountConnection from './TelegramAccountConnection'
import { MarketingToolkit } from './MarketingToolkit'
import { EXPANSION_PLAN, EXPANSION_YEARS, getExpansionPlanForYear } from '../constants'
import { NotificationCenter } from './user/NotificationCenter'
import { NotificationBadge, NotificationDropdown } from './user/NotificationBadge'
import { ReferralCenter } from './referrals/ReferralCenter'
import { ComprehensiveDividendsCalculator } from './dividends/ComprehensiveDividendsCalculator'

import { ComprehensivePortfolio } from './portfolio/ComprehensivePortfolio'
import { EmailPinService } from '../lib/emailPinService'
import { KYCCenter } from './user/KYCCenter'
import KYCFieldResubmission from './user/KYCFieldResubmission'
import { SmartKYCRouter } from './user/SmartKYCRouter'


import { DashboardSwitcher } from './DashboardSwitcher'
import UserSupportChat from './support/UserSupportChat'
import TicketingSystem from './support/TicketingSystem'
import TrainingContentManager from './training/TrainingContentManager'
import ConsultationBooking from './consultation/ConsultationBooking'
import { ProfilePictureUpload } from './ProfilePictureUpload'
import { ProfilePictureUploadEnhanced } from './ProfilePictureUploadEnhanced'
import { ImpersonationIndicator } from './admin/ImpersonationIndicator'
import { validateImpersonationSession, getCurrentImpersonationSession } from '../lib/adminImpersonation'
import { UsernameEditor } from './user/UsernameEditor'
import { CommissionWithdrawal } from './user/CommissionWithdrawal'
import { SecurePasswordChangeForm } from './user/SecurePasswordChangeForm'
import { MessageDashboard } from './messaging/MessageDashboard'
import { TermsAndConditions } from './TermsAndConditions'
import { PrivacyPolicy } from './PrivacyPolicy'
import { ComprehensivePrivacyPolicy } from './ComprehensivePrivacyPolicy'
import { ComprehensiveDisclaimer } from './ComprehensiveDisclaimer'
import SharePurchaseAgreement from './SharePurchaseAgreement'
import { LegalDocumentService } from '../lib/legalDocumentService'
import { getFullVersion } from '../lib/version'
import { PinVerificationModal } from './PinVerificationModal'

interface UserDashboardProps {
  onLogout: () => void
  user?: any
  onNavigate?: (section: string) => void
  onSwitchDashboard?: (dashboard: 'shareholder' | 'affiliate') => void
}

// Enhanced Navigation Types
type DashboardSection =
  | 'overview'
  | 'purchase-shares'
  | 'portfolio'
  | 'referrals'
  | 'payments'
  | 'notifications'
  | 'messages'
  | 'company-presentation'
  | 'mining-operations'
  | 'community-relations'
  | 'support-center'
  | 'settings'
  | 'kyc'
  | 'legal-documents'

// Modern Icons - Enhanced Set
const DashboardIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
  </svg>
)

const SharesIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H7z" clipRule="evenodd"/>
  </svg>
)

const PortfolioIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const CommissionIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
)

const ReferralIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
  </svg>
)

const PaymentIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>
)

const NotificationIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.97 4.97a.235.235 0 0 0-.02 0 .327.327 0 0 0-.23.06.19.19 0 0 0-.06.1c0 .06.02.13.06.18l.02.02c.05.06.12.09.2.09s.15-.03.2-.09l.02-.02c.04-.05.06-.12.06-.18a.19.19 0 0 0-.06-.1.327.327 0 0 0-.23-.06.235.235 0 0 0-.02 0z" />
  </svg>
)

const PresentationIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v14a1 1 0 01-1 1H3a1 1 0 01-1-1V5a1 1 0 011-1h4z" />
  </svg>
)

const MiningIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
  </svg>
)

const CommunityIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
  </svg>
)

const SupportIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
)

const SettingsIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

const KYCIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
)

const LegalIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
)

// Navigation Menu Items Configuration - Shareholder Dashboard
const shareholderNavigationItems = [
  {
    id: 'overview' as DashboardSection,
    label: 'Dashboard Overview',
    icon: DashboardIcon,
    description: 'Your share portfolio summary and quick actions'
  },
  {
    id: 'purchase-shares' as DashboardSection,
    label: 'Purchase Shares',
    icon: SharesIcon,
    description: 'Buy gold mining shares',
    highlight: true
  },
  {
    id: 'portfolio' as DashboardSection,
    label: 'My Portfolio',
    icon: PortfolioIcon,
    description: 'View your share purchases and performance'
  },
  {
    id: 'dividends' as DashboardSection,
    label: 'Dividends',
    icon: PaymentIcon,
    description: 'View dividend payments and projections'
  },
  {
    id: 'notifications' as DashboardSection,
    label: 'Notifications',
    icon: NotificationIcon,
    description: 'View system updates and alerts',
    badge: 0 // Will be updated with actual count
  },
  {
    id: 'messages' as DashboardSection,
    label: 'Messages',
    icon: () => <span>📬</span>,
    description: 'Internal messaging system',
    badge: 0 // Will be updated with unread count
  },
  {
    id: 'company-presentation' as DashboardSection,
    label: 'Company Presentation',
    icon: PresentationIcon,
    description: 'Learn about Aureus Alliance Holdings'
  },
  {
    id: 'mining-operations' as DashboardSection,
    label: 'Mining Operations',
    icon: MiningIcon,
    description: 'View mining progress and operations'
  },
  {
    id: 'support-center' as DashboardSection,
    label: 'Support Center',
    icon: SupportIcon,
    description: 'Get help and contact support'
  },
  {
    id: 'settings' as DashboardSection,
    label: 'Settings',
    icon: SettingsIcon,
    description: 'Manage your account preferences'
  },
  {
    id: 'kyc' as DashboardSection,
    label: 'KYC Verification',
    icon: KYCIcon,
    description: 'Complete identity verification for full access',
    conditional: true
  },
  {
    id: 'legal-documents' as DashboardSection,
    label: 'Legal Documents',
    icon: LegalIcon,
    description: 'Terms, privacy policy, and legal information'
  }
]

// Navigation Menu Items Configuration - Affiliate Dashboard
const affiliateNavigationItems = [
  {
    id: 'overview' as DashboardSection,
    label: 'Dashboard Overview',
    icon: DashboardIcon,
    description: 'Your affiliate performance and earnings summary'
  },
  {
    id: 'referrals' as DashboardSection,
    label: 'Referral Program',
    icon: ReferralIcon,
    description: 'Manage referrals and earn commissions',
    highlight: true
  },
  {
    id: 'network' as DashboardSection,
    label: 'My Network',
    icon: PortfolioIcon,
    description: 'View your referral network and downlines'
  },
  {
    id: 'commissions' as DashboardSection,
    label: 'Commission Earnings',
    icon: PaymentIcon,
    description: 'Track commission payments and withdrawals'
  },
  {
    id: 'marketing-tools' as DashboardSection,
    label: 'Marketing Tools',
    icon: PresentationIcon,
    description: 'Access marketing materials and training'
  },
  {
    id: 'payments' as DashboardSection,
    label: 'Payment Status',
    icon: PaymentIcon,
    description: 'Track payments and transactions'
  },
  {
    id: 'notifications' as DashboardSection,
    label: 'Notifications',
    icon: NotificationIcon,
    description: 'View system updates and alerts',
    badge: 0 // Will be updated with actual count
  },
  {
    id: 'messages' as DashboardSection,
    label: 'Messages',
    icon: () => <span>📬</span>,
    description: 'Internal messaging system',
    badge: 0 // Will be updated with unread count
  },
  {
    id: 'company-presentation' as DashboardSection,
    label: 'Company Presentation',
    icon: PresentationIcon,
    description: 'Learn about Aureus Alliance Holdings'
  },
  {
    id: 'mining-operations' as DashboardSection,
    label: 'Mining Operations',
    icon: MiningIcon,
    description: 'View mining progress and operations'
  },
  {
    id: 'community-relations' as DashboardSection,
    label: 'Community Relations',
    icon: CommunityIcon,
    description: 'Community development and engagement'
  },
  {
    id: 'support-center' as DashboardSection,
    label: 'Support Center',
    icon: SupportIcon,
    description: 'Get help and contact support'
  },
  {
    id: 'settings' as DashboardSection,
    label: 'Settings',
    icon: SettingsIcon,
    description: 'Manage your account preferences'
  },
  {
    id: 'legal-documents' as DashboardSection,
    label: 'Legal Documents',
    icon: LegalIcon,
    description: 'Terms, privacy policy, and legal information'
  }
]

// Dividend Calculator Component
interface DividendCalculatorProps {
  userShares: number
  onCalculationChange?: (calculation: any) => void
}

const DividendCalculator: React.FC<DividendCalculatorProps> = ({ userShares, onCalculationChange }) => {
  // Initialize with 2026 expansion plan data
  const initialExpansionData = getExpansionPlanForYear(2026);
  const [inputs, setInputs] = useState({
    landHa: initialExpansionData?.hectares || 250, // Default to 2026 capacity
    avgGravelThickness: 0.8,
    inSituGrade: 0.9,
    recoveryFactor: 70,
    goldPriceUsdPerKg: 109026, // Current market price
    opexPercent: 45,
    dividendPayoutPercent: 50,
    selectedYear: 2026
  })

  const [calculated, setCalculated] = useState({
    numPlants: 0,
    annualRevenue: 0,
    annualEbit: 0,
    annualGoldKg: 0,
    dividendPerShare: 0,
    userAnnualDividend: 0
  })

  // Constants from the main calculator
  const PLANT_CAPACITY_TPH = 200
  const EFFECTIVE_HOURS_PER_DAY = 20
  const OPERATING_DAYS_PER_YEAR = 330
  const BULK_DENSITY_T_PER_M3 = 1.8
  const HA_PER_PLANT = 25
  const TOTAL_SHARES = 1400000

  useEffect(() => {
    const { landHa, avgGravelThickness, inSituGrade, recoveryFactor, goldPriceUsdPerKg, opexPercent } = inputs

    // Calculate based on user's land selection (using same formula as homepage calculator)
    const numPlants = landHa / HA_PER_PLANT
    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR
    const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000
    const annualRevenue = annualGoldKg * goldPriceUsdPerKg
    const annualOperatingCost = annualRevenue * (opexPercent / 100)
    const annualEbit = annualRevenue - annualOperatingCost

    // Calculate dividend per share dynamically: Full EBIT ÷ Total Shares (100% payout like homepage)
    const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0
    const userAnnualDividend = dividendPerShare * userShares

    const calculationResult = {
      numPlants,
      annualRevenue,
      annualEbit,
      annualGoldKg,
      dividendPerShare,
      userAnnualDividend,
      inputs // Include inputs so parent can access current gold price
    }

    setCalculated(calculationResult)

    // Notify parent component of calculation changes
    if (onCalculationChange) {
      onCalculationChange(calculationResult)
    }
  }, [inputs, userShares, onCalculationChange])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setInputs(prev => ({ ...prev, [name]: parseFloat(value) || 0 }))
  }

  // Handler for year selection - automatically sets corresponding hectares
  const handleYearSelection = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedYear = parseInt(e.target.value)
    const expansionData = getExpansionPlanForYear(selectedYear)

    console.log('Dashboard - Year selected:', selectedYear, 'Expansion data:', expansionData)

    if (expansionData) {
      setInputs(prev => ({
        ...prev,
        selectedYear: selectedYear,
        landHa: expansionData.hectares
      }))
    }
  }

  const formatNumber = (num: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat('en-US', options).format(num)
  }

  const LAND_SIZE_OPTIONS = Array.from({ length: 200 }, (_, i) => (i + 1) * 25)

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      marginBottom: '30px',
      border: '1px solid #374151'
    }}>
      <h3 style={{
        color: '#F59E0B',
        fontSize: '20px',
        fontWeight: 'bold',
        margin: '0 0 8px 0',
        textAlign: 'center'
      }}>
        Dividend Calculator
      </h3>
      <p style={{
        color: '#9CA3AF',
        fontSize: '14px',
        textAlign: 'center',
        margin: '0 0 24px 0'
      }}>
        Configure your scenario and see how dividends are calculated based on your {userShares.toLocaleString()} shares
      </p>

      {/* Input Parameters */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px',
        marginBottom: '24px'
      }}>
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>TARGET YEAR</h4>
          <select
            name="selectedYear"
            value={inputs.selectedYear}
            onChange={handleYearSelection}
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: 'rgba(17, 24, 39, 0.8)',
              border: '1px solid #374151',
              borderRadius: '6px',
              color: 'white',
              fontSize: '16px'
            }}
          >
            {EXPANSION_YEARS.map(year => {
              const expansionData = getExpansionPlanForYear(year)
              return (
                <option key={year} value={year}>
                  {expansionData?.month} {year} - {expansionData?.plants} Plants ({expansionData?.hectares.toLocaleString()} ha)
                </option>
              )
            })}
          </select>
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            Select our planned capacity for a specific year
          </p>
        </div>

        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>LAND SIZE (Manual Override)</h4>
          <select
            name="landHa"
            value={inputs.landHa}
            onChange={handleInputChange}
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: 'rgba(17, 24, 39, 0.8)',
              border: '1px solid #374151',
              borderRadius: '6px',
              color: 'white',
              fontSize: '16px'
            }}
          >
            {LAND_SIZE_OPTIONS.map(size => (
              <option key={size} value={size}>{size} ha</option>
            ))}
          </select>
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            Corresponds to {formatNumber(inputs.landHa / HA_PER_PLANT * (TOTAL_SHARES / 10), { maximumFractionDigits: 0 })} shares<br/>
            Adjust manually for "what-if" scenarios
          </p>
        </div>

        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>YOUR SHARES</h4>
          <div style={{
            padding: '8px 12px',
            backgroundColor: 'rgba(17, 24, 39, 0.4)',
            border: '1px solid #374151',
            borderRadius: '6px',
            color: '#9CA3AF',
            fontSize: '16px'
          }}>
            {userShares.toLocaleString()}
          </div>
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            Total project shares: {TOTAL_SHARES.toLocaleString()}
          </p>
        </div>

        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>GOLD PRICE</h4>
          <input
            type="number"
            name="goldPriceUsdPerKg"
            value={inputs.goldPriceUsdPerKg}
            onChange={handleInputChange}
            step="1000"
            style={{
              width: '100%',
              padding: '8px 12px',
              backgroundColor: 'rgba(17, 24, 39, 0.8)',
              border: '1px solid #374151',
              borderRadius: '6px',
              color: 'white',
              fontSize: '16px'
            }}
          />
          <p style={{ color: '#9CA3AF', fontSize: '12px', margin: '8px 0 0 0' }}>
            USD per kilogram
          </p>
        </div>
      </div>

      {/* Results */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '16px'
      }}>
        <div style={{
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>🏭</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.numPlants, { maximumFractionDigits: 1 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Plants for Land</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>💰</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualRevenue, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Land Revenue Potential</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>📊</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualEbit, { style: 'currency', currency: 'USD', minimumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Land EBIT Potential</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>🥇</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {formatNumber(calculated.annualGoldKg, { maximumFractionDigits: 0 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>kg/year</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>💎</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {"$" + formatNumber(calculated.userAnnualDividend, { minimumFractionDigits: 2 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Your Annual Dividend</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(168, 85, 247, 0.1)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '4px' }}>📈</div>
          <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
            {"$" + formatNumber(calculated.dividendPerShare, { minimumFractionDigits: 4 })}
          </div>
          <div style={{ color: '#9CA3AF', fontSize: '12px' }}>Dividend Per Share</div>
        </div>
      </div>

      {/* Technical Parameters Display */}
      <div style={{
        marginTop: '24px',
        padding: '16px',
        backgroundColor: 'rgba(55, 65, 81, 0.3)',
        borderRadius: '8px',
        border: '1px solid #374151'
      }}>
        <h4 style={{ color: '#F59E0B', fontSize: '14px', fontWeight: 'bold', margin: '0 0 12px 0' }}>TECHNICAL PARAMETERS</h4>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px',
          fontSize: '13px'
        }}>
          <div>
            <span style={{ color: '#9CA3AF' }}>Gravel Thickness:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.avgGravelThickness}m</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>In-situ Grade:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.inSituGrade} g/t</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Recovery Factor:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.recoveryFactor}%</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Operating Cost:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.opexPercent}%</span>
          </div>
          <div>
            <span style={{ color: '#9CA3AF' }}>Dividend Payout:</span>
            <span style={{ color: 'white', marginLeft: '8px' }}>{inputs.dividendPayoutPercent}%</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export const UserDashboard: React.FC<UserDashboardProps> = ({ onLogout, user: propUser, onNavigate, onSwitchDashboard }) => {
  // Enhanced State Management
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)
  const [activeSection, setActiveSection] = useState<DashboardSection>(() => {
    // Check URL parameters for initial section
    const urlParams = new URLSearchParams(window.location.search);
    const sectionParam = urlParams.get('section');
    if (sectionParam && ['overview', 'portfolio', 'purchase-shares', 'kyc', 'dividends', 'notifications', 'messages', 'legal-documents', 'settings'].includes(sectionParam)) {
      return sectionParam as DashboardSection;
    }
    return 'overview';
  })
  const [showPurchaseFlow, setShowPurchaseFlow] = useState(false)
  const [showTelegramModal, setShowTelegramModal] = useState(false)
  const [showSharesModal, setShowSharesModal] = useState(false)
  const [telegramUser, setTelegramUser] = useState<any>(null)
  const [telegramLoading, setTelegramLoading] = useState(false)
  const [userSharePurchases, setUserSharePurchases] = useState<any[]>([])
  const [userData, setUserData] = useState<any>(null)
  const [commissionData, setCommissionData] = useState<any>(null)
  const [notificationCount, setNotificationCount] = useState(0)
  const [unreadMessageCount, setUnreadMessageCount] = useState(0)
  const [kycStatus, setKycStatus] = useState<'pending' | 'approved' | 'rejected' | 'not_started'>('not_started')
  const [kycId, setKycId] = useState<string | null>(null)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Demo mode: DISABLED - All users have full access
  const isDemoUser = () => {
    return true // Always return true to give all users full access
  }

  // Helper function to get referral username
  const getReferralUsername = (user: any) => {
    // Priority order: username -> database_user.username -> email prefix -> fallback
    if (user?.username) {
      return user.username
    }
    if (user?.database_user?.username) {
      return user.database_user.username
    }
    if (user?.email) {
      const emailPrefix = user.email.split('@')[0]
      // Clean up email prefix to be URL-friendly
      return emailPrefix.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()
    }
    if (user?.database_user?.email) {
      const emailPrefix = user.database_user.email.split('@')[0]
      return emailPrefix.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()
    }
    // Final fallback using user ID
    const userId = user?.database_user?.id || user?.id
    return userId ? `user_${userId.toString().slice(-8)}` : 'user_unknown'
  }

  // Navigation Functions
  const handleSectionChange = (section: DashboardSection) => {
    // Prevent navigation away from legal documents if compliance is required
    if (mustCompleteLegalDocuments && section !== 'legal-documents') {
      setMessage({
        type: 'warning',
        text: 'Please complete all legal document agreements before accessing other sections.'
      })
      setTimeout(() => setMessage(null), 4000)
      return
    }

    setActiveSection(section)
    setIsMobileMenuOpen(false)

    // Handle special cases
    if (section === 'purchase-shares') {
      setShowPurchaseFlow(true)
    }
  }

  // Get filtered navigation items based on user type and status
  const getNavigationItems = () => {
    // IMPORTANT: UserDashboard component ALWAYS shows shareholder navigation
    // regardless of user's actual role, because this IS the shareholder dashboard
    // Users can switch between dashboards using the dashboard switcher

    console.log('🔍 UserDashboard - Always showing shareholder navigation items')

    // Always use shareholder navigation items for UserDashboard component
    const baseNavigationItems = shareholderNavigationItems

    return baseNavigationItems.filter(item => {
      if (item.conditional && item.id === 'kyc') {
        return kycStatus !== 'approved'
      }
      return true
    }).map(item => ({
      ...item,
      badge: item.id === 'notifications' ? notificationCount :
             item.id === 'messages' ? unreadMessageCount : item.badge
    }))
  }

  // Dashboard data states
  const [dashboardData, setDashboardData] = useState({
    totalShares: 0,
    shareValue: 0,
    futureDividends: 0,
    // Separate commission types like Telegram bot
    usdtCommissions: {
      totalEarned: 0,
      available: 0,
      escrowed: 0
    },
    shareCommissions: {
      totalShares: 0,
      currentValue: 0
    },
    accountBalance: 0,
    referralCount: 0,
    monthlyEarnings: 0,
    currentSharePrice: 25
  })
  const [dataLoading, setDataLoading] = useState(true)

  // Settings states
  const [settingsLoading, setSettingsLoading] = useState(false)
  const [settingsMessage, setSettingsMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // General message state for dashboard-wide messages
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'warning', text: string } | null>(null)
  const [showProfileEditor, setShowProfileEditor] = useState(false)
  const [showUsernameEditor, setShowUsernameEditor] = useState(false)
  const [profileEditorData, setProfileEditorData] = useState({
    full_name: '',
    phone: '',
    profile_description: ''
  })

  // Notification states
  const [showNotificationDropdown, setShowNotificationDropdown] = useState(false)

  // Password change modal state
  const [showPasswordChangeModal, setShowPasswordChangeModal] = useState(false)

  // 2FA PIN verification states
  const [show2FAPinModal, setShow2FAPinModal] = useState(false)
  const [pending2FAState, setPending2FAState] = useState<boolean | null>(null)

  // Email verification states
  const [emailVerificationLoading, setEmailVerificationLoading] = useState(false)
  const [emailVerificationMessage, setEmailVerificationMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [lastEmailSentAt, setLastEmailSentAt] = useState<Date | null>(null)
  const [showEmailVerificationPinModal, setShowEmailVerificationPinModal] = useState(false)

  // Notification preferences states
  const [notificationPreferences, setNotificationPreferences] = useState({
    payment_confirmations: true,
    security_alerts: true,
    commission_updates: true,
    marketing_emails: false,
    in_app_notifications: true,
    sound_notifications: false,
    notification_frequency: 'immediate'
  })
  const [notificationPreferencesLoading, setNotificationPreferencesLoading] = useState(false)
  const [notificationPreferencesMessage, setNotificationPreferencesMessage] = useState<{
    type: 'success' | 'error'
    text: string
  } | null>(null)

  // Legal documents modal states
  const [showTermsModal, setShowTermsModal] = useState(false)
  const [showPrivacyModal, setShowPrivacyModal] = useState(false)
  const [showShareAgreementModal, setShowShareAgreementModal] = useState(false)
  const [showDisclaimerModal, setShowDisclaimerModal] = useState(false)
  const [showSharePurchaseAgreementModal, setShowSharePurchaseAgreementModal] = useState(false)

  // Agreement tracking states
  const [agreementStatus, setAgreementStatus] = useState({
    terms_conditions: false,
    privacy_policy: false,
    disclaimer: false,
    share_purchase_agreement: false,
    all_agreed: false
  })
  const [agreementLoading, setAgreementLoading] = useState(false)
  const [isCheckingCompliance, setIsCheckingCompliance] = useState(true)
  const [mustCompleteLegalDocuments, setMustCompleteLegalDocuments] = useState(false)

  // Check agreement status on component mount and enforce compliance
  useEffect(() => {
    const checkAgreements = async () => {
      if (user?.id) {
        console.log('🔍 LEGAL DOCS: Starting compliance check for user:', user.id)
        console.log('🔍 LEGAL DOCS: User object structure:', {
          user_id: user.id,
          user_id_type: typeof user.id,
          database_user_id: user?.database_user?.id,
          database_user_id_type: typeof user?.database_user?.id,
          email: user?.email,
          database_user_email: user?.database_user?.email,
          full_user_object: user
        })

        setIsCheckingCompliance(true)
        try {
          // Use database_user.id if available, fallback to user.id
          const userIdForCheck = user?.database_user?.id || user.id
          console.log('🔍 LEGAL DOCS: Using ID for check:', userIdForCheck, 'Type:', typeof userIdForCheck)

          const status = await LegalDocumentService.checkAgreementStatus(userIdForCheck)
          console.log('📋 LEGAL DOCS: Service returned status:', status)
          console.log('📋 LEGAL DOCS: Individual document status:', {
            terms_conditions: status.terms_conditions,
            privacy_policy: status.privacy_policy,
            disclaimer: status.disclaimer,
            share_purchase_agreement: status.share_purchase_agreement,
            all_agreed: status.all_agreed
          })

          setAgreementStatus(status)

          // If not all documents are agreed to, force legal documents section
          if (!status.all_agreed) {
            console.log('⚠️ LEGAL DOCS: Documents incomplete, forcing compliance mode')
            console.log('⚠️ LEGAL DOCS: Missing documents:', {
              terms_conditions: !status.terms_conditions,
              privacy_policy: !status.privacy_policy,
              disclaimer: !status.disclaimer,
              share_purchase_agreement: !status.share_purchase_agreement
            })
            setMustCompleteLegalDocuments(true)
            setActiveSection('legal-documents')
          } else {
            console.log('✅ LEGAL DOCS: All documents completed, allowing dashboard access')
            setMustCompleteLegalDocuments(false)
          }
        } catch (error) {
          console.error('❌ LEGAL DOCS: Error checking legal compliance:', error)
          console.error('❌ LEGAL DOCS: Error details:', {
            message: error.message,
            stack: error.stack,
            user_id: user.id,
            user_id_type: typeof user.id
          })
          // On error, force legal documents section for safety
          setMustCompleteLegalDocuments(true)
          setActiveSection('legal-documents')
        } finally {
          setIsCheckingCompliance(false)
          console.log('🔍 LEGAL DOCS: Compliance check completed')
        }
      } else {
        console.log('⚠️ LEGAL DOCS: No user ID available for compliance check')
      }
    }

    // Add a small delay to ensure user data is fully loaded
    // Only run if we have a valid user with either user.id or database_user.id
    if (user?.id || user?.database_user?.id) {
      const timeoutId = setTimeout(checkAgreements, 200) // Increased delay
      return () => clearTimeout(timeoutId)
    }
  }, [user?.id, user?.email, user?.database_user?.id, loading]) // Enhanced dependencies including loading state

  // Handle legal document agreement
  const handleDocumentAgreement = async (
    documentType: 'terms_conditions' | 'privacy_policy' | 'disclaimer' | 'share_purchase_agreement',
    closeModal: () => void
  ) => {
    if (!user?.id) {
      console.log('❌ LEGAL DOCS: No user ID available for recording agreement')
      return
    }

    // Use database_user.id if available, fallback to user.id
    const userIdForRecord = user?.database_user?.id || user.id
    console.log('📝 LEGAL DOCS: Recording agreement for document type:', documentType)
    console.log('📝 LEGAL DOCS: Using user ID:', userIdForRecord, 'Type:', typeof userIdForRecord)
    console.log('📝 LEGAL DOCS: User object details:', {
      user_id: user.id,
      database_user_id: user?.database_user?.id,
      email: user?.email || user?.database_user?.email
    })

    setAgreementLoading(true)
    try {
      const result = await LegalDocumentService.recordAgreement(userIdForRecord, documentType)
      console.log('📋 LEGAL DOCS: Agreement recording result:', result)

      if (result.success) {
        console.log('✅ LEGAL DOCS: Agreement recorded successfully')

        // Update agreement status
        console.log('🔄 LEGAL DOCS: Checking updated agreement status...')
        const newStatus = await LegalDocumentService.checkAgreementStatus(userIdForRecord)
        console.log('📋 LEGAL DOCS: Updated agreement status:', newStatus)
        console.log('📋 LEGAL DOCS: Updated individual status:', {
          terms_conditions: newStatus.terms_conditions,
          privacy_policy: newStatus.privacy_policy,
          disclaimer: newStatus.disclaimer,
          share_purchase_agreement: newStatus.share_purchase_agreement,
          all_agreed: newStatus.all_agreed
        })

        setAgreementStatus(newStatus)

        // Check if all documents are now agreed to and user was in compliance mode
        if (newStatus.all_agreed && mustCompleteLegalDocuments) {
          console.log('🎉 All legal documents now completed! Redirecting to dashboard...')
          setMustCompleteLegalDocuments(false)
          // Redirect to dashboard overview after completing all agreements
          setActiveSection('overview')

          // Show success message
          setMessage({
            type: 'success',
            text: 'All legal documents completed! Welcome to your dashboard.'
          })
          setTimeout(() => setMessage(null), 5000)
        } else if (newStatus.all_agreed) {
          console.log('✅ All documents agreed, but user was not in compliance mode')
          // Show individual agreement success message
          setSettingsMessage({
            type: 'success',
            text: 'Agreement recorded successfully!'
          })
          setTimeout(() => setSettingsMessage(null), 3000)
        } else {
          console.log('📝 Document agreement recorded, but not all documents completed yet. Status:', newStatus)
          // Show individual agreement success message
          setSettingsMessage({
            type: 'success',
            text: 'Agreement recorded successfully!'
          })
          setTimeout(() => setSettingsMessage(null), 3000)
        }

        // Close modal immediately on success
        closeModal()
      } else {
        // Show error message
        setSettingsMessage({
          type: 'error',
          text: result.error || 'Failed to record agreement'
        })
        setTimeout(() => setSettingsMessage(null), 5000)

        // Close modal after showing error message briefly
        setTimeout(() => closeModal(), 2000)
      }
    } catch (error) {
      console.error('Error recording agreement:', error)
      setSettingsMessage({
        type: 'error',
        text: 'Failed to record agreement'
      })
      setTimeout(() => setSettingsMessage(null), 5000)

      // Close modal after showing error message briefly
      setTimeout(() => closeModal(), 2000)
    } finally {
      setAgreementLoading(false)
    }
  }

  // Calculator state to track current gold price
  const [currentCalculatorData, setCurrentCalculatorData] = useState<any>(null)

  // Handle calculator changes to update Future Dividends
  const handleCalculatorChange = useCallback((calculationData: any) => {
    setCurrentCalculatorData(calculationData)

    // Update Future Dividends with the same calculation as the calculator
    if (calculationData && dashboardData.totalShares > 0) {
      const futureDividends = calculationData.dividendPerShare * dashboardData.totalShares
      setDashboardData(prev => ({
        ...prev,
        futureDividends
      }))
    }
  }, [dashboardData.totalShares])

  useEffect(() => {
    loadUser()
  }, [])

  // Load notification preferences when user data is available
  useEffect(() => {
    if (user?.database_user?.id) {
      loadNotificationPreferences()
    }
  }, [user?.database_user?.id])

  // Centralized profile picture update function
  const handleProfilePictureUpdate = async (newImageUrl: string) => {
    console.log('🖼️ Updating profile picture across all components:', newImageUrl);

    // Small delay to ensure database update has completed
    await new Promise(resolve => setTimeout(resolve, 100));

    // Update the user object immediately
    if (user?.database_user) {
      user.database_user.profile_image_url = newImageUrl;
      user.database_user.updated_at = new Date().toISOString();
    }

    // Update localStorage for persistence with timestamp
    const currentTimestamp = new Date().toISOString();
    const storedUser = localStorage.getItem('aureus_user');
    if (storedUser) {
      const userData = JSON.parse(storedUser);
      userData.profile_image_url = newImageUrl;
      userData.updated_at = currentTimestamp;
      localStorage.setItem('aureus_user', JSON.stringify(userData));
      console.log('✅ Profile picture URL updated in localStorage with timestamp');
    }

    const storedTelegramUser = localStorage.getItem('aureus_telegram_user');
    if (storedTelegramUser) {
      const telegramUserData = JSON.parse(storedTelegramUser);
      if (telegramUserData.database_user) {
        telegramUserData.database_user.profile_image_url = newImageUrl;
        telegramUserData.database_user.updated_at = currentTimestamp;
      }
      localStorage.setItem('aureus_telegram_user', JSON.stringify(telegramUserData));
      console.log('✅ Profile picture URL updated in Telegram user localStorage with timestamp');
    }

    // Force a re-render by updating state with the new URL
    const updatedUser = { ...user };
    if (updatedUser.database_user) {
      updatedUser.database_user.profile_image_url = newImageUrl;
      updatedUser.database_user.updated_at = currentTimestamp;
    }
    setUser(updatedUser);

    // Show success message
    setSettingsMessage({ type: 'success', text: 'Profile picture updated successfully!' });
    setTimeout(() => setSettingsMessage(null), 3000);

    console.log('✅ Profile picture update completed with timestamp:', currentTimestamp);
  };

  // Settings handlers
  const handle2FAToggle = async (enabled: boolean) => {
    if (!user?.database_user?.id) return;

    // Store the pending state and show PIN verification modal
    setPending2FAState(enabled);
    setShow2FAPinModal(true);
  };

  // Handle 2FA PIN verification success
  const handle2FAPinVerified = async () => {
    if (!user?.database_user?.id || pending2FAState === null) return;

    setSettingsLoading(true);
    setSettingsMessage(null);

    try {
      const result = await EmailPinService.toggle2FA(user.database_user.id, pending2FAState);

      if (result.success) {
        // Reload security settings from database to get the updated two_factor_enabled status
        await loadUserSecuritySettings(user.database_user.id);

        setSettingsMessage({
          type: 'success',
          text: `2FA ${pending2FAState ? 'enabled' : 'disabled'} successfully`
        });

        // Clear message after 3 seconds
        setTimeout(() => setSettingsMessage(null), 3000);
      } else {
        setSettingsMessage({
          type: 'error',
          text: result.error || 'Failed to update 2FA settings'
        });
      }
    } catch (error) {
      console.error('Error toggling 2FA:', error);
      setSettingsMessage({
        type: 'error',
        text: 'Failed to update 2FA settings'
      });
    } finally {
      setSettingsLoading(false);
      setPending2FAState(null);
    }
  };

  // Handle 2FA PIN modal close
  const handle2FAPinModalClose = () => {
    setShow2FAPinModal(false);
    setPending2FAState(null);
    setSettingsLoading(false);
  };

  // Profile editor handlers
  const openProfileEditor = () => {
    setProfileEditorData({
      full_name: user?.database_user?.full_name || '',
      phone: user?.database_user?.phone || '',
      profile_description: user?.database_user?.profile_description || ''
    });
    setShowProfileEditor(true);
  };

  const handleProfileUpdate = async () => {
    if (!user?.database_user?.id) return;

    setSettingsLoading(true);
    setSettingsMessage(null);

    try {
      console.log('🔄 Updating profile for user:', user.database_user.id);
      console.log('📝 Profile data:', profileEditorData);

      // Use service role client to bypass RLS policies
      const serviceClient = getServiceRoleClient();
      const { error } = await serviceClient
        .from('users')
        .update({
          full_name: profileEditorData.full_name,
          phone: profileEditorData.phone,
          profile_description: profileEditorData.profile_description,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.database_user.id);

      if (error) {
        console.error('❌ Database error:', error);
        throw error;
      }

      console.log('✅ Profile updated successfully in database');

      // Update user state with debugging
      console.log('🔄 Updating user state with new profile data:', profileEditorData);
      setUser(prev => {
        console.log('🔍 Previous user state:', prev);
        const updatedUser = {
          ...prev,
          database_user: {
            ...prev?.database_user,
            full_name: profileEditorData.full_name,
            phone: profileEditorData.phone,
            profile_description: profileEditorData.profile_description
          }
        };
        console.log('🔄 Updated user state:', updatedUser);
        return updatedUser;
      });

      // Force refresh profile data from database to ensure UI is up to date
      console.log('🔄 Force refreshing profile data from database...');
      try {
        const serviceClient = getServiceRoleClient();
        const { data: refreshedUser, error: refreshError } = await serviceClient
          .from('users')
          .select('*')
          .eq('id', user.database_user.id)
          .single();

        if (!refreshError && refreshedUser) {
          console.log('✅ Refreshed user data from database:', refreshedUser);
          setUser(prev => ({
            ...prev,
            database_user: refreshedUser
          }));
        }
      } catch (refreshError) {
        console.warn('⚠️ Could not refresh user data:', refreshError);
      }

      setSettingsMessage({
        type: 'success',
        text: 'Profile updated successfully!'
      });

      setShowProfileEditor(false);
      setTimeout(() => setSettingsMessage(null), 3000);

    } catch (error) {
      console.error('❌ Error updating profile:', error);
      setSettingsMessage({
        type: 'error',
        text: `Failed to update profile: ${error.message || 'Unknown error'}`
      });
    } finally {
      setSettingsLoading(false);
    }
  };

  // Username update handler
  const handleUsernameUpdate = (newUsername: string) => {
    // Update user state
    setUser(prev => ({
      ...prev,
      database_user: {
        ...prev.database_user,
        username: newUsername
      }
    }));

    // Update localStorage
    const storedUser = localStorage.getItem('aureus_user');
    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        parsedUser.username = newUsername;
        localStorage.setItem('aureus_user', JSON.stringify(parsedUser));
      } catch (error) {
        console.error('Error updating username in localStorage:', error);
      }
    }

    setSettingsMessage({
      type: 'success',
      text: 'Username updated successfully!'
    });

    setTimeout(() => setSettingsMessage(null), 3000);
  };

  // Email verification handler - Use PIN verification for consistency
  const handleResendVerificationEmail = async () => {
    if (!user?.database_user?.id || !user?.database_user?.email) return;

    // Rate limiting: prevent sending more than once every 5 minutes
    if (lastEmailSentAt && Date.now() - lastEmailSentAt.getTime() < 5 * 60 * 1000) {
      const remainingTime = Math.ceil((5 * 60 * 1000 - (Date.now() - lastEmailSentAt.getTime())) / 1000);
      setEmailVerificationMessage({
        type: 'error',
        text: `Please wait ${remainingTime} seconds before requesting another verification email.`
      });
      return;
    }

    // Clear any previous messages and show the PIN verification modal instead
    setEmailVerificationMessage(null);
    setShowEmailVerificationPinModal(true);
    setLastEmailSentAt(new Date());
  };

  // Handle successful email verification via PIN
  const handleEmailVerificationPinSuccess = async () => {
    if (!user?.database_user?.id) return;

    try {
      // Update email_verified status in database
      const serviceClient = getServiceRoleClient();
      const { error } = await serviceClient
        .from('users')
        .update({ email_verified: true })
        .eq('id', user.database_user.id);

      if (error) {
        console.error('❌ Error updating email verification status:', error);
        setEmailVerificationMessage({
          type: 'error',
          text: 'Failed to update email verification status'
        });
        return;
      }

      // Reload security settings from database to get the updated email_verified status
      await loadUserSecuritySettings(user.database_user.id);

      // Show success message
      setEmailVerificationMessage({
        type: 'success',
        text: 'Email verified successfully! Your account is now fully verified.'
      });

      // Close modal
      setShowEmailVerificationPinModal(false);

      // Clear success message after 5 seconds
      setTimeout(() => setEmailVerificationMessage(null), 5000);
    } catch (error) {
      console.error('❌ Error in email verification success handler:', error);
      setEmailVerificationMessage({
        type: 'error',
        text: 'Failed to complete email verification'
      });
    }
  };

  // Debug function to refresh user data (for testing)
  const handleRefreshUserData = async () => {
    console.log('🔄 Refreshing user data...');
    // Clear localStorage cache
    localStorage.removeItem('aureus_user');
    localStorage.removeItem('aureus_telegram_user');
    localStorage.removeItem('aureus_test_user');
    // Reload user data
    await loadUser();
    console.log('✅ User data refreshed');
  };

  // Notification preferences handlers
  const handleNotificationPreferenceChange = (key: string, value: any) => {
    setNotificationPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveNotificationPreferences = async () => {
    if (!user?.database_user?.id) return;

    setNotificationPreferencesLoading(true);
    setNotificationPreferencesMessage(null);

    try {
      console.log('🔔 Saving notification preferences:', notificationPreferences);

      const serviceClient = getServiceRoleClient();

      // Prepare the data for database
      const preferencesData = {
        user_id: user.database_user.id,
        telegram_id: user.database_user.telegram_id || 0, // Default to 0 if no telegram_id
        payment_confirmations: notificationPreferences.payment_confirmations,
        security_alerts: notificationPreferences.security_alerts,
        commission_updates: notificationPreferences.commission_updates,
        marketing_emails: notificationPreferences.marketing_emails,
        in_app_notifications: notificationPreferences.in_app_notifications,
        sound_notifications: notificationPreferences.sound_notifications,
        notification_frequency: notificationPreferences.notification_frequency,
        audio_enabled: notificationPreferences.sound_notifications, // Map to existing field
        updated_at: new Date().toISOString()
      };

      // Use upsert to insert or update
      const { error } = await serviceClient
        .from('user_notification_preferences')
        .upsert(preferencesData, {
          onConflict: 'user_id'
        });

      if (error) {
        throw error;
      }

      setNotificationPreferencesMessage({
        type: 'success',
        text: 'Notification preferences saved successfully!'
      });

      // Clear message after 3 seconds
      setTimeout(() => setNotificationPreferencesMessage(null), 3000);

    } catch (error) {
      console.error('Error saving notification preferences:', error);
      setNotificationPreferencesMessage({
        type: 'error',
        text: 'Failed to save notification preferences'
      });
    } finally {
      setNotificationPreferencesLoading(false);
    }
  };

  // Load user security settings from database
  const loadUserSecuritySettings = async (userId: number) => {
    try {
      console.log('🔐 Loading security settings for user:', userId);

      const serviceClient = getServiceRoleClient();
      const { data, error } = await serviceClient
        .from('users')
        .select('two_factor_enabled, email_verified')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Error loading security settings:', error);
        return;
      }

      if (data) {
        console.log('✅ Security settings loaded:', data);

        // Update user state with current database values
        setUser(prev => ({
          ...prev,
          database_user: {
            ...prev?.database_user,
            two_factor_enabled: data.two_factor_enabled,
            email_verified: data.email_verified
          }
        }));
      }
    } catch (error) {
      console.error('❌ Error in loadUserSecuritySettings:', error);
    }
  };

  // Load notification preferences from database via API
  const loadNotificationPreferences = async () => {
    if (!user?.database_user?.id) return;

    try {
      console.log('🔔 Loading notification preferences for user:', user.database_user.id);

      const response = await fetch(`/api/user-notifications?userId=${user.database_user.id}&type=preferences`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to load notification preferences');
      }

      const data = result.data;
      if (data && data.user_id) {
        // Update state with loaded preferences
        setNotificationPreferences({
          payment_confirmations: data.payment_confirmations ?? true,
          security_alerts: data.security_alerts ?? true,
          commission_updates: data.commission_updates ?? true,
          marketing_emails: data.marketing_emails ?? false,
          in_app_notifications: data.in_app_notifications ?? true,
          sound_notifications: data.sound_notifications ?? false,
          notification_frequency: data.notification_frequency ?? 'immediate'
        });
        console.log('✅ Notification preferences loaded via API:', data);
      } else {
        console.log('ℹ️ No existing notification preferences found, using defaults');
      }

    } catch (error) {
      console.error('Error loading notification preferences:', error);
    }
  };

  const loadUser = async () => {
    try {
      console.log('🔄 Loading current user...')

      // Check for admin impersonation session first
      const urlParams = new URLSearchParams(window.location.search)
      const impersonateUserId = urlParams.get('admin_impersonate')
      const sessionId = urlParams.get('session')
      const sessionDataParam = urlParams.get('session_data')

      if (impersonateUserId && sessionId) {
        console.log('🔍 Admin impersonation detected:', { impersonateUserId, sessionId })

        // If session data is in URL params and localStorage is empty, restore it
        if (sessionDataParam && !localStorage.getItem('admin_impersonation_data')) {
          try {
            const decodedSessionData = JSON.parse(decodeURIComponent(sessionDataParam))
            localStorage.setItem('admin_impersonation_data', JSON.stringify(decodedSessionData))
            console.log('🔄 Restored impersonation session from URL parameters')
          } catch (error) {
            console.error('❌ Failed to restore session data from URL:', error)
          }
        }

        const validation = await validateImpersonationSession(sessionId, parseInt(impersonateUserId))
        if (validation.valid && validation.user) {
          console.log('✅ Impersonation session validated, loading impersonated user')
          console.log('🔍 Impersonated user data:', validation.user)

          // Set up user object for impersonated user
          const impersonatedUser = {
            id: validation.user.id,
            email: validation.user.email,
            database_user: validation.user,
            account_type: 'impersonated',
            user_metadata: {
              user_id: validation.user.id,
              username: validation.user.username,
              full_name: validation.user.full_name,
              phone: validation.user.phone,
              telegram_id: validation.user.telegram_id,
              is_impersonated: true
            }
          }

          console.log('🎭 Setting impersonated user:', impersonatedUser)
          setUser(impersonatedUser)

          // Load dashboard data for the impersonated user
          const numericUserId = validation.user.id
          console.log('🚀 Loading dashboard data for impersonated user ID:', numericUserId)
          await loadDashboardData(numericUserId)
          await checkTelegramConnection(numericUserId)
          await loadUserSharePurchases(numericUserId)
          await loadUnreadMessageCount(numericUserId)
          await loadKYCStatus(numericUserId)

          setLoading(false)
          return
        } else {
          console.error('❌ Invalid impersonation session:', validation.error)
          alert('Invalid impersonation session. Redirecting to admin dashboard.')
          window.location.href = '/admin'
          return
        }
      }

      // PRIORITY: Use the user prop passed from login if available (but not during impersonation)
      let currentUser = propUser
      let userId = null

      if (currentUser) {
        console.log('✅ Using user prop from login:', currentUser)
        userId = currentUser?.database_user?.id || currentUser?.user_metadata?.user_id
        console.log('📊 User ID from prop:', userId)
        console.log('🔍 DEBUG: User prop structure:', {
          database_user_id: currentUser?.database_user?.id,
          user_metadata_user_id: currentUser?.user_metadata?.user_id,
          database_user: currentUser?.database_user,
          user_metadata: currentUser?.user_metadata
        })
      } else {
        // Fallback: Try to get user from localStorage
        try {
          const storedUser = localStorage.getItem('aureus_user')
          const storedTelegramUser = localStorage.getItem('aureus_telegram_user')
          const storedSession = localStorage.getItem('aureus_session')

        if (storedUser) {
          const parsedUser = JSON.parse(storedUser)

          // Create proper user object for email login
          currentUser = {
            id: parsedUser.auth_user_id || `db_${parsedUser.id}`,
            email: parsedUser.email,
            database_user: parsedUser,
            account_type: 'email',
            user_metadata: {
              telegram_id: parsedUser.telegram_id,
              username: parsedUser.username,
              full_name: parsedUser.full_name,
              phone: parsedUser.phone,
              country_of_residence: parsedUser.country_of_residence,
              user_id: parsedUser.id,
              is_email_user: true,
              telegram_connected: !!parsedUser.telegram_id
            }
          }
          userId = parsedUser.id
          console.log('✅ Email user loaded from localStorage:', currentUser, 'User ID:', userId, 'Type:', typeof userId)
          console.log('🔍 DEBUG: parsedUser structure:', {
            id: parsedUser.id,
            auth_user_id: parsedUser.auth_user_id,
            telegram_id: parsedUser.telegram_id,
            email: parsedUser.email
          })
        } else if (storedTelegramUser) {
          const parsedTelegramUser = JSON.parse(storedTelegramUser)
          currentUser = parsedTelegramUser
          // Fix: Extract user ID from multiple possible locations for Telegram users
          // Priority: database_user.id (users table ID) > user_metadata.user_id > telegram_id as fallback
          userId = parsedTelegramUser.database_user?.id ||
                   parsedTelegramUser.user_metadata?.user_id ||
                   parsedTelegramUser.id ||
                   parsedTelegramUser.telegram_id ||
                   parsedTelegramUser.database_user?.telegram_id
          console.log('✅ User loaded from telegram localStorage:', currentUser, 'User ID:', userId)
          console.log('🔍 DEBUG: Telegram user ID extraction:', {
            database_user_id: parsedTelegramUser.database_user?.id,
            user_metadata_user_id: parsedTelegramUser.user_metadata?.user_id,
            direct_id: parsedTelegramUser.id,
            telegram_id: parsedTelegramUser.telegram_id,
            database_telegram_id: parsedTelegramUser.database_user?.telegram_id,
            final_userId: userId,
            userId_type: typeof userId,
            full_user_object: parsedTelegramUser
          })
        }
        } catch (storageError) {
          console.log('⚠️ Could not load from localStorage, trying getCurrentUser')
        }

        // Fallback to getCurrentUser if localStorage fails
        if (!currentUser) {
        currentUser = await getCurrentUser()
        // CRITICAL FIX: Extract the correct database user ID with priority order
        userId = currentUser?.database_user?.id ||
                 currentUser?.user_metadata?.user_id ||
                 (currentUser?.user_metadata?.telegram_id ? currentUser.user_metadata.telegram_id : null)

        console.log('👤 Current user loaded from getCurrentUser:', currentUser, 'User ID:', userId)
        console.log('🔍 DEBUG: User ID extraction:', {
          database_user_id: currentUser?.database_user?.id,
          user_metadata_user_id: currentUser?.user_metadata?.user_id,
          telegram_id: currentUser?.user_metadata?.telegram_id,
          final_userId: userId,
          userId_type: typeof userId
        })
        }
      } // Close the } else { block

      setUser(currentUser)

      // Ensure we have a valid user ID for data loading
      if (!userId && currentUser) {
        userId = currentUser?.database_user?.id ||
                 currentUser?.user_metadata?.user_id ||
                 currentUser?.id
        console.log('🔧 Extracted user ID for data loading:', userId)
      }

      // IMMEDIATE FIX: Set user data from authenticated session to avoid database queries
      console.log('🔍 DEBUG: Checking user data conditions:', {
        hasCurrentUser: !!currentUser,
        hasDatabaseUser: !!currentUser?.database_user,
        hasUserMetadata: !!currentUser?.user_metadata,
        userId: userId,
        userIdType: typeof userId
      })

      // FIXED: Check for currentUser directly since database_user and user_metadata might not exist
      if (currentUser) {
        const userData = {
          telegram_id: currentUser?.database_user?.telegram_id || currentUser?.user_metadata?.telegram_id,
          username: currentUser?.database_user?.username || currentUser?.user_metadata?.username || 'User',
          full_name: currentUser?.database_user?.full_name || currentUser?.user_metadata?.full_name || 'User'
        }
        console.log('✅ User data set from session:', userData)
        setUserData(userData)

        // Set default commission data to avoid 406 errors
        setCommissionData({
          usdt_balance: 0,
          share_balance: 0,
          total_earned_usdt: 0,
          total_earned_shares: 0,
          escrowed_amount: 0,
          total_withdrawn: 0
        })
        console.log('✅ Default commission data set')

        // Set default share purchases to avoid errors
        setUserSharePurchases([])
        console.log('✅ Default share purchases set')

        // CRITICAL FIX: Set user state and load dashboard data
        console.log('🔍 DEBUG: About to check userId and currentUser:', {
          hasUserId: !!userId,
          userId: userId,
          hasCurrentUser: !!currentUser,
          userIdType: typeof userId
        })

        if (userId && currentUser) {
          console.log('🔄 Setting user state and loading dashboard data for user ID:', userId, 'Type:', typeof userId)

          // CRITICAL: Ensure userId is numeric before making database calls
          let numericUserId = null
          if (typeof userId === 'number' && !isNaN(userId)) {
            numericUserId = userId
          } else if (typeof userId === 'string') {
            const parsed = parseInt(userId, 10)
            if (!isNaN(parsed)) {
              numericUserId = parsed
            }
          }

          if (!numericUserId) {
            console.error('❌ Cannot convert user ID to numeric:', userId)
            console.log('🔍 DEBUG: User object details:', {
              currentUser,
              userId,
              userIdType: typeof userId,
              hasDatabase: !!currentUser?.database_user,
              hasMetadata: !!currentUser?.user_metadata
            })

            // Set user state and default data even without numeric ID
            setUser(currentUser)
            setDataLoading(false)
            return
          }

          console.log('✅ Using numeric user ID for database operations:', numericUserId)
          setUser(currentUser) // Set the user state so it displays in the UI

          console.log('🚀 About to call data loading functions...')
          await loadDashboardData(numericUserId)
          console.log('✅ loadDashboardData completed')

          await loadUserSecuritySettings(numericUserId)
          console.log('✅ loadUserSecuritySettings completed')

          await checkTelegramConnection(numericUserId)
          console.log('✅ checkTelegramConnection completed')

          await loadUserSharePurchases(numericUserId)
          console.log('✅ loadUserSharePurchases completed')

          await loadUnreadMessageCount(numericUserId)
          console.log('✅ loadUnreadMessageCount completed')

          await loadKYCStatus(numericUserId)
          console.log('✅ loadKYCStatus completed')
        } else {
          console.log('⚠️ No valid user ID found for data loading')
          console.log('🔍 DEBUG: Missing data:', { userId, currentUser: !!currentUser })

          // Set user state and default data even without user ID
          if (currentUser) {
            setUser(currentUser)

            // Set default user data to prevent UI errors
            const userData = {
              telegram_id: currentUser?.database_user?.telegram_id || currentUser?.user_metadata?.telegram_id,
              username: currentUser?.database_user?.username || currentUser?.user_metadata?.username || currentUser?.email?.split('@')[0] || 'User',
              full_name: currentUser?.database_user?.full_name || currentUser?.user_metadata?.full_name || 'User'
            }
            setUserData(userData)

            // Set default commission data
            setCommissionData({
              usdt_balance: 0,
              share_balance: 0,
              total_earned_usdt: 0,
              total_earned_shares: 0,
              escrowed_amount: 0,
              total_withdrawn: 0
            })

            // Set default dashboard data
            setDashboardData({
              totalShares: 0,
              shareValue: 0,
              futureDividends: 0,
              usdtCommissions: {
                totalEarned: 0,
                available: 0,
                escrowed: 0
              },
              shareCommissions: {
                totalShares: 0,
                currentValue: 0
              },
              accountBalance: 0,
              referralCount: 0,
              monthlyEarnings: 0,
              currentSharePrice: 5.00
            })
          }
          setDataLoading(false)
        }
      } // Close the if (currentUser?.database_user || currentUser?.user_metadata) block
    } catch (error) {
      console.error('❌ Error loading user:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load unread message count
  const loadUnreadMessageCount = async (userId: number) => {
    try {
      const serviceClient = getServiceRoleClient()
      const { count, error } = await serviceClient
        .from('internal_messages')
        .select('*', { count: 'exact', head: true })
        .eq('recipient_user_id', userId)
        .eq('is_read', false)

      if (error) {
        console.error('❌ Error loading unread message count:', error)
        return
      }

      setUnreadMessageCount(count || 0)
    } catch (error) {
      console.error('❌ Error loading unread message count:', error)
    }
  }

  // Load user share purchases
  const loadUserSharePurchases = async (userId: number) => {
    try {
      console.log('🔄 Loading share purchases for user:', userId)

      const serviceClient = getServiceRoleClient()
      const { data: purchases, error } = await serviceClient
        .from('aureus_share_purchases')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('❌ Error loading share purchases:', error)
        return
      }

      console.log('✅ Share purchases loaded:', purchases)
      setUserSharePurchases(purchases || [])
    } catch (error) {
      console.error('❌ Failed to load share purchases:', error)
    }
  }

  const loadDashboardData = async (userId: number) => {
    try {
      setDataLoading(true)
      console.log('🔄 Loading dashboard data for user:', userId, typeof userId)

      // Validate userId - handle both numeric and string IDs
      let validUserId = null
      if (typeof userId === 'number' && !isNaN(userId)) {
        validUserId = userId
      } else if (typeof userId === 'string') {
        const numericId = parseInt(userId, 10)
        if (!isNaN(numericId)) {
          validUserId = numericId
        }
      }

      if (!validUserId) {
        console.error('❌ Invalid user ID:', userId, 'Type:', typeof userId)
        // Set default data to prevent UI errors and show zero values instead of loading
        setCommissionData({
          usdt_balance: 0,
          share_balance: 0,
          total_earned_usdt: 0,
          total_earned_shares: 0,
          escrowed_amount: 0,
          total_withdrawn: 0
        })
        setUserSharePurchases([])

        // Set default dashboard data for new users
        setDashboardData({
          totalShares: 0,
          purchasedShares: 0,
          commissionShares: 0,
          shareValue: 0,
          futureDividends: 0,
          usdtCommissions: {
            totalEarned: 0,
            available: 0,
            escrowed: 0
          },
          shareCommissions: {
            totalShares: 0,
            currentValue: 0
          },
          accountBalance: 0,
          referralCount: 0,
          monthlyEarnings: 0,
          currentSharePrice: 5.00 // Default share price
        })

        setDataLoading(false)
        return
      }

      // Use the validated numeric user ID
      userId = validUserId
      console.log('✅ Using validated user ID:', userId, 'Type:', typeof userId)

      // Get service role client for database queries
      const serviceRoleClient = getServiceRoleClient()

      // Get current share price from active phase - use service role for this public data
      let currentSharePrice = 5.00 // Default fallback
      try {
        const { data: currentPhase, error: phaseError } = await supabase
          .from('investment_phases')
          .select('id, price_per_share, phase_name')
          .eq('is_active', true)
          .maybeSingle()

        if (phaseError) {
          console.log('⚠️ Could not fetch current phase, using default price:', phaseError.message)
        } else {
          currentSharePrice = currentPhase?.price_per_share || 5.00
        }
      } catch (error) {
        console.log('⚠️ Phase query failed, using default price:', error)
      }

      console.log('📊 Current share price:', currentSharePrice)

      // Fetch user shares from aureus_share_purchases - use service role client
      let sharesPurchases = []
      try {
        console.log('🔍 Fetching shares for user ID:', userId, typeof userId)
        const { data, error: sharesError } = await serviceRoleClient
          .from('aureus_share_purchases')
          .select('shares_purchased, total_amount, status')
          .eq('user_id', userId)
          .eq('status', 'active')

        if (sharesError) {
          console.log('⚠️ Could not fetch shares:', sharesError.message)
        } else {
          sharesPurchases = data || []
          console.log('✅ Shares data:', sharesPurchases)
        }
      } catch (error) {
        console.log('⚠️ Shares query failed:', error)
        sharesPurchases = []
      }

      // Fetch commission balance - use service role client to bypass RLS
      let commissionBalance = null
      try {
        console.log('🔍 Fetching commission balance for user ID:', userId, typeof userId)
        const { data, error: commissionError } = await serviceRoleClient
          .from('commission_balances')
          .select('usdt_balance, share_balance, total_earned_usdt, total_earned_shares, escrowed_amount, total_withdrawn')
          .eq('user_id', userId)
          .single()

        if (commissionError && commissionError.code !== 'PGRST116') {
          console.log('⚠️ Could not fetch commission balance:', commissionError.message)
        } else if (commissionError?.code === 'PGRST116') {
          console.log('ℹ️ No commission balance record found (expected for new users)')
        } else {
          commissionBalance = data
          console.log('✅ Commission balance data:', commissionBalance)
        }
      } catch (error) {
        console.log('⚠️ Commission balance query failed:', error)
        commissionBalance = null
      }

      // Fetch referral count
      let referrals = []
      try {
        console.log('🔍 Fetching referrals for user ID:', userId)
        const { data, error: referralsError } = await serviceRoleClient
          .from('referrals')
          .select('id')
          .eq('referrer_id', userId)

        if (referralsError) {
          console.log('⚠️ Could not fetch referrals:', referralsError.message)
        } else {
          referrals = data || []
          console.log('✅ Referrals data:', referrals)
        }
      } catch (error) {
        console.log('⚠️ Referrals query failed:', error)
        referrals = []
      }

      // Calculate purchased shares totals
      const purchasedShares = sharesPurchases?.reduce((sum, purchase) => sum + purchase.shares_purchased, 0) || 0
      const purchasedSharesValue = sharesPurchases?.reduce((sum, purchase) => sum + purchase.total_amount, 0) || 0

      // Commission data - separate USDT and Share commissions like Telegram bot
      const totalEarnedUSDT = commissionBalance?.total_earned_usdt || 0
      const totalEarnedShares = commissionBalance?.total_earned_shares || 0
      const availableUSDT = commissionBalance?.usdt_balance || 0
      const availableShares = commissionBalance?.share_balance || 0
      const escrowedAmount = commissionBalance?.escrowed_amount || 0

      // Calculate TOTAL shares owned (purchased + commission shares)
      const totalShares = purchasedShares + availableShares
      console.log('📊 Share calculation:', {
        purchasedShares,
        availableShares,
        totalShares,
        currentSharePrice
      })

      // Calculate TOTAL share value using current share price
      const shareValue = totalShares * currentSharePrice
      const shareCommissionValue = totalEarnedShares * currentSharePrice

      // Account balance = ONLY available USDT commission balance (liquid funds)
      // Share commissions are displayed separately in the share breakdown section
      const accountBalance = availableUSDT

      const referralCount = referrals?.length || 0

      // Future dividends will be calculated dynamically by the DividendCalculator component
      // This ensures the Future Dividends always matches the Dividend Calculator values
      const futureDividends = 0 // Will be updated by handleCalculatorChange

      setDashboardData({
        totalShares,
        purchasedShares, // Add purchased shares separately
        commissionShares: availableShares, // Add commission shares separately
        shareValue,
        futureDividends,
        // Separate commission types like Telegram bot
        usdtCommissions: {
          totalEarned: totalEarnedUSDT,
          available: availableUSDT,
          escrowed: escrowedAmount
        },
        shareCommissions: {
          totalShares: totalEarnedShares,
          currentValue: shareCommissionValue
        },
        accountBalance,
        referralCount,
        monthlyEarnings: (totalEarnedUSDT + shareCommissionValue) / 12, // Rough monthly average
        currentSharePrice
      })

      console.log('✅ Dashboard data loaded:', {
        totalShares,
        shareValue,
        futureDividends,
        usdtCommissions: {
          totalEarned: totalEarnedUSDT,
          available: availableUSDT,
          escrowed: escrowedAmount
        },
        shareCommissions: {
          totalShares: totalEarnedShares,
          currentValue: shareCommissionValue
        },
        accountBalance,
        referralCount,
        currentSharePrice
      })

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      // Set default data to prevent UI errors
      setCommissionData({
        usdt_balance: 0,
        share_balance: 0,
        total_earned_usdt: 0,
        total_earned_shares: 0,
        escrowed_amount: 0,
        total_withdrawn: 0
      })
      setUserSharePurchases([])

      // Set default dashboard data on error
      setDashboardData({
        totalShares: 0,
        purchasedShares: 0,
        commissionShares: 0,
        shareValue: 0,
        futureDividends: 0,
        usdtCommissions: {
          totalEarned: 0,
          available: 0,
          escrowed: 0
        },
        shareCommissions: {
          totalShares: 0,
          currentValue: 0
        },
        accountBalance: 0,
        referralCount: 0,
        monthlyEarnings: 0,
        currentSharePrice: 5.00 // Default share price
      })
    } finally {
      setDataLoading(false)
    }
  }

  const checkTelegramConnection = async (userId: number) => {
    try {
      console.log('🔍 IMMEDIATE FIX: Setting Telegram connection from authenticated user data')

      // CRITICAL FIX: Check if user exists before accessing properties
      if (!user) {
        console.log('❌ User object is undefined, cannot check Telegram connection')
        setTelegramUser(null)
        return
      }

      console.log('🔍 DEBUG: User object structure:', {
        hasUser: !!user,
        userKeys: user ? Object.keys(user) : [],
        hasDatabaseUser: !!user?.database_user,
        hasUserMetadata: !!user?.user_metadata,
        userMetadataKeys: user?.user_metadata ? Object.keys(user.user_metadata) : []
      });

      // IMMEDIATE FIX: Use data from authenticated user instead of database queries
      // Handle different user object structures after authentication fix
      const telegramId = user?.database_user?.telegram_id ||
                        user?.user_metadata?.telegram_id ||
                        user?.telegram_id;

      if (telegramId) {
        console.log('✅ User has Telegram ID from session:', telegramId)

        setTelegramUser({
          id: telegramId,
          telegram_id: telegramId,
          username: user?.database_user?.username || user?.user_metadata?.username || user?.username || 'User',
          full_name: user?.database_user?.full_name || user?.user_metadata?.full_name || user?.full_name || 'User'
        })
        console.log('✅ Telegram user set from session data')
      } else {
        console.log('❌ No Telegram connection found in session')
        setTelegramUser(null)
      }
    } catch (error) {
      console.error('Error checking Telegram connection:', error)
    }
  }

  const loadKYCStatus = async (userId: number) => {
    try {
      console.log('🔍 Loading KYC status for user via API:', userId)

      const response = await fetch(`/api/kyc-status?userId=${userId}`)
      const result = await response.json()

      if (!response.ok) {
        console.error('❌ Error fetching KYC status:', result.error)
        setKycStatus('not_started')
        setKycId(null)
        return
      }

      if (result.success) {
        console.log('✅ KYC status loaded via API:', result.kycStatus, 'ID:', result.kycId)
        setKycStatus(result.kycStatus)
        setKycId(result.kycId)
      } else {
        console.error('❌ Invalid KYC status response:', result)
        setKycStatus('not_started')
        setKycId(null)
      }
    } catch (error) {
      console.error('❌ Error loading KYC status:', error)
      setKycStatus('not_started')
      setKycId(null)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  const handleTelegramConnect = async (telegramId: string) => {
    if (!user?.database_user?.id) {
      throw new Error('User not authenticated')
    }

    // Validate telegram_id format (should be numeric)
    if (!/^\d{8,12}$/.test(telegramId)) {
      throw new Error('Invalid Telegram ID format')
    }

    try {
      // Call the telegram-link API endpoint
      const response = await fetch('/api/telegram-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.database_user.id,
          telegram_id: telegramId
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to connect Telegram account');
      }

      // Success - refresh the page to update the UI
      alert('✅ Telegram account connected successfully! The page will refresh to update your connection status.');
      window.location.reload();

    } catch (error) {
      console.error('Telegram connection error:', error);
      throw error;
    }
  }

  const handleTelegramDisconnect = async () => {
    if (!user?.database_user?.id || !telegramUser) {
      return
    }

    try {
      // Unlink the accounts by removing the user_id from telegram_users record
      const { error: updateError } = await supabase
        .from('telegram_users')
        .update({
          user_id: null,
          is_registered: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.database_user.id)

      if (updateError) {
        console.error('Error unlinking telegram_users:', updateError)
        throw new Error('Failed to unlink telegram_users record')
      }

      // Also clear telegram_id from users table
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          telegram_id: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.database_user.id)

      if (userUpdateError) {
        console.error('Error clearing users telegram_id:', userUpdateError)
        // Don't fail the whole process, but log the error
        console.warn('⚠️ Failed to clear telegram_id from users table, but telegram_users unlink was successful')
      }

      setTelegramUser(null)
      console.log('✅ Telegram account disconnected successfully')
    } catch (error) {
      console.error('❌ Telegram disconnect error:', error)
    }
  }

  if (loading || isCheckingCompliance) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0f1419]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">
            {isCheckingCompliance ? 'Checking legal document compliance...' : 'Loading your dashboard...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: '#050505',
      color: '#FFFFFF',
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Admin Impersonation Indicator */}
      <ImpersonationIndicator />

      {/* Mobile Header - Visible only on mobile */}
      <div className="lg:hidden bg-gray-800 border-b border-gray-700 sticky top-0 z-50" style={{
        backgroundColor: 'rgba(18, 18, 18, 0.9)',
        backdropFilter: 'blur(16px)',
        borderBottom: '1px solid #1E1E1E'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '16px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '32px',
              height: '32px',
              background: '#FFD700',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 0 18px #FFD70066'
            }}>
              <span style={{ color: '#000000', fontWeight: 'bold', fontSize: '14px' }}>A</span>
            </div>
            <span style={{ fontWeight: 'bold', fontSize: '18px', color: '#FFFFFF' }}>Aureus Alliance</span>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            style={{
              padding: '8px',
              borderRadius: '8px',
              backgroundColor: '#121212',
              border: '1px solid #1E1E1E',
              color: '#FFFFFF',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            aria-label="Toggle menu"
            onMouseEnter={(e) => e.target.style.backgroundColor = '#1E1E1E'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#121212'}
          >
            <svg style={{ width: '24px', height: '24px' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <div
            className="fixed inset-y-0 left-0 w-80 bg-gray-800 z-50 shadow-xl border-r border-gray-700"
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              {/* Mobile Menu Header */}
              <div className="p-6 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
                      <span className="text-black font-bold text-lg">A</span>
                    </div>
                    <span className="text-xl font-bold text-white">Aureus Alliance</span>
                  </div>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Mobile Dashboard Switcher - CRITICAL FIX */}
              {onSwitchDashboard && (
                <div className="p-4 border-b border-gray-700 bg-gray-900/50">
                  <DashboardSwitcher
                    currentDashboard="shareholder"
                    onSwitch={onSwitchDashboard}
                    user={user}
                  />
                </div>
              )}

              {/* Mobile Navigation */}
              <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
                {getNavigationItems().map((item) => {
                  const IconComponent = item.icon
                  const isActive = activeSection === item.id
                  const isLocked = mustCompleteLegalDocuments && item.id !== 'legal-documents'

                  return (
                    <div
                      key={item.id}
                      onClick={() => handleSectionChange(item.id)}
                      className={`
                        group relative p-3 rounded-lg cursor-pointer transition-all duration-200
                        ${isActive
                          ? 'bg-blue-600/20 border border-blue-500/30 text-blue-400'
                          : isLocked
                          ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed opacity-60'
                          : 'hover:bg-gray-700/50 text-gray-300 hover:text-white'
                        }
                        ${item.highlight ? 'ring-2 ring-yellow-500/30' : ''}
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`
                          p-2 rounded-lg transition-colors
                          ${isActive ? 'bg-blue-500/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'}
                        `}>
                          <IconComponent />
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm truncate">{item.label}</span>
                            {isLocked && (
                              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                            )}
                            {!isLocked && item.badge !== undefined && item.badge > 0 && (
                              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">
                                {item.badge}
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-400 mt-1 truncate">
                            {isLocked ? 'Complete legal documents to access' : item.description}
                          </p>
                        </div>
                      </div>

                      {item.highlight && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                      )}
                    </div>
                  )
                })}
              </nav>

              {/* Mobile User Info */}
              <div className="p-4 border-t border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <ProfilePictureUpload
                      currentImageUrl={user?.database_user?.profile_image_url}
                      userId={user?.database_user?.id || user?.id}
                      onImageUpdate={handleProfilePictureUpdate}
                      size="small"
                      editable={false}
                      userInitials={(user?.username || user?.email)?.charAt(0).toUpperCase() || 'U'}
                      userName={user?.username || user?.email?.split('@')[0] || 'User'}
                    />
                    <div>
                      <p className="text-white text-sm font-medium">
                        {user?.username || user?.email?.split('@')[0]}
                      </p>
                      <p className="text-gray-400 text-xs">Active</p>
                    </div>
                  </div>
                  <button
                    onClick={onLogout}
                    className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
                    title="Logout"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div style={{ display: 'flex', minHeight: '100vh', maxWidth: '100vw', overflowX: 'hidden' }} className="mobile-container">
        {/* Desktop Sidebar - Hidden on mobile */}
        <div style={{
          display: window.innerWidth >= 1024 ? 'flex' : 'none',
          width: '320px',
          backgroundColor: '#121212',
          borderRight: '1px solid #1E1E1E',
          flexDirection: 'column',
          backdropFilter: 'blur(16px)',
          boxShadow: '0px 0px 24px rgba(255, 215, 0, 0.15)'
        }}>
          {/* Desktop Logo */}
          <div style={{ padding: '24px', borderBottom: '1px solid #1E1E1E' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '40px',
                height: '40px',
                background: '#FFD700',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 0 18px #FFD70066'
              }}>
                <span style={{ color: '#000000', fontWeight: 'bold', fontSize: '18px' }}>A</span>
              </div>
              <span style={{ fontSize: '20px', fontWeight: 'bold', color: '#FFFFFF' }}>Aureus Alliance</span>
            </div>
          </div>

          {/* Dashboard Switcher */}
          {onSwitchDashboard && (
            <div style={{ padding: '16px', borderBottom: '1px solid #1E1E1E' }}>
              <DashboardSwitcher
                currentDashboard="shareholder"
                onSwitch={onSwitchDashboard}
                user={user}
              />
            </div>
          )}



        {/* Enhanced Navigation */}
        <nav style={{
          flex: 1,
          padding: '16px',
          overflowY: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }}>
          {getNavigationItems().map((item) => {
            const IconComponent = item.icon
            const isActive = activeSection === item.id
            const isLocked = mustCompleteLegalDocuments && item.id !== 'legal-documents'

            return (
              <div
                key={item.id}
                onClick={() => handleSectionChange(item.id)}
                style={{
                  position: 'relative',
                  padding: '12px',
                  borderRadius: '8px',
                  cursor: isLocked ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s ease',
                  backgroundColor: isActive ? 'rgba(255, 215, 0, 0.1)' : 'transparent',
                  border: isActive ? '1px solid rgba(255, 215, 0, 0.3)' : '1px solid transparent',
                  color: isActive ? '#FFD700' : isLocked ? '#666666' : '#B0B0B0',
                  opacity: isLocked ? 0.6 : 1,
                  boxShadow: item.highlight ? '0 0 0 2px rgba(255, 215, 0, 0.3)' : 'none'
                }}
                onMouseEnter={(e) => {
                  if (!isActive && !isLocked) {
                    e.target.style.backgroundColor = 'rgba(30, 30, 30, 0.5)'
                    e.target.style.color = '#FFFFFF'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive && !isLocked) {
                    e.target.style.backgroundColor = 'transparent'
                    e.target.style.color = '#B0B0B0'
                  }
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <div style={{
                    padding: '8px',
                    borderRadius: '8px',
                    backgroundColor: isActive ? 'rgba(255, 215, 0, 0.2)' : 'rgba(30, 30, 30, 0.5)',
                    transition: 'all 0.2s ease'
                  }}>
                    <IconComponent />
                  </div>

                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span style={{ fontWeight: '500', fontSize: '14px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{item.label}</span>
                      {item.badge !== undefined && item.badge > 0 && (
                        <span style={{
                          backgroundColor: '#dc2626',
                          color: 'white',
                          fontSize: '12px',
                          padding: '4px 8px',
                          borderRadius: '20px',
                          marginLeft: '8px'
                        }}>
                          {item.badge}
                        </span>
                      )}
                    </div>
                    <p style={{
                      fontSize: '12px',
                      color: '#9ca3af',
                      marginTop: '4px',
                      margin: 0,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>{item.description}</p>
                  </div>
                </div>

                {item.highlight && (
                  <div style={{
                    position: 'absolute',
                    top: '-4px',
                    right: '-4px',
                    width: '12px',
                    height: '12px',
                    backgroundColor: '#FFD700',
                    borderRadius: '50%',
                    animation: 'pulse 2s infinite'
                  }}></div>
                )}
              </div>
            )
          })}
        </nav>

        {/* User Info at Bottom */}
        <div style={{ padding: '16px', borderTop: '1px solid #1E1E1E' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <ProfilePictureUpload
                currentImageUrl={user?.database_user?.profile_image_url}
                userId={user?.database_user?.id || user?.id}
                onImageUpdate={handleProfilePictureUpdate}
                size="small"
                editable={false}
                userInitials={(user?.username || user?.email)?.charAt(0).toUpperCase() || 'U'}
                userName={user?.username || user?.email?.split('@')[0] || 'User'}
              />
              <div>
                <p style={{ color: '#FFFFFF', fontSize: '14px', fontWeight: '500', margin: 0 }}>
                  {user?.username || user?.email?.split('@')[0]}
                </p>
                <p style={{ color: '#B0B0B0', fontSize: '12px', margin: 0 }}>Active</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              style={{
                background: 'none',
                border: 'none',
                color: '#B0B0B0',
                cursor: 'pointer',
                padding: '4px',
                transition: 'color 0.2s ease'
              }}
              title="Logout"
              onMouseEnter={(e) => e.target.style.color = '#FFD700'}
              onMouseLeave={(e) => e.target.style.color = '#B0B0B0'}
            >
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, padding: '32px', backgroundColor: '#050505', maxWidth: '100%', overflowX: 'hidden', boxSizing: 'border-box' }} className="mobile-dashboard-content">
        {/* General Messages */}
        {message && (
          <div className={`rounded-lg p-4 mb-6 ${
            message.type === 'success'
              ? 'bg-green-900/20 border border-green-500/30 text-green-300'
              : message.type === 'warning'
              ? 'bg-yellow-900/20 border border-yellow-500/30 text-yellow-300'
              : 'bg-red-900/20 border border-red-500/30 text-red-300'
          }`}>
            <p className="text-sm">
              {message.type === 'success' ? '✅' : message.type === 'warning' ? '⚠️' : '❌'} {message.text}
            </p>
          </div>
        )}

        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '32px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
            {/* Profile Picture */}
            <ProfilePictureUploadEnhanced
              currentImageUrl={user?.database_user?.profile_image_url}
              userId={user?.database_user?.id || user?.id}
              onImageUpdate={handleProfilePictureUpdate}
              size="large"
              autoSave={false}
              showSaveButton={true}
              editable={true}
              userInitials={(
                user?.database_user?.full_name ||
                user?.user_metadata?.full_name ||
                user?.database_user?.first_name ||
                user?.username ||
                user?.email?.split('@')[0] ||
                'User'
              ).charAt(0).toUpperCase()}
              userName={
                user?.database_user?.full_name ||
                user?.user_metadata?.full_name ||
                user?.database_user?.first_name ||
                user?.username ||
                user?.email?.split('@')[0] ||
                'User'
              }
            />

            {/* Welcome Text */}
            <div>
              <h1 style={{ fontSize: '32px', fontWeight: 'bold', color: '#FFFFFF', margin: 0 }}>Dashboard</h1>
              <p style={{ color: '#B0B0B0', marginTop: '8px', margin: 0 }}>
                Welcome back, {
                  user?.database_user?.full_name ||
                  user?.user_metadata?.full_name ||
                  user?.database_user?.first_name ||
                  user?.username ||
                  user?.email?.split('@')[0] ||
                  'User'
                }!
              </p>
              <p style={{ color: '#6B7280', fontSize: '14px', marginTop: '4px', margin: 0 }}>
                Click your profile picture to update it
              </p>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {/* Notification Badge with Dropdown */}
            <div style={{ position: 'relative' }}>
              <NotificationBadge
                userId={user?.database_user?.id || user?.id}
                onClick={() => setShowNotificationDropdown(!showNotificationDropdown)}
                className="p-2 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors"
              />

              <NotificationDropdown
                userId={user?.database_user?.id || user?.id}
                isOpen={showNotificationDropdown}
                onClose={() => setShowNotificationDropdown(false)}
                onViewAll={() => {
                  setActiveSection('notifications')
                  setShowNotificationDropdown(false)
                }}
              />
            </div>

            <div style={{
              backgroundColor: '#121212',
              border: '1px solid #1E1E1E',
              padding: '16px 24px',
              borderRadius: '12px',
              boxShadow: '0px 0px 24px rgba(255, 215, 0, 0.15)'
            }}>
              <span style={{ color: '#B0B0B0', fontSize: '14px', display: 'block' }}>Account Balance</span>
              <p style={{ color: '#FFFFFF', fontWeight: 'bold', fontSize: '18px', margin: 0 }}>
                {"$" + (dataLoading ? '...' : dashboardData.accountBalance.toFixed(2))}
              </p>
            </div>

            {/* Premium Logout Button */}
            <button
              onClick={handleLogout}
              style={{
                backgroundColor: '#121212',
                border: '1px solid #FFD700',
                color: '#FFD700',
                padding: '12px 20px',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'all 0.3s ease',
                boxShadow: '0 0 18px #FFD70066'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#FFD700';
                e.currentTarget.style.color = '#000000';
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 0 28px #FFD70088';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#121212';
                e.currentTarget.style.color = '#FFD700';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 0 18px #FFD70066';
              }}
              title="Sign out of your account"
            >
              <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Logout
            </button>
          </div>
        </div>



        {/* Conditional Content Based on Active Section */}
        {activeSection === 'notifications' ? (
          <NotificationCenter
            userId={user?.database_user?.id || user?.id}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        ) : activeSection === 'messages' ? (
          <MessageDashboard
            userId={user?.database_user?.id || user?.id}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />

        ) : activeSection === 'overview' ? (
          <>
            {/* Migration Status Banner removed - migration system simplified */}

            {/* Shareholder Dashboard Content */}
            {(() => {
              // IMPORTANT: UserDashboard component ALWAYS shows shareholder content
              // regardless of user's actual role, because this IS the shareholder dashboard

              console.log('🔍 UserDashboard overview - Always showing shareholder content')

              // Always show shareholder content - no role-based switching in UserDashboard
              // Users who want affiliate features should switch to AffiliateDashboard

              return (
                <>
                    {/* Shareholder Dashboard Cards - Separated Share Categories */}
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
                      gap: '20px',
                      marginBottom: '30px'
                    }}>
                      {/* Purchased Shares */}
                      <div style={{
                        backgroundColor: 'rgba(31, 41, 55, 0.9)',
                        borderRadius: '12px',
                        padding: '20px',
                        textAlign: 'center',
                        border: '1px solid #374151',
                        position: 'relative'
                      }}>
                        <div style={{ fontSize: '20px', marginBottom: '8px' }}>💰</div>
                        <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#3b82f6', margin: '0 0 5px 0' }}>
                          {dataLoading ? '...' : (dashboardData.purchasedShares || 0).toLocaleString()}
                        </p>
                        <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0 }}>Purchased Shares</p>
                        <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 0' }}>Direct Investment</p>
                      </div>

                      {/* Commission Shares */}
                      <div style={{
                        backgroundColor: 'rgba(31, 41, 55, 0.9)',
                        borderRadius: '12px',
                        padding: '20px',
                        textAlign: 'center',
                        border: '1px solid #374151',
                        position: 'relative'
                      }}>
                        <div style={{ fontSize: '20px', marginBottom: '8px' }}>🎁</div>
                        <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', margin: '0 0 5px 0' }}>
                          {dataLoading ? '...' : (dashboardData.commissionShares || 0).toLocaleString()}
                        </p>
                        <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0 }}>Commission Shares</p>
                        <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 0' }}>Referral Rewards</p>
                      </div>

                      {/* Total Shares */}
                      <div style={{
                        backgroundColor: 'rgba(31, 41, 55, 0.9)',
                        borderRadius: '12px',
                        padding: '20px',
                        textAlign: 'center',
                        border: '2px solid #f59e0b',
                        position: 'relative'
                      }}>
                        <div style={{ fontSize: '20px', marginBottom: '8px' }}>🏆</div>
                        <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#f59e0b', margin: '0 0 5px 0' }}>
                          {dataLoading ? '...' : dashboardData.totalShares.toLocaleString()}
                        </p>
                        <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0 }}>Total Shares</p>
                        <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 0' }}>Combined Holdings</p>
                      </div>

                      {/* Portfolio Value */}
                      <div style={{
                        backgroundColor: 'rgba(31, 41, 55, 0.9)',
                        borderRadius: '12px',
                        padding: '20px',
                        textAlign: 'center',
                        border: '1px solid #374151'
                      }}>
                        <div style={{ fontSize: '20px', marginBottom: '8px' }}>📊</div>
                        <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', margin: '0 0 5px 0' }}>
                          {"$" + (dataLoading ? '...' : dashboardData.shareValue.toLocaleString())}
                        </p>
                        <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0 }}>Portfolio Value</p>
                        <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 0' }}>Current Market Value</p>
                      </div>

                      {/* Future Dividends */}
                      <div style={{
                        backgroundColor: 'rgba(31, 41, 55, 0.9)',
                        borderRadius: '12px',
                        padding: '20px',
                        textAlign: 'center',
                        border: '1px solid #374151'
                      }}>
                        <div style={{ fontSize: '20px', marginBottom: '8px' }}>💎</div>
                        <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#f59e0b', margin: '0 0 5px 0' }}>
                          {"$" + (dataLoading ? '...' : dashboardData.futureDividends.toLocaleString())}
                        </p>
                        <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0 }}>Future Dividends</p>
                        <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 0' }}>Annual Projection</p>
                      </div>

                      {/* Current Share Price */}
                      <div style={{
                        backgroundColor: 'rgba(31, 41, 55, 0.9)',
                        borderRadius: '12px',
                        padding: '20px',
                        textAlign: 'center',
                        border: '1px solid #374151'
                      }}>
                        <div style={{ fontSize: '20px', marginBottom: '8px' }}>💲</div>
                        <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#8b5cf6', margin: '0 0 5px 0' }}>
                          {"$" + (dataLoading ? '...' : dashboardData.currentSharePrice.toFixed(2))}
                        </p>
                        <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0 }}>Current Share Price</p>
                        <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 0' }}>Per Share Value</p>
                      </div>
                    </div>

                    {/* Share Categories Explanation */}
                    <div style={{
                      backgroundColor: 'rgba(17, 24, 39, 0.8)',
                      borderRadius: '12px',
                      padding: '20px',
                      marginBottom: '30px',
                      border: '1px solid #374151'
                    }}>
                      <h3 style={{ color: '#f3f4f6', fontSize: '18px', fontWeight: 'bold', marginBottom: '15px', textAlign: 'center' }}>
                        📋 Share Portfolio Breakdown
                      </h3>
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                        gap: '15px'
                      }}>
                        <div style={{ textAlign: 'center', padding: '10px' }}>
                          <div style={{ fontSize: '16px', marginBottom: '5px' }}>💰 <strong style={{ color: '#3b82f6' }}>Purchased Shares</strong></div>
                          <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0, lineHeight: '1.4' }}>
                            Shares you bought directly with your own money. These represent your direct financial investment.
                          </p>
                        </div>
                        <div style={{ textAlign: 'center', padding: '10px' }}>
                          <div style={{ fontSize: '16px', marginBottom: '5px' }}>🎁 <strong style={{ color: '#10b981' }}>Commission Shares</strong></div>
                          <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0, lineHeight: '1.4' }}>
                            Shares earned through referrals and affiliate commissions. These are rewards for bringing new shareholders.
                          </p>
                        </div>
                        <div style={{ textAlign: 'center', padding: '10px' }}>
                          <div style={{ fontSize: '16px', marginBottom: '5px' }}>🏆 <strong style={{ color: '#f59e0b' }}>Total Shares</strong></div>
                          <p style={{ fontSize: '13px', color: '#9ca3af', margin: 0, lineHeight: '1.4' }}>
                            Your complete shareholding including both purchased and earned shares. All shares receive equal dividends.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Mining Dividend Calculator - Always show for shareholders */}
                    <DividendCalculator
                      userShares={dashboardData.totalShares}
                      onCalculationChange={handleCalculatorChange}
                    />
                  </>
                )
            })()}

        {/* Shareholder Additional Content */}
        {(() => {
          // IMPORTANT: UserDashboard component ALWAYS shows shareholder content
          // No role-based switching - this IS the shareholder dashboard

          console.log('🔍 UserDashboard additional content - Always showing shareholder content')

          // Always show shareholder additional content (none currently needed)
          return null; // No additional content needed for shareholders currently

        })()}
          </>
        ) : activeSection === 'referrals' ? (
          <div className="space-y-6">
            <ReferralCenter
              userId={user?.database_user?.id || user?.id || 0}
              className="bg-gray-800 rounded-lg border border-gray-700 p-6"
            />
            <CommissionWithdrawal
              userId={user?.database_user?.id || user?.id || 0}
              availableUSDT={dashboardData.usdtCommissions?.available || 0}
              availableShares={dashboardData.shareCommissions?.totalShares || 0}
              currentSharePrice={dashboardData.currentSharePrice || 1}
              onWithdrawalRequest={() => {
                // Refresh dashboard data after withdrawal request
                loadDashboardData();
              }}
            />
          </div>
        ) : activeSection === 'dividends' ? (
          <ComprehensiveDividendsCalculator
            userShares={dashboardData.totalShares}
            currentPhase={null}
            userId={user?.database_user?.id || user?.id || 0}
          />
        ) : activeSection === 'portfolio' ? (
          <ComprehensivePortfolio
            user={user}
            currentPhase={{ price_per_share: dashboardData.currentSharePrice }}
            calculatorData={currentCalculatorData}
          />
        ) : activeSection === 'shares' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-2">📊 My Shares</h2>
            <p className="text-gray-400 mb-4">Shares section temporarily unavailable.</p>
            <button
              onClick={() => setShowPurchaseFlow(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Purchase Shares
            </button>
          </div>
        ) : activeSection === 'purchase-shares' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">🛒 Purchase Gold Shares</h2>
            <p className="text-gray-400 mb-6">
              Purchase Aureus Alliance Holdings gold mining shares. Choose your share purchase amount and payment method.
            </p>
            <button
              onClick={() => setShowPurchaseFlow(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Start Purchase Process
            </button>
          </div>
        ) : activeSection === 'company-presentation' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">📋 Company Presentation</h2>
            <p className="text-gray-400 mb-6">
              Learn about Aureus Alliance Holdings, our mining operations, and share purchase opportunities.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-700 rounded-lg p-6 hover:bg-gray-600 transition-colors">
                <div className="text-center">
                  <div className="text-4xl mb-4">🇬🇧</div>
                  <h3 className="text-lg font-semibold text-white mb-3">English Presentation</h3>
                  <p className="text-gray-300 text-sm mb-4">Complete company overview and mining operations details</p>
                  <a
                    href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Eng.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    📄 Download PDF
                  </a>
                </div>
              </div>
              <div className="bg-gray-700 rounded-lg p-6 hover:bg-gray-600 transition-colors">
                <div className="text-center">
                  <div className="text-4xl mb-4">🇮🇳</div>
                  <h3 className="text-lg font-semibold text-white mb-3">Hindi Presentation</h3>
                  <p className="text-gray-300 text-sm mb-4">हिंदी में कंपनी की पूरी जानकारी और खनन संचालन विवरण</p>
                  <a
                    href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Hindi.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    📄 Download PDF
                  </a>
                </div>
              </div>
              <div className="bg-gray-700 rounded-lg p-6 hover:bg-gray-600 transition-colors">
                <div className="text-center">
                  <div className="text-4xl mb-4">🇮🇩</div>
                  <h3 className="text-lg font-semibold text-white mb-3">Indonesian Presentation</h3>
                  <p className="text-gray-300 text-sm mb-4">Gambaran lengkap perusahaan dan detail operasi pertambangan</p>
                  <a
                    href="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/marketing-materials/Aureus%20Presentation%20Plan%20-%20Indonesian.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    📄 Download PDF
                  </a>
                </div>
              </div>
            </div>
          </div>
        ) : activeSection === 'mining-operations' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-6">⛏️ Mining Operations</h2>

            {/* Main Operations Overview */}
            <div className="bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg p-6 mb-8">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-white mb-4">🌍 Network of Eco-Friendly Wash Plants</h3>
                <p className="text-gray-200 text-lg leading-relaxed">
                  We are building a network of over <span className="text-yellow-400 font-bold">200 eco-friendly wash plants across Africa</span>.
                  These plants process mine tailings to extract fine traces of <span className="text-yellow-400 font-bold">gold, dust, and nuggets</span> —
                  producing an estimated <span className="text-yellow-400 font-bold">100 tons per year</span>.
                </p>
              </div>
              <div className="text-center">
                <p className="text-gray-300 text-base">
                  Our operations are driven by <span className="text-green-400 font-semibold">environmental sustainability</span> and
                  <span className="text-blue-400 font-semibold"> social responsibility</span>, ensuring long-term growth while uplifting the communities around us.
                </p>
              </div>
            </div>

            {/* Timeline and Expansion */}
            <div className="mb-8">
              <h3 className="text-xl font-bold text-white mb-6 text-center">📅 Timeline and Expansion</h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div className="bg-gray-700 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">🚀 Operational Timeline</h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <span className="text-yellow-400 font-bold">January 2026:</span>
                      <span className="text-gray-300">Two wash plants operational</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-yellow-400 font-bold">June 2026:</span>
                      <span className="text-gray-300">Expanding to 10 plants</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-yellow-400 font-bold">2030:</span>
                      <span className="text-gray-300">200+ plants across 5,000 hectares</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">💰 Dividend Schedule</h4>
                  <div className="space-y-2 text-sm">
                    <p className="text-gray-300">
                      <span className="text-blue-400 font-semibold">Years 1-2:</span> Quarterly payments
                    </p>
                    <p className="text-gray-300">
                      <span className="text-blue-400 font-semibold">Years 3-4:</span> Biannual payments
                    </p>
                    <p className="text-gray-300">
                      <span className="text-blue-400 font-semibold">Year 6+:</span> Annual payments
                    </p>
                    <p className="text-xs text-gray-400 mt-2">
                      Aligned with our long-term growth strategy
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Dividend Projections */}
            <div className="mb-8">
              <h3 className="text-xl font-bold text-white mb-6 text-center">📈 Dividend Projections</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-gradient-to-br from-green-800 to-green-700 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-white">2026</div>
                  <div className="text-green-200 text-lg font-semibold">$200 per share</div>
                  <div className="text-green-300 text-xs">Estimated</div>
                </div>
                <div className="bg-gradient-to-br from-blue-800 to-blue-700 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-white">2027</div>
                  <div className="text-blue-200 text-lg font-semibold">$500 per share</div>
                  <div className="text-blue-300 text-xs">Estimated</div>
                </div>
                <div className="bg-gradient-to-br from-purple-800 to-purple-700 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-white">2028</div>
                  <div className="text-purple-200 text-lg font-semibold">$1,000 per share</div>
                  <div className="text-purple-300 text-xs">Projected</div>
                </div>
                <div className="bg-gradient-to-br from-orange-800 to-orange-700 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-white">2029</div>
                  <div className="text-orange-200 text-lg font-semibold">$2,000 per share</div>
                  <div className="text-orange-300 text-xs">Around</div>
                </div>
                <div className="bg-gradient-to-br from-yellow-800 to-yellow-700 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-white">2030</div>
                  <div className="text-yellow-200 text-lg font-semibold">$4,000 per share</div>
                  <div className="text-yellow-300 text-xs">Approximately</div>
                </div>
                <div className="bg-gradient-to-br from-gray-700 to-gray-600 rounded-lg p-4 text-center flex flex-col justify-center">
                  <div className="text-white text-sm font-semibold">200+ Plants</div>
                  <div className="text-gray-300 text-xs">5,000 hectares</div>
                  <div className="text-gray-400 text-xs">Across Africa</div>
                </div>
              </div>
            </div>

            {/* Multi-Resource Operations */}
            <div className="mb-8">
              <h3 className="text-xl font-bold text-white mb-6 text-center">💎 Multi-Resource Operations</h3>
              <div className="bg-gray-700 rounded-lg p-6">
                <p className="text-gray-300 text-center mb-4">
                  These results are powered not only by <span className="text-yellow-400 font-bold">gold</span>, but also by:
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
                  <div className="bg-gray-600 rounded-lg p-3 text-center">
                    <div className="text-orange-400 text-lg">🥉</div>
                    <div className="text-white text-sm font-semibold">Copper</div>
                  </div>
                  <div className="bg-gray-600 rounded-lg p-3 text-center">
                    <div className="text-gray-400 text-lg">⚙️</div>
                    <div className="text-white text-sm font-semibold">Iron</div>
                  </div>
                  <div className="bg-gray-600 rounded-lg p-3 text-center">
                    <div className="text-gray-300 text-lg">🥈</div>
                    <div className="text-white text-sm font-semibold">Platinum</div>
                  </div>
                  <div className="bg-gray-600 rounded-lg p-3 text-center">
                    <div className="text-gray-200 text-lg">🥈</div>
                    <div className="text-white text-sm font-semibold">Silver</div>
                  </div>
                  <div className="bg-gray-600 rounded-lg p-3 text-center">
                    <div className="text-blue-400 text-lg">🔷</div>
                    <div className="text-white text-sm font-semibold">Cobalt</div>
                  </div>
                  <div className="bg-gray-600 rounded-lg p-3 text-center">
                    <div className="text-blue-200 text-lg">💎</div>
                    <div className="text-white text-sm font-semibold">Diamonds</div>
                  </div>
                  <div className="bg-gray-600 rounded-lg p-3 text-center">
                    <div className="text-yellow-400 text-lg">🥇</div>
                    <div className="text-white text-sm font-semibold">Gold</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Diversified Business */}
            <div className="bg-gradient-to-r from-blue-800 to-purple-800 rounded-lg p-6">
              <h3 className="text-xl font-bold text-white mb-4 text-center">🚀 Beyond Mining</h3>
              <p className="text-gray-200 text-center mb-4">
                Aureus is also expanding into <span className="text-blue-300 font-semibold">property development</span>,
                <span className="text-purple-300 font-semibold"> technology</span>, and
                <span className="text-green-300 font-semibold"> artificial intelligence</span> through partnerships with
                <span className="text-yellow-300 font-bold"> SUN (Smart United Network)</span> and other collaborators.
              </p>
              <div className="text-center">
                <span className="inline-block bg-yellow-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  🎯 Multiple Streams of Profit for All Shareholders
                </span>
              </div>
            </div>
          </div>
        ) : activeSection === 'community-relations' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">🏘️ Community Relations</h2>
            <p className="text-gray-400 mb-6">
              Our commitment to community development and stakeholder engagement.
            </p>
            <div className="space-y-4">
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🤝 Community Meetings</h3>
                <p className="text-gray-300 text-sm">Regular engagement with local communities</p>
              </div>
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🏗️ Development Plans</h3>
                <p className="text-gray-300 text-sm">Infrastructure and community development initiatives</p>
              </div>
            </div>
          </div>
        ) : activeSection === 'support-center' ? (
          <div className="space-y-6">
            {/* Support Center Header */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h2 className="text-2xl font-bold text-white mb-4">🆘 Support Center</h2>
              <p className="text-gray-400 mb-6">
                Get comprehensive support through multiple channels - live chat, tickets, training, and private consultations.
              </p>
            </div>

            {/* Support Options Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 hover:border-yellow-500 transition-colors cursor-pointer"
                   onClick={() => setActiveSection('live-chat')}>
                <div className="text-2xl mb-2">💬</div>
                <h3 className="text-lg font-semibold text-white mb-2">Live Chat</h3>
                <p className="text-gray-400 text-sm">Instant messaging with support agents</p>
              </div>

              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 hover:border-yellow-500 transition-colors cursor-pointer"
                   onClick={() => setActiveSection('support-tickets')}>
                <div className="text-2xl mb-2">🎫</div>
                <h3 className="text-lg font-semibold text-white mb-2">Support Tickets</h3>
                <p className="text-gray-400 text-sm">Create and track support requests</p>
              </div>



              <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 hover:border-yellow-500 transition-colors cursor-pointer"
                   onClick={() => setActiveSection('consultations')}>
                <div className="text-2xl mb-2">📅</div>
                <h3 className="text-lg font-semibold text-white mb-2">Private Consultations</h3>
                <p className="text-gray-400 text-sm">Book one-on-one expert sessions</p>
              </div>
            </div>

            {/* Quick Access Live Chat */}
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Quick Chat</h3>
              <UserSupportChat />
            </div>
          </div>
        ) : activeSection === 'live-chat' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">💬 Live Chat Support</h2>
            <p className="text-gray-400 mb-6">
              Connect instantly with our support team for immediate assistance.
            </p>
            <UserSupportChat />
          </div>
        ) : activeSection === 'support-tickets' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="text-white">
              <TicketingSystem />
            </div>
          </div>

        ) : activeSection === 'consultations' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="text-white">
              <ConsultationBooking />
            </div>
          </div>
        ) : activeSection === 'settings' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">⚙️ Settings</h2>
            <p className="text-gray-400 mb-6">
              Manage your account preferences and security settings.
            </p>
            {/* Settings Messages */}
            {settingsMessage && (
              <div className={`rounded-lg p-4 mb-4 ${
                settingsMessage.type === 'success'
                  ? 'bg-green-900/20 border border-green-500/30 text-green-300'
                  : 'bg-red-900/20 border border-red-500/30 text-red-300'
              }`}>
                <p className="text-sm">
                  {settingsMessage.type === 'success' ? '✅' : '❌'} {settingsMessage.text}
                </p>
              </div>
            )}

            <div className="space-y-4">
              {/* Profile Picture Section */}
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">📷 Profile Picture</h3>
                <p className="text-gray-300 text-sm mb-4">Upload a professional profile picture</p>

                <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
                  <ProfilePictureUpload
                    currentImageUrl={user?.database_user?.profile_image_url}
                    userId={user?.database_user?.id || user?.id}
                    onImageUpdate={handleProfilePictureUpdate}
                    size="large"
                    editable={true}
                    showSaveButton={true}
                    autoSave={false}
                    userInitials={(
                      user?.database_user?.full_name ||
                      user?.user_metadata?.full_name ||
                      user?.database_user?.first_name ||
                      user?.username ||
                      user?.email?.split('@')[0] ||
                      'User'
                    ).charAt(0).toUpperCase()}
                    userName={
                      user?.database_user?.full_name ||
                      user?.user_metadata?.full_name ||
                      user?.database_user?.first_name ||
                      user?.username ||
                      user?.email?.split('@')[0] ||
                      'User'
                    }
                  />
                  <div>
                    <p className="text-white font-medium mb-2">Upload Guidelines:</p>
                    <ul className="text-gray-300 text-sm space-y-1">
                      <li>• Use a clear, professional photo</li>
                      <li>• Maximum file size: 5MB</li>
                      <li>• Supported formats: JPG, PNG, GIF</li>
                      <li>• Square images work best</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">👤 Profile Settings</h3>
                <p className="text-gray-300 text-sm mb-4">Update your personal information</p>

                {/* Display Current Profile Information */}
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Full Name</label>
                      <div className="bg-gray-600 rounded-lg px-3 py-2 text-white">
                        {user?.database_user?.full_name || 'Not set'}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Email</label>
                      <div className="bg-gray-600 rounded-lg px-3 py-2 text-white">
                        {user?.database_user?.email || user?.email || 'Not set'}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
                      <div className="bg-gray-600 rounded-lg px-3 py-2 text-white">
                        {user?.database_user?.phone || 'Not set'}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Country</label>
                      <div className="bg-gray-600 rounded-lg px-3 py-2 text-white">
                        {(() => {
                          const countryCode = user?.database_user?.country_of_residence;
                          const countryMap: { [key: string]: string } = {
                            'ZAF': 'South Africa',
                            'NAM': 'Namibia',
                            'SWZ': 'Eswatini',
                            'BWA': 'Botswana',
                            'ZWE': 'Zimbabwe',
                            'MOZ': 'Mozambique',
                            'LSO': 'Lesotho',
                            'USA': 'United States',
                            'GBR': 'United Kingdom',
                            'CAN': 'Canada',
                            'AUS': 'Australia',
                            'DEU': 'Germany',
                            'FRA': 'France'
                          };
                          return countryMap[countryCode] || countryCode || 'Not set';
                        })()}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Username</label>
                      <div className="flex items-center gap-2">
                        <div className="bg-gray-600 rounded-lg px-3 py-2 text-white flex-1">
                          {user?.database_user?.username || 'Not set'}
                        </div>
                        <button
                          onClick={() => setShowUsernameEditor(true)}
                          className="bg-yellow-600 hover:bg-yellow-700 text-white text-sm px-3 py-2 rounded-lg transition-colors"
                          title="Change username"
                        >
                          ✏️
                        </button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Account Type</label>
                      <div className="bg-gray-600 rounded-lg px-3 py-2 text-white">
                        {user?.account_type === 'telegram' ? 'Telegram' : 'Email'} Account
                      </div>
                    </div>
                  </div>

                  {/* Profile Description Section */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Profile Description</label>
                    <div className="bg-gray-600 rounded-lg px-3 py-2 text-white min-h-[60px]">
                      {user?.database_user?.profile_description || 'No description set'}
                    </div>
                  </div>

                  <div className="pt-2">
                    <button
                      onClick={openProfileEditor}
                      className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      Edit Profile
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🔒 Security</h3>
                <p className="text-gray-300 text-sm mb-4">Password and security preferences</p>

                <div className="space-y-4">
                  {/* Password Change */}
                  <div className="border-b border-gray-600 pb-4">
                    <h4 className="text-white font-medium mb-2">Change Password</h4>
                    <p className="text-gray-400 text-sm mb-3">Update your account password</p>
                    <button
                      onClick={() => setShowPasswordChangeModal(true)}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      Change Password
                    </button>
                  </div>

                  {/* 2FA Email PIN */}
                  <div className="border-b border-gray-600 pb-4">
                    <h4 className="text-white font-medium mb-2">Two-Factor Authentication (2FA)</h4>
                    <p className="text-gray-400 text-sm mb-3">
                      Email PIN verification for withdrawals and sensitive operations
                    </p>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-white text-sm">Email PIN Verification</p>
                        <p className="text-gray-400 text-xs">
                          {user?.database_user?.two_factor_enabled ?
                            'Enabled - PIN sent to your email for withdrawals' :
                            'Disabled - Enable for enhanced security'}
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={user?.database_user?.two_factor_enabled || false}
                          onChange={(e) => handle2FAToggle(e.target.checked)}
                          disabled={settingsLoading}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                      </label>
                    </div>
                  </div>

                  {/* Account Security Status */}
                  <div>
                    <h4 className="text-white font-medium mb-2">Account Security Status</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 text-sm">Email Verified</span>
                        <div className="flex items-center space-x-2">
                          <span className={`text-sm ${user?.database_user?.email_verified === true ? 'text-green-400' : 'text-yellow-400'}`}>
                            {user?.database_user?.email_verified === true ? '✅ Verified' : '⚠️ Pending'}
                          </span>
                          {user?.database_user?.email_verified !== true && (
                            <button
                              onClick={handleResendVerificationEmail}
                              disabled={emailVerificationLoading}
                              className="px-2 py-1 text-xs bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded transition-colors duration-200"
                              title="Resend verification email"
                            >
                              {emailVerificationLoading ? 'Sending...' : 'Resend'}
                            </button>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 text-sm">Profile Complete</span>
                        <span className="text-green-400 text-sm">✅ Complete</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 text-sm">2FA Protection</span>
                        <span className={`text-sm ${user?.database_user?.two_factor_enabled ? 'text-green-400' : 'text-gray-400'}`}>
                          {user?.database_user?.two_factor_enabled ? '✅ Enabled' : '❌ Disabled'}
                        </span>
                      </div>
                    </div>

                    {/* Email Verification Messages */}
                    {emailVerificationMessage && (
                      <div className={`mt-3 rounded-lg p-3 ${
                        emailVerificationMessage.type === 'success'
                          ? 'bg-green-900/20 border border-green-500/30 text-green-300'
                          : 'bg-red-900/20 border border-red-500/30 text-red-300'
                      }`}>
                        <p className="text-xs">
                          {emailVerificationMessage.type === 'success' ? '✅' : '❌'} {emailVerificationMessage.text}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-2">🔔 Notifications</h3>
                <p className="text-gray-300 text-sm mb-4">Notification preferences</p>

                <div className="space-y-4">
                  {/* Email Notifications */}
                  <div className="border-b border-gray-600 pb-4">
                    <h4 className="text-white font-medium mb-3">Email Notifications</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white text-sm">Payment Confirmations</p>
                          <p className="text-gray-400 text-xs">Share purchases and commission payments</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notificationPreferences.payment_confirmations}
                            onChange={(e) => handleNotificationPreferenceChange('payment_confirmations', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white text-sm">Security Alerts</p>
                          <p className="text-gray-400 text-xs">Login attempts and security changes</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notificationPreferences.security_alerts}
                            onChange={(e) => handleNotificationPreferenceChange('security_alerts', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white text-sm">Commission Updates</p>
                          <p className="text-gray-400 text-xs">New referrals and commission earnings</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notificationPreferences.commission_updates}
                            onChange={(e) => handleNotificationPreferenceChange('commission_updates', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white text-sm">Company Updates</p>
                          <p className="text-gray-400 text-xs">Mining operations and company news</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notificationPreferences.marketing_emails}
                            onChange={(e) => handleNotificationPreferenceChange('marketing_emails', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Dashboard Notifications */}
                  <div className="border-b border-gray-600 pb-4">
                    <h4 className="text-white font-medium mb-3">Dashboard Notifications</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white text-sm">In-App Notifications</p>
                          <p className="text-gray-400 text-xs">Show notifications in dashboard</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notificationPreferences.in_app_notifications}
                            onChange={(e) => handleNotificationPreferenceChange('in_app_notifications', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white text-sm">Sound Notifications</p>
                          <p className="text-gray-400 text-xs">Play sound for important alerts</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notificationPreferences.sound_notifications}
                            onChange={(e) => handleNotificationPreferenceChange('sound_notifications', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Notification Frequency */}
                  <div>
                    <h4 className="text-white font-medium mb-3">Notification Frequency</h4>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="notification_frequency"
                          value="immediate"
                          checked={notificationPreferences.notification_frequency === 'immediate'}
                          onChange={(e) => handleNotificationPreferenceChange('notification_frequency', e.target.value)}
                          className="w-4 h-4 text-yellow-600 bg-gray-600 border-gray-500 focus:ring-yellow-500"
                        />
                        <span className="ml-2 text-white text-sm">Immediate</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="notification_frequency"
                          value="daily"
                          checked={notificationPreferences.notification_frequency === 'daily'}
                          onChange={(e) => handleNotificationPreferenceChange('notification_frequency', e.target.value)}
                          className="w-4 h-4 text-yellow-600 bg-gray-600 border-gray-500 focus:ring-yellow-500"
                        />
                        <span className="ml-2 text-white text-sm">Daily Summary</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="notification_frequency"
                          value="weekly"
                          checked={notificationPreferences.notification_frequency === 'weekly'}
                          onChange={(e) => handleNotificationPreferenceChange('notification_frequency', e.target.value)}
                          className="w-4 h-4 text-yellow-600 bg-gray-600 border-gray-500 focus:ring-yellow-500"
                        />
                        <span className="ml-2 text-white text-sm">Weekly Summary</span>
                      </label>
                    </div>
                  </div>

                  <div className="pt-2">
                    <button
                      onClick={handleSaveNotificationPreferences}
                      disabled={notificationPreferencesLoading}
                      className="bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center space-x-2"
                    >
                      {notificationPreferencesLoading && (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      )}
                      <span>
                        {notificationPreferencesLoading ? 'Saving...' : 'Save Notification Preferences'}
                      </span>
                    </button>

                    {/* Success/Error Message */}
                    {notificationPreferencesMessage && (
                      <div className={`mt-3 p-3 rounded-lg ${
                        notificationPreferencesMessage.type === 'success'
                          ? 'bg-green-600 text-white'
                          : 'bg-red-600 text-white'
                      }`}>
                        {notificationPreferencesMessage.text}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : activeSection === 'kyc' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">🔐 KYC Verification</h2>
            <p className="text-gray-400 mb-6">
              Complete your Know Your Customer (KYC) verification to unlock full access to your account features,
              including commission withdrawals and dividend payments.
            </p>

            <div className="space-y-6">
              {/* Smart KYC Router - Show appropriate component based on status and field rejections */}
              <SmartKYCRouter
                userId={user?.database_user?.id || user?.id || 0}
                kycStatus={kycStatus}
                kycId={kycId}
                onKYCStatusChange={() => {
                  const userId = user?.database_user?.id || user?.id || 0;
                  if (userId) {
                    loadKYCStatus(userId);
                  }
                }}
              />
            </div>
          </div>
        ) : activeSection === 'legal-documents' ? (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">📋 Legal Documents</h2>

            {/* Enhanced Compliance Status Section */}
            {(() => {
              // Calculate progress correctly - count only the actual document agreements
              const completedCount = [
                agreementStatus.terms_conditions,
                agreementStatus.privacy_policy,
                agreementStatus.disclaimer,
                agreementStatus.share_purchase_agreement
              ].filter(Boolean).length

              const totalCount = 4
              const progressPercentage = Math.round((completedCount / totalCount) * 100)

              return (
                <div className="mb-6">
                  {/* Overall Progress Card */}
                  <div className={`rounded-lg p-6 mb-4 ${
                    mustCompleteLegalDocuments
                      ? 'bg-orange-900/20 border border-orange-500/30'
                      : 'bg-green-900/20 border border-green-500/30'
                  }`}>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`text-2xl ${mustCompleteLegalDocuments ? 'text-orange-400' : 'text-green-400'}`}>
                          {mustCompleteLegalDocuments ? '⚠️' : '✅'}
                        </div>
                        <div>
                          <h3 className={`font-semibold text-lg ${mustCompleteLegalDocuments ? 'text-orange-400' : 'text-green-400'}`}>
                            {mustCompleteLegalDocuments ? 'Legal Document Compliance Required' : 'Legal Documents Complete'}
                          </h3>
                          <p className={`text-sm ${mustCompleteLegalDocuments ? 'text-orange-200' : 'text-green-200'}`}>
                            {mustCompleteLegalDocuments
                              ? 'Complete all legal documents to access your dashboard'
                              : 'All legal documents have been agreed to'
                            }
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-2xl font-bold ${mustCompleteLegalDocuments ? 'text-orange-400' : 'text-green-400'}`}>
                          {completedCount}/{totalCount}
                        </div>
                        <div className={`text-sm ${mustCompleteLegalDocuments ? 'text-orange-300' : 'text-green-300'}`}>
                          {progressPercentage}% Complete
                        </div>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <div className="flex justify-between text-sm mb-2">
                        <span className={mustCompleteLegalDocuments ? 'text-orange-300' : 'text-green-300'}>
                          Progress: {completedCount} of {totalCount} documents completed
                        </span>
                        <span className={mustCompleteLegalDocuments ? 'text-orange-300' : 'text-green-300'}>
                          {progressPercentage}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full transition-all duration-500 ${
                            mustCompleteLegalDocuments ? 'bg-orange-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${progressPercentage}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Next Steps */}
                    {mustCompleteLegalDocuments && (
                      <div className="bg-orange-800/30 rounded-lg p-3">
                        <h4 className="text-orange-300 font-medium mb-2">Next Steps:</h4>
                        <ul className="text-orange-200 text-sm space-y-1">
                          {!agreementStatus.terms_conditions && <li>• Read and agree to Terms & Conditions</li>}
                          {!agreementStatus.privacy_policy && <li>• Read and agree to Privacy Policy</li>}
                          {!agreementStatus.disclaimer && <li>• Read and agree to Legal Disclaimer</li>}
                          {!agreementStatus.share_purchase_agreement && <li>• Read and agree to Share Purchase Agreement</li>}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )
            })()}

            <p className="text-gray-400 mb-4">
              {mustCompleteLegalDocuments
                ? "Please review and agree to all documents below to access your dashboard."
                : "Access important legal documents and agreements. Your agreement status is tracked for compliance."
              }
            </p>

            {/* Manual Refresh Button */}
            <div className="mb-6">
              <button
                onClick={async () => {
                  console.log('🔄 LEGAL DOCS: Manual refresh button clicked')
                  if (!user?.id) {
                    console.log('❌ LEGAL DOCS: No user ID available for manual refresh')
                    return
                  }

                  // Use database_user.id if available, fallback to user.id
                  const userIdForCheck = user?.database_user?.id || user.id
                  console.log('🔄 LEGAL DOCS: Manual refresh using ID:', userIdForCheck, 'Type:', typeof userIdForCheck)
                  console.log('🔄 LEGAL DOCS: User object for manual refresh:', {
                    user_id: user.id,
                    database_user_id: user?.database_user?.id,
                    email: user?.email || user?.database_user?.email
                  })

                  setIsCheckingCompliance(true)
                  try {
                    const status = await LegalDocumentService.checkAgreementStatus(userIdForCheck)
                    console.log('📋 LEGAL DOCS: Manual refresh result:', status)
                    console.log('📋 LEGAL DOCS: Manual refresh individual status:', {
                      terms_conditions: status.terms_conditions,
                      privacy_policy: status.privacy_policy,
                      disclaimer: status.disclaimer,
                      share_purchase_agreement: status.share_purchase_agreement,
                      all_agreed: status.all_agreed
                    })

                    setAgreementStatus(status)

                    if (!status.all_agreed) {
                      console.log('⚠️ LEGAL DOCS: Manual refresh shows incomplete documents')
                      setMustCompleteLegalDocuments(true)
                      setActiveSection('legal-documents')
                    } else {
                      console.log('✅ LEGAL DOCS: Manual refresh confirms all documents completed')
                      setMustCompleteLegalDocuments(false)
                      setMessage({
                        type: 'success',
                        text: 'Legal documents status refreshed - all documents completed!'
                      })
                      setTimeout(() => setMessage(null), 3000)
                    }
                  } catch (error) {
                    console.error('❌ LEGAL DOCS: Error during manual refresh:', error)
                    console.error('❌ LEGAL DOCS: Manual refresh error details:', {
                      message: error.message,
                      stack: error.stack,
                      user_id: userIdForCheck
                    })
                  } finally {
                    setIsCheckingCompliance(false)
                  }
                }}
                disabled={isCheckingCompliance}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center gap-2"
              >
                {isCheckingCompliance ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Checking Status...
                  </>
                ) : (
                  <>
                    🔄 Refresh Status
                  </>
                )}
              </button>
            </div>



            {/* Enhanced Individual Document Cards */}
            <div className="space-y-4 mb-6">
              {[
                {
                  title: 'Terms & Conditions',
                  description: 'Platform usage terms and share purchase conditions',
                  onClick: () => setShowTermsModal(true),
                  completed: agreementStatus.terms_conditions,
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  )
                },
                {
                  title: 'Privacy Policy',
                  description: 'Data protection and POPIA compliance policy',
                  onClick: () => setShowPrivacyModal(true),
                  completed: agreementStatus.privacy_policy,
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  )
                },
                {
                  title: 'Legal Disclaimer',
                  description: 'Risk disclosure and liability limitations',
                  onClick: () => setShowDisclaimerModal(true),
                  completed: agreementStatus.disclaimer,
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  )
                },
                {
                  title: 'Share Purchase Agreement',
                  description: 'Comprehensive share purchase agreement with terms and conditions',
                  onClick: () => setShowSharePurchaseAgreementModal(true),
                  completed: agreementStatus.share_purchase_agreement,
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  )
                }
              ].map((doc, index) => (
                <div key={index} className={`rounded-lg p-5 border transition-all duration-200 hover:shadow-lg ${
                  doc.completed
                    ? 'bg-green-900/10 border-green-500/30 hover:border-green-500/50'
                    : 'bg-gray-800/50 border-gray-600 hover:border-blue-500/50'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-lg ${
                        doc.completed ? 'bg-green-500/20 text-green-400' : 'bg-gray-700 text-gray-400'
                      }`}>
                        {doc.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-white font-semibold text-lg">{doc.title}</h3>
                          {doc.completed && (
                            <div className="flex items-center space-x-1 bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs">
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              <span>Agreed</span>
                            </div>
                          )}
                        </div>
                        <p className="text-gray-300 text-sm mb-3">{doc.description}</p>
                        {!doc.completed && (
                          <div className="flex items-center space-x-2 text-orange-400 text-sm">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Action Required: Please review and agree to continue</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={doc.onClick}
                      className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                        doc.completed
                          ? 'bg-green-600/20 text-green-300 border border-green-500/30 hover:bg-green-600/30'
                          : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                      }`}
                    >
                      {doc.completed ? 'Review' : 'View & Agree'}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Official Company Documents Section */}
            <div className="mt-8">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                🏢 Official Company Documents
              </h3>
              <p className="text-gray-400 mb-6">
                View our official company registration, tax compliance, and banking documents for transparency and verification.
              </p>

              <div className="space-y-3">
                {/* CIPC Certificate */}
                <div className="flex items-center justify-between bg-gray-700 rounded-lg p-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-white font-medium">📜 CIPC Certificate</h4>
                      <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">Verified</span>
                    </div>
                    <p className="text-gray-400 text-sm">Official company registration certificate from the Companies and Intellectual Property Commission (CIPC)</p>
                  </div>
                  <button
                    onClick={() => window.open('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/cipc.pdf', '_blank')}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>View PDF</span>
                  </button>
                </div>

                {/* SARS Tax Certificate */}
                <div className="flex items-center justify-between bg-gray-700 rounded-lg p-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-white font-medium">💰 SARS Tax Certificate</h4>
                      <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">Verified</span>
                    </div>
                    <p className="text-gray-400 text-sm">South African Revenue Service (SARS) tax compliance certificate confirming good standing</p>
                  </div>
                  <button
                    onClick={() => window.open('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/sars.pdf', '_blank')}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>View PDF</span>
                  </button>
                </div>

                {/* FNB Bank Proof */}
                <div className="flex items-center justify-between bg-gray-700 rounded-lg p-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-white font-medium">🏦 FNB Bank Proof</h4>
                      <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">Verified</span>
                    </div>
                    <p className="text-gray-400 text-sm">First National Bank (FNB) proof of account document confirming official company banking</p>
                  </div>
                  <button
                    onClick={() => window.open('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/fnb.pdf', '_blank')}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>View PDF</span>
                  </button>
                </div>
              </div>

              {/* Document Verification Notice */}
              <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mt-6">
                <h4 className="text-blue-400 font-semibold mb-2">🔍 Document Verification</h4>
                <p className="text-gray-300 text-sm">
                  All official company documents are verified and up-to-date. These documents demonstrate our legal compliance,
                  tax good standing, and legitimate banking relationships. You can verify these documents independently with
                  the respective authorities if needed.
                </p>
              </div>
            </div>
          </div>
        ) : (
          // Fallback for any unhandled sections
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">
              {activeSection.charAt(0).toUpperCase() + activeSection.slice(1).replace('-', ' ')}
            </h2>
            <p className="text-gray-400">
              This section is coming soon. Please check back later.
            </p>
          </div>
        )}

        {/* Version Footer */}
        <div style={{
          marginTop: '40px',
          padding: '16px',
          borderTop: '1px solid #374151',
          textAlign: 'center'
        }}>
          <p style={{
            color: '#6B7280',
            fontSize: '12px',
            margin: 0
          }}>
            Aureus Africa {getFullVersion()} • {new Date().getFullYear()} Aureus Alliance Holdings (Pty) Ltd
          </p>
          {/* Debug refresh button for testing (only show for user ID 106) */}
          {user?.database_user?.id === 106 && (
            <button
              onClick={handleRefreshUserData}
              style={{
                marginTop: '8px',
                padding: '4px 8px',
                fontSize: '10px',
                backgroundColor: '#374151',
                color: '#9CA3AF',
                border: '1px solid #4B5563',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
              title="Refresh user data from database (clears cache)"
            >
              🔄 Refresh User Data
            </button>
          )}
        </div>
      </div>

      </div>

      {/* Share Purchase Flow Modal */}
      {showPurchaseFlow && (
        <SharePurchaseFlow
          user={user}
          onClose={() => setShowPurchaseFlow(false)}
        />
      )}

      {/* Telegram Connection Modal */}
      <TelegramConnectionModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
        onConnect={handleTelegramConnect}
        loading={telegramLoading}
      />

      {/* Shares Modal temporarily disabled to fix build error. */}
      {null}

      {/* Profile Editor Modal */}
      {showProfileEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-white mb-4">✏️ Edit Profile</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                <input
                  type="text"
                  value={profileEditorData.full_name}
                  onChange={(e) => setProfileEditorData(prev => ({ ...prev, full_name: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                <input
                  type="text"
                  value={profileEditorData.phone}
                  onChange={(e) => setProfileEditorData(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500"
                  placeholder="Enter your phone number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Profile Description</label>
                <textarea
                  value={profileEditorData.profile_description}
                  onChange={(e) => setProfileEditorData(prev => ({ ...prev, profile_description: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 resize-none"
                  placeholder="Tell others about yourself..."
                  rows={4}
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowProfileEditor(false)}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                disabled={settingsLoading}
              >
                Cancel
              </button>
              <button
                onClick={handleProfileUpdate}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
                disabled={settingsLoading}
              >
                {settingsLoading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Username Editor Modal */}
      {showUsernameEditor && user?.database_user && (
        <UsernameEditor
          currentUsername={user.database_user.username || ''}
          userId={user.database_user.id}
          onUsernameUpdate={handleUsernameUpdate}
          onClose={() => setShowUsernameEditor(false)}
        />
      )}

      {/* Password Change Modal */}
      {showPasswordChangeModal && user?.database_user && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-md w-full">
            <SecurePasswordChangeForm
              userId={user.database_user.id}
              userEmail={user.database_user.email || user.email}
              onPasswordChanged={() => {
                setSettingsMessage({
                  type: 'success',
                  text: 'Password changed successfully!'
                });
                setTimeout(() => setSettingsMessage(null), 3000);
              }}
              onClose={() => setShowPasswordChangeModal(false)}
            />
          </div>
        </div>
      )}

      {/* Email Verification PIN Modal */}
      {showEmailVerificationPinModal && user?.database_user?.email && (
        <PinVerificationModal
          isOpen={showEmailVerificationPinModal}
          onClose={() => setShowEmailVerificationPinModal(false)}
          onVerified={handleEmailVerificationPinSuccess}
          userId={user.database_user.id}
          email={user.database_user.email}
          purpose="profile_update"
          title="Verify Your Email Address"
          description="Please enter the 6-digit PIN sent to your email to verify your account."
        />
      )}

      {/* Legal Documents Modals */}
      {showTermsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold text-white">Terms & Conditions</h2>
              <button
                onClick={() => setShowTermsModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <TermsAndConditions />
            </div>
            {!agreementStatus.terms_conditions && (
              <div className="border-t border-gray-700 p-6 bg-gray-900">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="agree-terms"
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="agree-terms" className="text-sm text-gray-300">
                      I have read and agree to the Terms & Conditions
                    </label>
                  </div>
                  <button
                    onClick={() => {
                      const checkbox = document.getElementById('agree-terms') as HTMLInputElement
                      if (checkbox?.checked) {
                        handleDocumentAgreement('terms_conditions', () => setShowTermsModal(false))
                      } else {
                        alert('Please check the agreement checkbox first.')
                      }
                    }}
                    disabled={agreementLoading}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-2 rounded text-sm transition-colors"
                  >
                    {agreementLoading ? 'Recording...' : 'Agree & Close'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {showPrivacyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold text-white">Privacy Policy</h2>
              <button
                onClick={() => setShowPrivacyModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <ComprehensivePrivacyPolicy />
            </div>
            {!agreementStatus.privacy_policy && (
              <div className="border-t border-gray-700 p-6 bg-gray-900">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="agree-privacy"
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="agree-privacy" className="text-sm text-gray-300">
                      I have read and agree to the Privacy Policy
                    </label>
                  </div>
                  <button
                    onClick={() => {
                      const checkbox = document.getElementById('agree-privacy') as HTMLInputElement
                      if (checkbox?.checked) {
                        handleDocumentAgreement('privacy_policy', () => setShowPrivacyModal(false))
                      } else {
                        alert('Please check the agreement checkbox first.')
                      }
                    }}
                    disabled={agreementLoading}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-2 rounded text-sm transition-colors"
                  >
                    {agreementLoading ? 'Recording...' : 'Agree & Close'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {showDisclaimerModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold text-white">Legal Disclaimer</h2>
              <button
                onClick={() => setShowDisclaimerModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <ComprehensiveDisclaimer />
            </div>
            {!agreementStatus.disclaimer && (
              <div className="border-t border-gray-700 p-6 bg-gray-900">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="agree-disclaimer"
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="agree-disclaimer" className="text-sm text-gray-300">
                      I have read and acknowledge the Legal Disclaimer
                    </label>
                  </div>
                  <button
                    onClick={() => {
                      const checkbox = document.getElementById('agree-disclaimer') as HTMLInputElement
                      if (checkbox?.checked) {
                        handleDocumentAgreement('disclaimer', () => setShowDisclaimerModal(false))
                      } else {
                        alert('Please check the acknowledgment checkbox first.')
                      }
                    }}
                    disabled={agreementLoading}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-2 rounded text-sm transition-colors"
                  >
                    {agreementLoading ? 'Recording...' : 'Acknowledge & Close'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Share Purchase Agreement Modal */}
      {showSharePurchaseAgreementModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold text-white">Share Purchase Agreement</h2>
              <button
                onClick={() => setShowSharePurchaseAgreementModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <SharePurchaseAgreement />
            </div>
            {!agreementStatus.share_purchase_agreement && (
              <div className="border-t border-gray-700 p-6 bg-gray-900">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="agree-share-purchase"
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="agree-share-purchase" className="text-sm text-gray-300">
                      I have read and agree to the Share Purchase Agreement
                    </label>
                  </div>
                  <button
                    onClick={() => {
                      const checkbox = document.getElementById('agree-share-purchase') as HTMLInputElement
                      if (checkbox?.checked) {
                        handleDocumentAgreement('share_purchase_agreement', () => setShowSharePurchaseAgreementModal(false))
                      } else {
                        alert('Please check the agreement checkbox first.')
                      }
                    }}
                    disabled={agreementLoading}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-2 rounded text-sm transition-colors"
                  >
                    {agreementLoading ? 'Recording...' : 'Agree & Close'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {showShareAgreementModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold text-white">Share Purchase Agreement Template</h2>
              <button
                onClick={() => setShowShareAgreementModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="text-white">
                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-6">
                  <h3 className="text-blue-400 font-bold mb-2">📋 Share Purchase Agreement Template</h3>
                  <p className="text-sm text-gray-300">
                    This is a template of the standard share purchase agreement. Actual agreements are generated
                    automatically upon share purchase and include specific details such as share quantities,
                    prices, and certificate numbers.
                  </p>
                </div>

                <div className="space-y-4 text-gray-300">
                  <h3 className="text-lg font-semibold text-white">Share Purchase Agreement</h3>
                  <p>
                    This Share Purchase Agreement ("Agreement") is entered into between Aureus Alliance Holdings (Pty) Ltd
                    ("Company") and the purchaser ("Shareholder") for the acquisition of no par value shares in the Company.
                  </p>

                  <h4 className="text-white font-semibold">1. Company Details</h4>
                  <p>
                    <strong>Company Name:</strong> Aureus Alliance Holdings (Pty) Ltd<br/>
                    <strong>Registration Number:</strong> 2025/368711/07<br/>
                    <strong>Registered Address:</strong> 1848 Mees Avenue, Randpark Ridge, Randburg, Gauteng, 2169
                  </p>

                  <h4 className="text-white font-semibold">2. Share Details</h4>
                  <p>
                    The shares being purchased are no par value shares in Aureus Alliance Holdings (Pty) Ltd,
                    a company incorporated under the laws of South Africa. Each share represents direct equity
                    ownership in the Company's gold mining operations.
                  </p>

                  <h4 className="text-white font-semibold">3. Purchase Terms</h4>
                  <p>
                    Shares are sold at the current phase price as determined by the Company's 5-year expansion plan.
                    Payment must be completed in full before share allocation. The Company reserves the right to
                    reject any purchase at its sole discretion.
                  </p>

                  <h4 className="text-white font-semibold">4. Certificate Processing</h4>
                  <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3">
                    <p className="text-sm">
                      <strong>Share certificates will be available 10-14 business days after purchase completion,</strong>
                      provided KYC verification has been completed and approved. Certificates are issued by CIPC
                      (Companies and Intellectual Property Commission).
                    </p>
                  </div>

                  <h4 className="text-white font-semibold">5. Rights and Obligations</h4>
                  <p>
                    Shareholders are entitled to dividends as declared by the Company and have voting rights
                    in accordance with the Company's Memorandum of Incorporation. Shareholders must comply
                    with all applicable laws and Company policies.
                  </p>

                  <h4 className="text-white font-semibold">6. Risk Acknowledgment</h4>
                  <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3">
                    <p className="text-sm">
                      <strong>Share purchases involve significant risk.</strong> Mining operations are subject to
                      operational, environmental, regulatory, and market risks. Past performance does not guarantee
                      future results. Shareholders may lose some or all of their investment.
                    </p>
                  </div>

                  <h4 className="text-white font-semibold">7. Governing Law</h4>
                  <p>
                    This Agreement is governed by South African law and subject to the jurisdiction of
                    South African courts.
                  </p>

                  <p className="text-sm text-gray-400 mt-6 border-t border-gray-600 pt-4">
                    <strong>Note:</strong> This is a template for informational purposes. Actual share purchase
                    agreements are generated with specific transaction details upon completion of purchase.
                    For legal questions, contact <EMAIL>.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 2FA PIN Verification Modal */}
      {show2FAPinModal && user?.database_user?.email && (
        <PinVerificationModal
          isOpen={show2FAPinModal}
          onClose={handle2FAPinModalClose}
          onVerified={handle2FAPinVerified}
          userId={user.database_user.id}
          email={user.database_user.email}
          purpose="security_change"
          title="2FA Security Verification"
          description={`Please enter the 6-digit PIN sent to your email to ${pending2FAState ? 'enable' : 'disable'} Two-Factor Authentication.`}
        />
      )}

    </div>
  )
};



export default UserDashboard
