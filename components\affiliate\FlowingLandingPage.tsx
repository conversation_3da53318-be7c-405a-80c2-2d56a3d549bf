import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { EmailRegistrationFormProgressive } from '../EmailRegistrationFormProgressive';
import { getFullVersion } from '../../lib/version';
import DynamicMeetingSection from './DynamicMeetingSection';
import MeetingBookingModal from './MeetingBookingModal';
import BookingSuccessModal from './BookingSuccessModal';
// Premium Calculator Component
const PremiumCalculator = () => {
  const [shares, setShares] = useState(1000);
  const [washPlants, setWashPlants] = useState(2);
  const [selectedYear, setSelectedYear] = useState(2026);

  // Real calculation constants
  const TOTAL_SHARES = 1400000;
  const GOLD_PRICE_USD_PER_KG = 109026;
  const EXPANSION_PLAN = {
    2026: { plants: 10, hectares: 250 },
    2027: { plants: 25, hectares: 625 },
    2028: { plants: 50, hectares: 1250 },
    2029: { plants: 100, hectares: 2500 },
    2030: { plants: 200, hectares: 5000 }
  };

  // Wash plant options (starting with 2)
  const washPlantOptions = [2, 5, 10, 15, 20, 25, 30, 40, 50, 75, 100, 150, 200];

  // Calculate real dividends
  const calculateDividends = () => {
    const numPlants = washPlants;

    // Real mining calculations
    const plantCapacityTPH = 200;
    const effectiveHoursPerDay = 20;
    const operatingDaysPerYear = 330;
    const bulkDensity = 1.8;
    const avgGravelThickness = 0.8;
    const inSituGrade = 0.9;
    const recoveryFactor = 70;
    const opexPercent = 45;

    // Annual throughput and gold production
    const annualThroughputT = numPlants * plantCapacityTPH * effectiveHoursPerDay * operatingDaysPerYear;
    const annualGoldKg = (annualThroughputT * (inSituGrade / bulkDensity) * (recoveryFactor / 100)) / 1000;

    // Revenue and EBIT
    const annualRevenue = annualGoldKg * GOLD_PRICE_USD_PER_KG;
    const annualEbit = annualRevenue * (1 - opexPercent / 100);

    // Dividends
    const dividendPerShare = annualEbit / TOTAL_SHARES;
    const userAnnualDividend = dividendPerShare * shares;
    const monthlyDividend = userAnnualDividend / 12;

    // Share value calculation (assuming $50 per share)
    const sharePrice = 50;
    const totalShareValue = shares * sharePrice;
    const dividendYield = (userAnnualDividend / totalShareValue);

    return {
      annualDividend: userAnnualDividend,
      monthlyDividend: monthlyDividend,
      dividendYield: dividendYield,
      goldProduction: annualGoldKg,
      totalRevenue: annualRevenue
    };
  };

  const results = calculateDividends();

  return (
    <div className="premium-calculator">
      <div className="calc-header">
        <div className="calc-icon">💎</div>
        <div className="calc-title">
          <h3>Premium Dividend Calculator</h3>
          <p>Real calculations based on actual mining data</p>
        </div>
      </div>

      <div className="calc-inputs">
        <div className="input-section">
          <label>Your Shares</label>
          <div className="input-wrapper">
            <input
              type="number"
              value={shares}
              onChange={(e) => setShares(parseInt(e.target.value) || 0)}
              className="premium-input"
              min="1"
              max="100000"
            />
            <span className="input-suffix">shares</span>
          </div>
          <div className="input-info">
            {((shares / TOTAL_SHARES) * 100).toFixed(4)}% ownership
          </div>
        </div>

        <div className="input-section">
          <label>Wash Plants</label>
          <select
            value={washPlants}
            onChange={(e) => setWashPlants(parseInt(e.target.value))}
            className="premium-select"
          >
            {washPlantOptions.map(plants => (
              <option key={plants} value={plants}>
                {plants} Plants ({(plants * 25).toLocaleString()}ha)
              </option>
            ))}
          </select>
          <div className="input-info">
            {(washPlants * 25).toLocaleString()} hectares coverage
          </div>
        </div>

        <div className="input-section">
          <label>Target Year</label>
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="premium-select"
          >
            <option value={2026}>2026 Production</option>
            <option value={2027}>2027 Production</option>
            <option value={2028}>2028 Production</option>
            <option value={2029}>2029 Production</option>
            <option value={2030}>2030 Production</option>
          </select>
          <div className="input-info">
            Year {selectedYear} projections
          </div>
        </div>
      </div>

      <div className="calc-results">
        <div className="result-grid">
          <div className="result-card primary">
            <div className="result-icon">💰</div>
            <div className="result-content">
              <div className="result-value">${results.annualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
              <div className="result-label">Annual Dividend</div>
              <div className="result-sub">${results.monthlyDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}/month</div>
            </div>
          </div>

          <div className="result-card">
            <div className="result-icon">📈</div>
            <div className="result-content">
              <div className="result-value">${results.dividendYield.toFixed(0)}</div>
              <div className="result-label">Dividend Yield</div>
              <div className="result-sub">Based on current calculations</div>
            </div>
          </div>

          <div className="result-card">
            <div className="result-icon">🥇</div>
            <div className="result-content">
              <div className="result-value">{results.goldProduction.toLocaleString(undefined, { maximumFractionDigits: 0 })}kg</div>
              <div className="result-label">Gold Production</div>
              <div className="result-sub">${(results.totalRevenue / 1000000).toFixed(1)}M revenue</div>
            </div>
          </div>
        </div>

        <div className="calc-disclaimer">
          <div className="disclaimer-icon">⚠️</div>
          <p><strong>Important:</strong> These are calculations only, not guaranteed outcomes. Based on current gold price of ${GOLD_PRICE_USD_PER_KG.toLocaleString()}/kg, 70% recovery rate, and 45% operational costs. Mining operations involve significant risks and actual results may vary substantially. Past performance does not guarantee future results.</p>
        </div>
      </div>
    </div>
  );
};


interface FlowingLandingPageProps {
  username: string;
}

interface AffiliateProfile {
  id: number;
  username: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  email: string;
  phone?: string;
  phone_number?: string;
  profile_description?: string;
  profile_image_url?: string;
  total_referrals: number;
  total_earnings: number;
  created_at: string;
}

export const FlowingLandingPage: React.FC<FlowingLandingPageProps> = ({ username }) => {
  const [affiliate, setAffiliate] = useState<AffiliateProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [showRegistration, setShowRegistration] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);
  const [contactMessage, setContactMessage] = useState('');
  const [contactSending, setContactSending] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Meeting booking states
  const [selectedMeeting, setSelectedMeeting] = useState<any>(null);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [showBookingSuccess, setShowBookingSuccess] = useState(false);
  const [confirmationCode, setConfirmationCode] = useState('');

  // Format phone number to international format
  const formatPhoneNumber = (phone: string) => {
    if (!phone) return '';
    // If phone starts with 0, replace with +27
    if (phone.startsWith('0')) {
      return `+27${phone.substring(1)}`;
    }
    // If phone already has +27, return as is
    if (phone.startsWith('+27')) {
      return phone;
    }
    // If phone starts with 27, add +
    if (phone.startsWith('27')) {
      return `+${phone}`;
    }
    // Otherwise assume it's a South African number without country code
    return `+27${phone}`;
  };
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const galleryImages = [
    {
      src: "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-1.jpg",
      alt: "Mining Operations Overview",
      title: "Mining Operations Overview"
    },
    {
      src: "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-2.jpg",
      alt: "Equipment and Machinery",
      title: "Equipment and Machinery"
    },
    {
      src: "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-3.jpg",
      alt: "Site Location",
      title: "Site Location"
    },
    {
      src: "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-4.jpg",
      alt: "Processing Operations",
      title: "Processing Operations"
    },
    {
      src: "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-5.jpg",
      alt: "Team Operations",
      title: "Team Operations"
    },
    {
      src: "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-6.jpg",
      alt: "Gold Extraction",
      title: "Gold Extraction"
    }
  ];

  useEffect(() => {
    loadAffiliateProfile();
  }, [username]);

  // Refresh affiliate data when page becomes visible (to catch profile picture updates)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && affiliate) {
        // Refresh affiliate data when page becomes visible
        loadAffiliateProfile();
      }
    };

    const handleFocus = () => {
      if (affiliate) {
        // Refresh affiliate data when window gains focus
        loadAffiliateProfile();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [affiliate]);

  // Back to top button scroll handler
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!lightboxOpen) return;

      switch (e.key) {
        case 'Escape':
          closeLightbox();
          break;
        case 'ArrowLeft':
          prevImage();
          break;
        case 'ArrowRight':
          nextImage();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [lightboxOpen]);

  const loadAffiliateProfile = async () => {
    try {
      console.log('🔍 Loading affiliate profile for username:', username);

      // First try to find user in users table (primary approach)
      console.log('🔍 Checking users table first...');
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .ilike('username', username) // Case-insensitive search
        .single();

      let affiliateData = null;

      if (!userError && userData) {
        // Found user in users table
        affiliateData = {
          ...userData,
          telegram_username: userData.telegram_username || null // Keep telegram_username if it exists
        };
        console.log('✅ Found user in users table:', username);
      } else {
        // Fallback: check if this user exists in telegram_users table (only if not found in users)
        console.log('🔍 User not found in users table, checking telegram_users table...');
        const { data: telegramUser, error: telegramError } = await supabase
          .from('telegram_users')
          .select('*')
          .ilike('username', username) // Case-insensitive search
          .maybeSingle(); // Use maybeSingle to avoid 406 errors

        if (!telegramError && telegramUser) {
        console.log('✅ Found Telegram user:', telegramUser.username);

          // If telegram user exists, get their linked user data
          if (telegramUser.user_id) {
            const { data: linkedUserData, error: linkedUserError } = await supabase
              .from('users')
              .select('*')
              .eq('id', telegramUser.user_id)
              .single();

            if (!linkedUserError && linkedUserData) {
              affiliateData = {
                ...linkedUserData,
                telegram_username: telegramUser.username
              };
              console.log('✅ Found linked user data for Telegram user');
            } else {
              // Create affiliate data from telegram user info
              affiliateData = {
                id: telegramUser.user_id || telegramUser.id,
                username: telegramUser.username,
                first_name: telegramUser.first_name,
                last_name: telegramUser.last_name,
                full_name: telegramUser.full_name,
                email: telegramUser.temp_email || '',
                phone: telegramUser.phone_number || '',
                profile_description: '',
                profile_image_url: '',
                total_referrals: 0,
                total_earnings: 0,
                created_at: telegramUser.created_at,
                telegram_username: telegramUser.username
              };
              console.log('✅ Created affiliate data from Telegram user info');
            }
          } else {
            // Create basic affiliate data from telegram user
            affiliateData = {
              id: telegramUser.id,
              username: telegramUser.username,
              first_name: telegramUser.first_name,
              last_name: telegramUser.last_name,
              full_name: telegramUser.full_name,
              email: telegramUser.temp_email || '',
              phone: telegramUser.phone_number || '',
              profile_description: '',
              profile_image_url: '',
              total_referrals: 0,
              total_earnings: 0,
              created_at: telegramUser.created_at,
              telegram_username: telegramUser.username
            };
            console.log('✅ Created basic affiliate data from Telegram user');
          }
        } else {
          console.log('❌ No user found for username:', username);
          throw new Error(`No user found for username: ${username}`);
        }
      }

      if (!affiliateData) {
        throw new Error(`No affiliate data found for username: ${username}`);
      }

      console.log('Final affiliate data:', affiliateData);
      setAffiliate(affiliateData);
    } catch (error) {
      console.error('Error loading affiliate profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRegistrationSuccess = (userData: any) => {
    console.log('✅ Registration successful on affiliate landing page:', userData);
    setRegistrationSuccess(true);
    setShowRegistration(false);

    // Store user data and redirect to dashboard
    if (userData) {
      // Store user data in localStorage for dashboard access
      localStorage.setItem('aureus_user', JSON.stringify(userData.database_user || userData));
      localStorage.setItem('aureus_user_type', 'shareholder'); // Users from affiliate pages become shareholders

      // Store session info
      const sessionData = {
        user_id: userData.id || userData.database_user?.id,
        email: userData.email || userData.database_user?.email,
        account_type: userData.account_type || 'web',
        user_type: 'shareholder', // Users from affiliate pages become shareholders
        logged_in_at: new Date().toISOString()
      };
      localStorage.setItem('aureus_session', JSON.stringify(sessionData));

      console.log('💾 Stored user session data, redirecting to dashboard...');

      // Redirect to dashboard after brief delay
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 1500);
    }
  };

  // Meeting booking handlers
  const handleBookingClick = (meeting: any) => {
    setSelectedMeeting(meeting);
    setShowBookingModal(true);
  };

  const handleBookingSuccess = (code: string) => {
    setConfirmationCode(code);
    setShowBookingModal(false);
    setShowBookingSuccess(true);
  };

  const handleCloseBookingModal = () => {
    setShowBookingModal(false);
    setSelectedMeeting(null);
  };

  const handleCloseSuccessModal = () => {
    setShowBookingSuccess(false);
    setConfirmationCode('');
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };



  const handleSendMessage = async () => {
    if (!contactMessage.trim()) return;

    setContactSending(true);
    try {
      const response = await fetch('/api/send-contact-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          affiliateId: affiliate.id,
          message: contactMessage,
          senderInfo: {
            page: `${affiliate.username} Landing Page`,
            timestamp: new Date().toISOString()
          }
        }),
      });

      if (response.ok) {
        setContactMessage('');
        setShowContactForm(false);
        alert('Message sent successfully! Your sponsor will receive it in their dashboard.');
      } else {
        alert('Failed to send message. Please try again.');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    } finally {
      setContactSending(false);
    }
  };

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
    document.body.style.overflow = 'hidden';
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
    document.body.style.overflow = 'unset';
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % galleryImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + galleryImages.length) % galleryImages.length);
  };

  if (loading) {
    return (
      <div className="loading-screen">
        <div className="loading-content">
          <div className="loading-spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!affiliate) {
    return (
      <div className="error-screen">
        <div className="error-content">
          <h2>Affiliate Not Found</h2>
          <p>The affiliate profile "{username}" could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="aureus-experience">
      {/* Floating Navigation */}
      <nav className="floating-nav">
        <div className="nav-content">
          <div className="nav-brand">
            <img
              src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
              alt="Aureus Alliance Holdings"
              className="nav-logo"
            />
          </div>
          <div className="nav-menu">
            <a href="#hero" className="nav-link">Home</a>
            <a href="#about" className="nav-link">About</a>
            <a href="#operations" className="nav-link">Operations</a>
            <a href="#calculator" className="nav-link">Calculator</a>
            <a href="#gallery" className="nav-link">Gallery</a>
          </div>
          <div className="nav-actions">
            <a href="/login" className="nav-button login-btn">
              Login
            </a>
            <button
              onClick={() => setShowRegistration(true)}
              className="cta-button"
            >
              <span>Purchase Shares</span>
              <div className="button-glow"></div>
            </button>
          </div>
        </div>
      </nav>

      {/* Profile Section */}
      <section id="profile" className="profile-section">
        <div className="section-container">
          <div className="profile-hero">
            <div className="profile-content">
              <div className="profile-text">
                <div className="profile-header">
                  <div className="profile-info">
                    <h1 className="profile-name">
                      {affiliate.full_name || `${affiliate.first_name} ${affiliate.last_name}`.trim() || affiliate.username}
                    </h1>
                    <p className="profile-role">Aureus Alliance Holdings Representative</p>

                    {/* Contact Information */}
                    <div className="contact-details">
                      <div className="contact-item">
                        <span className="contact-icon">🌍</span>
                        <span>{affiliate.country || 'South Africa'}</span>
                      </div>

                      {(affiliate.phone_number || affiliate.phone) && (
                        <div className="contact-item">
                          <span className="contact-icon">📱</span>
                          <a href={`https://wa.me/${formatPhoneNumber(affiliate.phone_number || affiliate.phone).replace(/[^0-9]/g, '')}`} target="_blank" rel="noopener noreferrer">
                            WhatsApp
                          </a>
                        </div>
                      )}

                      {affiliate.telegram_username && (
                        <div className="contact-item">
                          <span className="contact-icon">✈️</span>
                          <a href={`https://t.me/${affiliate.telegram_username}`} target="_blank" rel="noopener noreferrer">
                            @{affiliate.telegram_username}
                          </a>
                        </div>
                      )}

                      {affiliate.linkedin_url && (
                        <div className="contact-item">
                          <span className="contact-icon">💼</span>
                          <a href={affiliate.linkedin_url} target="_blank" rel="noopener noreferrer">
                            LinkedIn
                          </a>
                        </div>
                      )}

                      {affiliate.email && !affiliate.email.includes('@telegram.local') && (
                        <div className="contact-item">
                          <span className="contact-icon">📧</span>
                          <a href={`mailto:${affiliate.email}`}>
                            Email
                          </a>
                        </div>
                      )}
                    </div>

                    {/* Tags */}
                    {affiliate.tags && affiliate.tags.length > 0 && (
                      <div className="profile-tags">
                        {affiliate.tags.map((tag: string, index: number) => (
                          <span key={index} className="profile-tag">{tag}</span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="profile-description">
                  <p>
                    {affiliate.profile_description ||
                     `Hi, my name is ${affiliate.full_name || `${affiliate.first_name} ${affiliate.last_name}`.trim() || affiliate.username}, and I'm proud to represent Aureus Alliance Holdings.

At Aureus Alliance Holdings, our mission is to lead sustainable gold mining while creating opportunities for our global community. As your trusted representative, I'm here to guide you through our vision — combining eco-friendly mining practices, cutting-edge innovation, and NFT-backed shareholder benefits.

Together, we're shaping a future built on transparency, growth, and prosperity.`}
                  </p>
                </div>



                <div className="profile-actions">
                  <button
                    onClick={() => setShowRegistration(true)}
                    className="primary-cta"
                  >
                    <span>Purchase Shares</span>
                    <div className="cta-glow"></div>
                  </button>

                  <div className="contact-options">
                    {(affiliate.phone_number || affiliate.phone) && (
                      <button
                        onClick={() => window.open(`https://wa.me/${formatPhoneNumber(affiliate.phone_number || affiliate.phone).replace(/[^0-9]/g, '')}?text=Hi ${affiliate.first_name || affiliate.username}, I'm interested in learning more about Aureus Alliance Holdings!`, '_blank')}
                        className="contact-btn whatsapp-btn"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.787"/>
                        </svg>
                        WhatsApp
                      </button>
                    )}

                    <button
                      onClick={() => setShowContactForm(true)}
                      className="contact-btn message-btn"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                      </svg>
                      Send Message
                    </button>
                  </div>
                </div>
              </div>

              <div className="profile-visual">
                <div className="profile-image-container">
                  <div className="large-profile-image">
                    <img
                      src={affiliate.profile_image_url ? `${affiliate.profile_image_url}?t=${Date.now()}` : "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/default-avatar.png"}
                      alt={affiliate.full_name || affiliate.username}
                      style={{
                        width: '100%',
                        height: '100%',
                        borderRadius: '12px',
                        objectFit: 'cover'
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section id="hero" className="hero-section">
        <div className="section-container">
          <div className="hero-content">
            <div className="hero-text">
              <h1 className="hero-title">
                <span className="title-line">Real Gold</span>
                <span className="title-line">Real Shares</span>
                <span className="title-line">Real Ownership</span>
              </h1>
              
              <p className="hero-description">
                Join the future of gold mining with transparent, profitable, and sustainable share ownership.
                Backed by real gold reserves across Africa, with dividend potential based on mining operations.
              </p>

              <div className="hero-metrics">
                <div className="metric-item">
                  <span className="metric-number">5,000</span>
                  <span className="metric-label">Hectares Planned</span>
                </div>
                <div className="metric-item">
                  <span className="metric-number">200</span>
                  <span className="metric-label">Plants by 2030</span>
                </div>
                <div className="metric-item">
                  <span className="metric-number">25ha</span>
                  <span className="metric-label">Per Plant</span>
                </div>
                <div className="metric-item">
                  <span className="metric-number">2030</span>
                  <span className="metric-label">Target Year</span>
                </div>
              </div>
              
              <div className="hero-actions">
                <button
                  onClick={() => setShowRegistration(true)}
                  className="primary-cta"
                >
                  <span>Purchase Shares</span>
                  <div className="cta-glow"></div>
                </button>
                <button className="secondary-cta">
                  <span>Learn More</span>
                </button>
              </div>
            </div>

            <div className="hero-visual">
              <div className="visual-container">
                <img
                  src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                  alt="Aureus Alliance Holdings"
                  className="hero-logo"
                />
                <div className="logo-glow"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="about-section">
        <div className="section-container">
          <div className="section-content">
            <div className="section-text">
              <h2 className="section-title">About Aureus Alliance Holdings</h2>
              <p className="section-description">
                CIPC-registered gold mining company building wealth through real operations across Africa.
                Our revolutionary 5-year expansion plan transforms 5,000 hectares into 200 operational plants,
                delivering consistent annual dividends to shareholders worldwide.
              </p>

              <div className="feature-list">
                <div className="feature-item">
                  <div className="feature-icon">🏆</div>
                  <div className="feature-text">
                    <h4>Proven Track Record</h4>
                    <p>Established mining operations with transparent reporting</p>
                  </div>
                </div>
                <div className="feature-item">
                  <div className="feature-icon">💎</div>
                  <div className="feature-text">
                    <h4>Real Gold Reserves</h4>
                    <p>Backed by verified gold deposits across multiple African countries</p>
                  </div>
                </div>
                <div className="feature-item">
                  <div className="feature-icon">📈</div>
                  <div className="feature-text">
                    <h4>Consistent Dividends</h4>
                    <p>Annual dividends paid directly to shareholders</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="section-visual">
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-number">5,000</div>
                  <div className="stat-text">Hectares</div>
                </div>
                <div className="stat-card">
                  <div className="stat-number">200</div>
                  <div className="stat-text">Plants</div>
                </div>
                <div className="stat-card">
                  <div className="stat-number">25ha</div>
                  <div className="stat-text">Per Plant</div>
                </div>
                <div className="stat-card">
                  <div className="stat-number">2030</div>
                  <div className="stat-text">Target</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Washplant Setup Section */}
      <section id="operations" className="operations-section">
        <div className="section-container">
          <div className="section-content reverse">
            <div className="section-visual">
              <div className="video-container">
                <video
                  className="operations-video"
                  poster="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/washplant-setup.jpg"
                  controls
                >
                  <source src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/yagden.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="video-overlay">
                  <div className="play-button">▶</div>
                </div>
              </div>
            </div>
            <div className="section-text">
              <h2 className="section-title">Washplant Setup Progress</h2>
              <p className="section-description">
                Watch our washplant setup and preparation process. We're building the infrastructure
                that will process gold-bearing material and generate dividends for shareholders.
                This shows the real work happening to make your dividends possible.
              </p>
              <div className="operation-metrics">
                <div className="metric-row">
                  <span className="metric-label">Setup Status:</span>
                  <span className="metric-value">In Progress</span>
                </div>
                <div className="metric-row">
                  <span className="metric-label">Target Capacity:</span>
                  <span className="metric-value">200 TPH</span>
                </div>
                <div className="metric-row">
                  <span className="metric-label">Expected Start:</span>
                  <span className="metric-value">2026</span>
                </div>
              </div>
              <button
                onClick={() => setShowRegistration(true)}
                className="section-cta"
              >
                Join Before Operations Begin
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Calculator Section */}
      <section id="calculator" className="calculator-section">
        <div className="section-container">
          <div className="calculator-intro">
            <div className="intro-content">
              <h2 className="section-title">Calculate Your Dividends</h2>
              <p className="section-description">
                Use our advanced calculator to estimate your potential dividends based on real mining operations.
                Adjust the number of wash plants and see how your dividends scale with our expansion.
              </p>
              <div className="intro-features">
                <div className="feature-point">
                  <span className="feature-icon">🏭</span>
                  <span>Start with 2 wash plants minimum</span>
                </div>
                <div className="feature-point">
                  <span className="feature-icon">📈</span>
                  <span>Scale up to 200 plants by 2030</span>
                </div>
                <div className="feature-point">
                  <span className="feature-icon">💰</span>
                  <span>Real-time dividend calculations</span>
                </div>
              </div>
            </div>
          </div>

          <PremiumCalculator />

          <button
            onClick={() => setShowRegistration(true)}
            className="section-cta"
          >
            Purchase Shares
          </button>
        </div>
      </section>

      {/* Dynamic Zoom Meetings Section */}
      <DynamicMeetingSection onBookingClick={handleBookingClick} />

      {/* Gallery Section - Proof of Operations */}
      <section id="gallery" className="gallery-section">
        <div className="section-container">
          <div className="gallery-intro">
            <h2 className="section-title">Proof of Operations</h2>
            <p className="section-description">
              Regular photo updates from our operational countries showing real mining activities,
              equipment deployment, and gold recovery processes. This transparency ensures you can
              verify the legitimacy of our operations.
            </p>
          </div>

          <div className="gallery-grid">
            <div className="gallery-item" onClick={() => openLightbox(0)}>
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-1.jpg"
                alt="Aureus Alliance Holdings Professional Gold Mining Operations"
                className="gallery-image"
              />
            </div>

            <div className="gallery-item" onClick={() => openLightbox(1)}>
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-2.jpg"
                alt="Aureus Alliance Holdings Heavy Mining Equipment"
                className="gallery-image"
              />
            </div>

            <div className="gallery-item" onClick={() => openLightbox(2)}>
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-3.jpg"
                alt="Mining site location"
                className="gallery-image"
              />
            </div>

            <div className="gallery-item" onClick={() => openLightbox(3)}>
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-4.jpg"
                alt="Gold processing operations"
                className="gallery-image"
              />
            </div>

            <div className="gallery-item" onClick={() => openLightbox(4)}>
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-5.jpg"
                alt="Mining team operations"
                className="gallery-image"
              />
            </div>

            <div className="gallery-item" onClick={() => openLightbox(5)}>
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-6.jpg"
                alt="Gold extraction process"
                className="gallery-image"
              />
            </div>
          </div>

          <div className="gallery-note">
            <div className="note-icon">📸</div>
            <p>Photos updated monthly from our operational sites across Africa. All images are authentic documentation of our mining activities.</p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer-section">
        <div className="section-container">
          <div className="footer-content">
            {/* Left Column - Logo and Company Info */}
            <div className="footer-left">
              <div className="footer-brand">
                <img
                  src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
                  alt="Aureus Alliance Holdings"
                  className="footer-logo"
                />
                <h2 className="footer-company-name">Aureus Alliance Holdings</h2>
                <p className="footer-tagline">Real Gold • Real Shares • Real Ownership</p>
                <p className="footer-description">
                  Building wealth through legitimate gold mining operations across Africa.
                </p>
              </div>
            </div>

            {/* Center Column - Legal and Copyright */}
            <div className="footer-center">
              <div className="footer-legal">
                <p>&copy; 2025 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
                <p>CIPC Registration: 2025/368711/07 | Licensed Gold Mining Company</p>
                <p className="risk-warning">
                  <strong>Risk Warning:</strong> Share purchases involve significant risk. Mining operations are subject to operational, environmental, and market risks. Dividend estimates are calculations only and not guaranteed. Past performance does not guarantee future results. Only purchase what you can afford to lose.
                </p>
                <p style={{ color: '#666', fontSize: '11px', marginTop: '12px' }}>
                  Aureus Africa {getFullVersion()}
                </p>
              </div>
            </div>

            {/* Right Column - Sponsor Contact */}
            <div className="footer-right">
              <div className="sponsor-card">
                <div className="sponsor-avatar">
                  <img
                    src={affiliate.profile_image_url ? `${affiliate.profile_image_url}?t=${Date.now()}` : "https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/default-avatar.png"}
                    alt={affiliate.full_name || affiliate.username}
                  />
                </div>
                <div className="sponsor-details">
                  <p className="sponsor-name">{affiliate.full_name || affiliate.username}</p>
                  {(affiliate.phone_number || affiliate.phone) && (
                    <p className="sponsor-phone">
                      <a href={`https://wa.me/${formatPhoneNumber(affiliate.phone_number || affiliate.phone).replace(/[^0-9]/g, '')}`} target="_blank" rel="noopener noreferrer">
                        {formatPhoneNumber(affiliate.phone_number || affiliate.phone)}
                      </a>
                    </p>
                  )}
                  <p className="sponsor-note">Your sponsor - Contact for guidance and support</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          className="back-to-top-btn"
          onClick={scrollToTop}
          aria-label="Back to top"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4L4 12H8V20H16V12H20L12 4Z" fill="currentColor"/>
          </svg>
        </button>
      )}

      {/* Registration Modal */}
      {showRegistration && (
        <div className="modal-overlay" onClick={() => setShowRegistration(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Purchase Shares</h2>
              <button 
                className="close-button"
                onClick={() => setShowRegistration(false)}
              >
                ×
              </button>
            </div>
            <EmailRegistrationFormProgressive
              onRegistrationSuccess={handleRegistrationSuccess}
              userType="affiliate"
              defaultSponsor={affiliate.username}
            />
          </div>
        </div>
      )}

      {/* Contact Modal */}
      {showContactForm && (
        <div className="modal-overlay" onClick={() => setShowContactForm(false)}>
          <div className="modal-content contact-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Send Message to {affiliate.first_name || affiliate.username}</h2>
              <button
                className="close-button"
                onClick={() => setShowContactForm(false)}
              >
                ×
              </button>
            </div>

            <div className="contact-form">
              <p>Your message will be delivered directly to their dashboard.</p>

              <textarea
                value={contactMessage}
                onChange={(e) => setContactMessage(e.target.value)}
                placeholder="Type your message here..."
                rows={6}
                className="contact-textarea"
              />

              <div className="contact-actions">
                <button
                  onClick={() => setShowContactForm(false)}
                  className="cancel-btn"
                  disabled={contactSending}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSendMessage}
                  className="send-btn"
                  disabled={contactSending || !contactMessage.trim()}
                >
                  {contactSending ? 'Sending...' : 'Send Message'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Registration Success Modal */}
      {registrationSuccess && (
        <div className="modal-overlay">
          <div className="modal-content success-modal" onClick={(e) => e.stopPropagation()}>
            <div className="success-content">
              <div className="success-icon">✓</div>
              <h2>Welcome to the Team!</h2>
              <p>Your registration was successful! You're being redirected to your dashboard...</p>
              <div className="loading-indicator">
                <div className="spinner"></div>
                <span>Logging you in...</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Lightbox Modal */}
      {lightboxOpen && (
        <div className="lightbox-overlay" onClick={closeLightbox}>
          <div className="lightbox-content" onClick={(e) => e.stopPropagation()}>
            <button className="lightbox-close" onClick={closeLightbox}>×</button>
            <button className="lightbox-prev" onClick={prevImage}>‹</button>
            <button className="lightbox-next" onClick={nextImage}>›</button>

            <div className="lightbox-image-container">
              <img
                src={galleryImages[currentImageIndex].src}
                alt={galleryImages[currentImageIndex].alt}
                className="lightbox-image"
              />
              <div className="lightbox-caption">
                <h3>{galleryImages[currentImageIndex].title}</h3>
                <p>{currentImageIndex + 1} of {galleryImages.length}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Meeting Booking Modal */}
      <MeetingBookingModal
        meeting={selectedMeeting}
        isOpen={showBookingModal}
        onClose={handleCloseBookingModal}
        onBookingSuccess={handleBookingSuccess}
      />

      {/* Booking Success Modal */}
      <BookingSuccessModal
        isOpen={showBookingSuccess}
        confirmationCode={confirmationCode}
        onClose={handleCloseSuccessModal}
      />

      <style jsx="true">{`
        /* Reset & Base */
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        .aureus-experience {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          background: #0f1419;
          color: #ffffff;
          overflow-x: hidden;
          position: relative;
          min-height: 100vh;
        }

        /* Floating Navigation */
        .floating-nav {
          position: fixed;
          top: 20px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 1000;
          background: rgba(15, 20, 25, 0.95);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 193, 7, 0.2);
          border-radius: 50px;
          padding: 12px 24px;
          transition: all 0.3s ease;
          max-width: 90vw;
          width: auto;
        }

        /* Enhanced Floating Nav Mobile Fixes */
        @media screen and (max-width: 968px) {
          .floating-nav {
            top: 15px !important;
            left: 15px !important;
            right: 15px !important;
            transform: none !important;
            border-radius: 25px !important;
            padding: 10px 18px !important;
            max-width: none !important;
          }
        }

        @media screen and (max-width: 480px) {
          .floating-nav {
            top: 8px !important;
            left: 8px !important;
            right: 8px !important;
            padding: 8px 12px !important;
            border-radius: 16px !important;
          }
        }

        .nav-content {
          display: grid;
          grid-template-columns: 1fr 2fr 1fr;
          align-items: center;
          gap: 20px;
          width: 100%;
        }

        /* Nav Content Mobile Fixes */
        @media screen and (max-width: 968px) {
          .nav-content {
            grid-template-columns: 1fr auto !important;
            gap: 15px !important;
            justify-content: space-between !important;
          }
        }

        @media screen and (max-width: 640px) {
          .nav-content {
            gap: 10px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .nav-content {
            gap: 8px !important;
          }
        }

        .nav-actions {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 12px;
        }

        .nav-button {
          padding: 8px 20px;
          border-radius: 25px;
          text-decoration: none;
          font-weight: 600;
          transition: all 0.3s ease;
          border: 2px solid transparent;
        }

        .login-btn {
          color: #ffc107;
          border-color: rgba(255, 193, 7, 0.3);
          background: rgba(255, 193, 7, 0.1);
        }

        .login-btn:hover {
          background: rgba(255, 193, 7, 0.2);
          border-color: rgba(255, 193, 7, 0.5);
          transform: translateY(-2px);
        }

        .nav-brand {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 10px;
        }

        .nav-logo {
          height: 32px;
          width: auto;
          filter: brightness(1.2);
        }

        .brand-text {
          font-weight: 700;
          font-size: 1.1rem;
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .nav-menu {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 24px;
        }

        .nav-link {
          color: rgba(255, 255, 255, 0.8);
          text-decoration: none;
          font-weight: 500;
          font-size: 0.95rem;
          transition: all 0.3s ease;
          position: relative;
        }

        .nav-link:hover {
          color: #ffc107;
        }

        .cta-button {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          background: #ffc107;
          border: none;
          border-radius: 8px;
          color: #000;
          padding: 10px 20px;
          font-weight: 600;
          font-size: 0.9rem;
          cursor: pointer;
          transition: all 0.2s ease;
          text-decoration: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .cta-button:hover {
          background: #ffcd38;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .cta-button:active {
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Hero Section */
        .hero-section {
          position: relative;
          min-height: 100% !important;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0px !important;
          background: linear-gradient(180deg, #2f2000 0%, #0a0800 100%) !important;
          border-bottom: none;
          overflow: hidden;
        }



        .hero-container {
          max-width: 100%;
          width: 100%;
          padding: 0PX;
          margin: 0 auto;
        }



        /* Profile Section */
        .profile-section {
          padding: 100px 0;
          background: linear-gradient(360deg, #2f2000 0%, #0a0800 100%) !important;
          position: relative;
          border-bottom: none;
          overflow: hidden;
        }

        /* Professional Profile Hero Section */
        .profile-hero {
          border: none;
          border-radius: 0;
          padding: 30px 0;
          margin-bottom: 0px;
          position: relative;
          overflow: hidden;
          max-width: 100%;
        }



        .profile-content {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 40px;
          align-items: center;
          position: relative;
          z-index: 1;
          max-width: 1200px;
          margin: 0 auto;
        }

        /* Profile Content Mobile Fixes */
        @media screen and (max-width: 768px) {
          .profile-content {
            grid-template-columns: 1fr !important;
            gap: 30px !important;
            text-align: center !important;
            padding: 0 20px !important;
          }
        }

        @media screen and (max-width: 768px) {
          .profile-content {
            gap: 25px !important;
            padding: 0 15px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .profile-content {
            gap: 20px !important;
            padding: 0 10px !important;
          }
        }

        .profile-badge {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 32px;
        }

        .profile-avatar {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          object-fit: cover;
          border: 3px solid #ffc107;
          box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3);
        }

        .profile-name {
          font-size: 2.5rem;
          font-weight: 700;
          color: rgba(255, 255, 255, 0.95);
          margin: 0 0 8px 0;
          line-height: 1.2;
        }

        .profile-role {
          font-size: 1.1rem;
          font-weight: 500;
          color: #ffc107;
          margin: 0;
        }

        .profile-description {
          font-size: 1.1rem;
          line-height: 1.7;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 40px;
          max-width: 500px;
        }



        .metric-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .metric-number {
          font-size: 2rem;
          font-weight: 700;
          color: #ffc107;
          line-height: 1;
        }

        .metric-label {
          font-size: 0.9rem;
          color: rgba(255, 255, 255, 0.6);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .profile-actions {
          margin-top: 32px;
        }

        /* Profile Actions Mobile Fixes */
        @media screen and (max-width: 768px) {
          .profile-actions {
            margin-top: 24px !important;
            display: flex !important;
            flex-direction: column !important;
            gap: 16px !important;
            align-items: center !important;
            width: 100% !important;
          }
        }

        @media screen and (max-width: 480px) {
          .profile-actions {
            margin-top: 20px !important;
            gap: 14px !important;
          }
        }

        .primary-cta {
          background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
          color: #000;
          border: none;
          padding: 18px 36px;
          border-radius: 50px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          transition: all 0.3s ease;
          box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3);
        }

        .primary-cta:hover {
          transform: translateY(-2px);
          box-shadow: 0 12px 40px rgba(255, 193, 7, 0.4);
        }

        .cta-glow {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          transition: left 0.6s ease;
        }

        .primary-cta:hover .cta-glow {
          left: 100%;
        }

        .contact-options {
          display: flex;
          gap: 12px;
          justify-content: center;
          flex-wrap: wrap;
          margin-top: 16px;
        }

        .contact-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 20px;
          border-radius: 8px;
          font-size: 0.9rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          border: none;
          text-decoration: none;
        }

        /* Contact Button Mobile Fixes */
        @media screen and (max-width: 768px) {
          .contact-btn {
            width: 100% !important;
            max-width: 300px !important;
            justify-content: center !important;
            padding: 14px 24px !important;
            font-size: 1rem !important;
          }
        }

        @media screen and (max-width: 480px) {
          .contact-btn {
            padding: 12px 20px !important;
            font-size: 0.95rem !important;
            max-width: 280px !important;
          }
        }

        .whatsapp-btn {
          background: #25D366;
          color: white;
        }

        .whatsapp-btn:hover {
          background: #128C7E;
          transform: translateY(-1px);
        }

        .message-btn {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-btn:hover {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(255, 255, 255, 0.3);
          transform: translateY(-1px);
        }

        .profile-visual {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .video-container {
          position: relative;
          border-radius: 20px;
          overflow: hidden;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 193, 7, 0.2);
          margin-right: 50px !important;
          width: 450px !important;
          height: inherit !important;
          max-width: inherit;
        }

        .video-wrapper {
          position: relative;
          width: 100%;
          height: 225px;
          border-radius: 20px;
          overflow: hidden;
          border: 2px solid rgba(255, 193, 7, 0.3);
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .profile-video {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .video-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
          padding: 20px;
          display: flex;
          align-items: center;
          gap: 12px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .video-container:hover .video-overlay {
          opacity: 1;
        }

        .play-button {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: rgba(255, 193, 7, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          color: #000;
          flex-shrink: 0;
        }

        .video-title {
          color: rgba(255, 255, 255, 0.9);
          font-size: 0.9rem;
          font-weight: 500;
          margin: 0;
        }

        .hero-content {
          display: grid;
          grid-template-columns: 1fr 400px;
          align-items: center;
          gap: 60px;
          max-width: 1200px;
          margin: 0 auto;
          padding: 50px 5px !important;
          width: 100%;
        }

        /* Hero Mobile Fixes */
        @media screen and (max-width: 768px) {
          .hero-content {
            grid-template-columns: 1fr !important;
            gap: 40px !important;
            text-align: center !important;
            padding: 40px 20px !important;
          }
        }

        @media screen and (max-width: 768px) {
          .hero-content {
            gap: 30px !important;
            padding: 30px 15px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .hero-content {
            gap: 25px !important;
            padding: 25px 10px !important;
          }
        }

        .hero-text {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          text-align: left;
        }

        .hero-title {
          font-size: 3.5rem;
          font-weight: 800;
          line-height: 1.2;
          margin-bottom: 20px;
          display: flex;
          flex-direction: initial !important;
          gap: 4px;
        }

        /* Hero Title Mobile Fixes */
        @media screen and (max-width: 968px) {
          .hero-title {
            font-size: 2.8rem !important;
            flex-direction: column !important;
            text-align: center !important;
            gap: 8px !important;
          }

          .hero-text {
            align-items: center !important;
            text-align: center !important;
          }
        }

        @media screen and (max-width: 768px) {
          .hero-title {
            font-size: 2.4rem !important;
            margin-bottom: 16px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .hero-title {
            font-size: 2rem !important;
            margin-bottom: 14px !important;
          }
        }

        .title-line {
          color: rgba(255, 255, 255, 0.95);
          display: block;
        }

        .title-line:nth-child(2) {
          color: #ffc107;
        }



        .hero-description {
          font-size: 1.2rem;
          line-height: 1.6;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 20px !important;
          font-weight: 400;
          text-align: center;
          max-width: 600px;
        }

        .hero-metrics {
          display: flex;
          gap: 25px !important;
          margin-bottom: 20px;
          justify-content: center;
          flex-wrap: wrap;
        }

        /* iPad Override for Hero Metrics */
        @media only screen and (min-width: 769px) and (max-width: 1024px) {
          .hero-metrics {
            display: grid !important;
            grid-template-columns: repeat(4, 1fr) !important;
            gap: 20px !important;
            flex-wrap: nowrap !important;
            justify-content: stretch !important;
          }
        }

        .metric-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
        }

        .metric-number {
          font-size: 2.5rem;
          font-weight: 900;
          color: #ffc107;
          line-height: 1;
          margin-bottom: 8px;
        }

        .metric-label {
          font-size: 0.9rem;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .hero-actions {
          display: flex;
          gap: 20px;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
        }

        /* Hero Actions Mobile Fixes */
        @media screen and (max-width: 768px) {
          .hero-actions {
            flex-direction: column !important;
            gap: 16px !important;
            width: 100% !important;
          }
        }

        @media screen and (max-width: 480px) {
          .hero-actions {
            gap: 14px !important;
          }
        }

        .primary-cta {
          position: relative;
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          border: none;
          border-radius: 50px;
          color: #0f1419;
          padding: 16px 32px;
          font-weight: 700;
          font-size: 1.1rem;
          cursor: pointer;
          transition: all 0.3s ease;
          overflow: hidden;
          box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3);
        }

        /* Primary CTA Mobile Fixes */
        @media screen and (max-width: 768px) {
          .primary-cta {
            width: 100% !important;
            max-width: 300px !important;
            padding: 14px 28px !important;
            font-size: 1rem !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
          }
        }

        @media screen and (max-width: 480px) {
          .primary-cta {
            padding: 12px 24px !important;
            font-size: 0.95rem !important;
            max-width: 280px !important;
          }
        }

        .primary-cta:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 40px rgba(255, 193, 7, 0.5);
        }

        .cta-glow {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
          transition: left 0.6s ease;
        }

        .primary-cta:hover .cta-glow {
          left: 100%;
        }

        .secondary-cta {
          background: transparent;
          border: 2px solid rgba(255, 193, 7, 0.5);
          border-radius: 50px;
          color: #ffc107;
          padding: 14px 30px;
          font-weight: 600;
          font-size: 1.1rem;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        /* Secondary CTA Mobile Fixes */
        @media screen and (max-width: 768px) {
          .secondary-cta {
            width: 100% !important;
            max-width: 300px !important;
            padding: 12px 26px !important;
            font-size: 1rem !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
          }
        }

        @media screen and (max-width: 480px) {
          .secondary-cta {
            padding: 10px 22px !important;
            font-size: 0.95rem !important;
            max-width: 280px !important;
          }
        }

        .secondary-cta:hover {
          background: rgba(255, 193, 7, 0.1);
          border-color: #ffc107;
          transform: translateY(-2px);
        }

        .hero-visual {
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
        }

        .visual-container {
          position: relative;
          width: 400px;
          height: 400px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .hero-logo {
          width: 400px;
          height: 240px;
          object-fit: contain;
          filter: drop-shadow(0 0 40px rgba(255, 193, 7, 0.4));
          animation: logoFloat 6s ease-in-out infinite;
        }

        @keyframes logoFloat {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(2deg); }
        }

        /* Additional Styles */
        .metric-row {
          display: grid !important;
          justify-content: space-between;
          align-items: center;
          padding: 0px !important;
          border-bottom: 0px !important;
          margin: 0px !important;
        }

        .operation-metrics {
          display: flex;
          flex-direction: column;
          gap: 5px !important;
          margin-bottom: 15px !important;
        }

        .feature-icon {
          font-size: 1.5rem;
          width: 50px;
          height: 25px !important;
          display: flex;
          align-items: center;
          justify-content: center;
          background: none !important;
          border-radius: 10px;
          flex-shrink: 0;
        }

        .section-meeting {
          display: inline;
        }

        .gallery-intro {
          text-align: center;
          margin-bottom: 10px !important;
          padding: 5px !important;
        }

        .section-title {
          font-size: 2.5rem;
          font-weight: 700;
          margin-bottom: 5px !important;
          color: rgba(255, 255, 255, 0.95);
          line-height: inherit !important;
        }

        .section-description {
          font-size: 1.1rem;
          line-height: inherit !important;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 5px !important;
        }

        .gallery-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 15px;
          margin-bottom: 60px;
        }

        /* Gallery Grid Mobile Fixes */
        @media screen and (max-width: 768px) {
          .gallery-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
            gap: 12px !important;
            margin-bottom: 40px !important;
          }
        }

        @media screen and (max-width: 640px) {
          .gallery-grid {
            grid-template-columns: 1fr !important;
            gap: 15px !important;
            max-width: 400px !important;
            margin: 0 auto 30px auto !important;
          }
        }

        @media screen and (max-width: 480px) {
          .gallery-grid {
            gap: 12px !important;
            max-width: 350px !important;
            margin-bottom: 25px !important;
          }
        }

        .section-container {
          max-width: 1200px;
          width: 100%;
          padding: 0 0px;
          margin: 0 auto;
        }

        .footer-section {
          background: linear-gradient(180deg, #2d1f02 0%, #000000 100%) !important;
          padding: 40px 0 !important;
          border-top: 0;
          position: relative;
          bottom: 0;
        }

        .logo-glow {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 350px;
          height: 350px;
          background: radial-gradient(circle, rgba(255, 193, 7, 0.2) 0%, transparent 70%);
          border-radius: 50%;
          animation: glowPulse 4s ease-in-out infinite;
        }

        @keyframes glowPulse {
          0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
          50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
        }

        /* Section Styles */
        .about-section,
        .operations-section,
        .calculator-section {
          padding: 120px 0;
          position: relative;
        }

        .about-section {
          background: linear-gradient(360deg, #2d1f02 0%, #000000 100%) !important;
          padding: 50px 0;
          position: relative;
        }

        .operations-section {
          background: linear-gradient(180deg, #2d1f02 0%, #000000 100%) !important;
          padding: 50px 0;
          position: relative;
        }

        /* Operations Section Mobile Fixes */
        @media screen and (max-width: 768px) {
          .operations-section {
            padding: 40px 0 !important;
          }

          .operations-section .section-text {
            padding: 0 20px !important;
            text-align: center !important;
            max-width: 100% !important;
            margin: 0 auto !important;
          }

          .operations-section .section-content {
            gap: 30px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .operations-section {
            padding: 30px 0 !important;
          }

          .operations-section .section-text {
            padding: 0 15px !important;
          }

          .operations-section .section-content {
            gap: 25px !important;
          }
        }

        .calculator-section {
          background: linear-gradient(360deg, #2d1f02 0%, #000000 100%) !important;
          padding: 100px 0;
          text-align: center;
        }

        .gallery-section {
          background: linear-gradient(360deg, #2d1f02 0%, #000000 100%) !important;
        }

        .meetings-section {
          background: linear-gradient(180deg, #2d1f02 0%, #000000 100%) !important;
          padding: 50px 0;
        }

        /* Contact Details Styling */
        .contact-details {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin: 20px 0;
        }

        .contact-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          background: rgba(255, 193, 7, 0.1);
          border: 1px solid rgba(255, 193, 7, 0.3);
          border-radius: 20px;
          font-size: 14px;
        }

        .contact-item a {
          color: #ffc107;
          text-decoration: none;
          font-weight: 500;
        }

        .contact-item a:hover {
          color: #ffcd39;
          text-decoration: underline;
        }



        .contact-icon {
          font-size: 16px;
        }

        /* Profile Tags Styling */
        .profile-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-top: 16px;
        }

        .profile-tag {
          padding: 6px 12px;
          background: rgba(59, 130, 246, 0.2);
          border: 1px solid rgba(59, 130, 246, 0.4);
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;
          color: #60a5fa;
        }

        /* Large Profile Image Styling */
        .profile-image-container {
          width: 400px;
          height: 400px;
          border-radius: 0;
          overflow: hidden;
          box-shadow: none;
          margin: 0 auto;
        }

        .large-profile-image {
          width: 100%;
          height: 100%;
          position: relative;
        }

        .large-profile-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        /* Meetings Section Styling */
        .meetings-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
          gap: 30px;
          margin: 20px 0 !important;
        }

        /* Meetings Grid Mobile Fixes */
        @media screen and (max-width: 768px) {
          .meetings-grid {
            grid-template-columns: 1fr !important;
            gap: 20px !important;
            margin: 15px 0 !important;
          }
        }

        @media screen and (max-width: 480px) {
          .meetings-grid {
            gap: 16px !important;
            margin: 12px 0 !important;
          }
        }

        .meeting-card {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          padding: 30px;
          backdrop-filter: blur(10px);
        }

        .meeting-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 20px;
        }

        .meeting-icon {
          font-size: 24px;
          width: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 193, 7, 0.2);
          border-radius: 12px;
        }

        .meeting-header h3 {
          font-size: 1.5rem;
          font-weight: 700;
          color: #ffc107;
          margin: 0;
        }

        .meeting-content p {
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 20px;
          line-height: 1.6;
        }

        .meeting-video {
          margin: 20px 0;
          border-radius: 8px;
          overflow: hidden;
        }

        .meeting-details {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-top: 16px;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
        }

        .meeting-schedule {
          display: flex;
          flex-direction: column;
          gap: 12px;
          margin: 20px 0;
        }

        .schedule-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .schedule-label {
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
        }

        .schedule-value {
          color: #ffc107;
          font-weight: 600;
        }

        .meeting-topics {
          margin: 20px 0;
        }

        .meeting-topics h5 {
          color: #ffc107;
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
        }

        .meeting-topics ul {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .meeting-topics li {
          padding: 6px 0;
          color: rgba(255, 255, 255, 0.8);
          position: relative;
          padding-left: 20px;
        }

        .meeting-topics li:before {
          content: '•';
          color: #ffc107;
          position: absolute;
          left: 0;
          font-weight: bold;
        }

        .meeting-register-btn {
          width: 100%;
          padding: 12px 24px;
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          border: none;
          border-radius: 8px;
          color: #000;
          font-weight: 600;
          font-size: 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-top: 20px;
        }

        .meeting-register-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
        }

        .meeting-cta {
          text-align: center;
          margin-top: 40px;
          padding: 30px;
          background: rgba(255, 193, 7, 0.05);
          border: 1px solid rgba(255, 193, 7, 0.2);
          border-radius: 16px;
        }

        .meeting-cta p {
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 20px;
          font-size: 18px;
        }

        .section-container {
          max-width: 1200px; /* Consistent with hero container */
          width: 100%;
          padding: 0 40px;
          margin: 0 auto;
        }

        .section-content {
          display: grid !important;
          grid-template-columns: 1fr 1fr;
          gap: 60px;
          align-items: center;
        }

        .section-content.reverse {
          grid-template-columns: 1fr 1fr;
        }

        .section-content.reverse .section-visual {
          order: -1;
        }

        /* Section Content Mobile Fixes */
        @media screen and (max-width: 768px) {
          .section-content {
            grid-template-columns: 1fr !important;
            gap: 40px !important;
            text-align: center !important;
          }

          .section-content.reverse .section-visual {
            order: 0 !important;
          }
        }

        @media screen and (max-width: 768px) {
          .section-content {
            gap: 30px !important;
            padding: 0 15px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .section-content {
            gap: 25px !important;
            padding: 0 10px !important;
          }
        }

        .section-title {
          font-size: 2.5rem;
          font-weight: 700;
          line-height: 1.3;
          margin-bottom: 16px;
          color: rgba(255, 255, 255, 0.95);
        }

        /* Section Title Mobile Fixes */
        @media screen and (max-width: 768px) {
          .section-title {
            font-size: 2rem !important;
            line-height: 1.2 !important;
            margin-bottom: 12px !important;
            text-align: center !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
          }
        }

        @media screen and (max-width: 480px) {
          .section-title {
            font-size: 1.8rem !important;
            margin-bottom: 10px !important;
            padding: 0 10px !important;
          }
        }

        .section-description {
          font-size: 1.1rem;
          line-height: 1.6;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 32px;
        }

        /* Section Description Mobile Fixes */
        @media screen and (max-width: 768px) {
          .section-description {
            font-size: 1rem !important;
            line-height: 1.5 !important;
            margin-bottom: 20px !important;
            text-align: center !important;
            padding: 0 15px !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
          }
        }

        @media screen and (max-width: 480px) {
          .section-description {
            font-size: 0.95rem !important;
            margin-bottom: 16px !important;
            padding: 0 10px !important;
          }
        }

        .feature-list {
          display: flex;
          flex-direction: column;
          gap: 24px;
        }

        .feature-item {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          padding: 20px;
          background: rgba(255, 255, 255, 0.03);
          border: 1px solid rgba(255, 193, 7, 0.1);
          border-radius: 16px;
          transition: all 0.3s ease;
        }

        .feature-item:hover {
          background: rgba(255, 255, 255, 0.05);
          border-color: rgba(255, 193, 7, 0.3);
          transform: translateY(-2px);
        }

        .feature-icon {
          font-size: 2rem;
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          border-radius: 12px;
          flex-shrink: 0;
        }

        .feature-text h4 {
          font-size: 1.2rem;
          font-weight: 700;
          color: #ffc107;
          margin-bottom: 8px;
        }

        .feature-text p {
          color: rgba(255, 255, 255, 0.7);
          line-height: 1.5;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }

        /* Stats Grid Mobile Fixes */
        @media screen and (max-width: 640px) {
          .stats-grid {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
            max-width: 300px !important;
            margin: 0 auto !important;
          }
        }

        @media screen and (max-width: 480px) {
          .stats-grid {
            gap: 14px !important;
            max-width: 280px !important;
          }
        }

        .stat-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 193, 7, 0.2);
          border-radius: 20px;
          padding: 32px 24px;
          text-align: center;
          transition: all 0.3s ease;
          width: 160px !important;
          margin-left: 50px !important;
        }

        .stat-card:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 193, 7, 0.4);
          transform: translateY(-4px);
        }

        .stat-number {
          font-size: 2.5rem;
          font-weight: 900;
          color: #ffc107;
          line-height: 1;
          margin-bottom: 8px;
        }

        .stat-text {
          font-size: 1rem;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .video-container {
          position: relative;
          border-radius: 20px;
          overflow: hidden;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .operations-video {
          width: 100%;
          height: 300px;
          object-fit: cover;
        }

        /* Operations Video Mobile Fixes */
        @media screen and (max-width: 768px) {
          .operations-video {
            height: 250px !important;
            border-radius: 12px !important;
          }

          .video-container {
            margin: 0 auto !important;
            max-width: 400px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .operations-video {
            height: 200px !important;
            border-radius: 10px !important;
          }

          .video-container {
            max-width: 350px !important;
          }
        }

        .video-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.3);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .video-container:hover .video-overlay {
          opacity: 1;
        }

        .play-button {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 2rem;
          color: #0f1419;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .play-button:hover {
          transform: scale(1.1);
        }

        .operation-metrics {
          display: flex;
          flex-direction: column;
          gap: 16px;
          margin-bottom: 32px;
        }

        /* Operation Metrics Mobile Fixes */
        @media screen and (max-width: 768px) {
          .operation-metrics {
            gap: 12px !important;
            margin-bottom: 20px !important;
            padding: 0 15px !important;
            text-align: center !important;
          }
        }

        @media screen and (max-width: 480px) {
          .operation-metrics {
            gap: 10px !important;
            margin-bottom: 16px !important;
            padding: 0 10px !important;
          }
        }

        .metric-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 0;
          border-bottom: 1px solid rgba(255, 193, 7, 0.1);
        }

        /* Metric Row Mobile Fixes */
        @media screen and (max-width: 768px) {
          .metric-row {
            flex-direction: column !important;
            gap: 4px !important;
            padding: 10px 0 !important;
            text-align: center !important;
          }
        }

        @media screen and (max-width: 480px) {
          .metric-row {
            padding: 8px 0 !important;
            gap: 2px !important;
          }
        }

        .metric-label {
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
        }

        .metric-value {
          color: #ffc107;
          font-weight: 700;
          font-size: 1.1rem;
        }

        /* Metric Label/Value Mobile Fixes */
        @media screen and (max-width: 768px) {
          .metric-label {
            font-size: 0.9rem !important;
            margin-bottom: 2px !important;
          }

          .metric-value {
            font-size: 1rem !important;
            font-weight: 700 !important;
          }
        }

        @media screen and (max-width: 480px) {
          .metric-label {
            font-size: 0.85rem !important;
          }

          .metric-value {
            font-size: 0.95rem !important;
          }
        }

        .section-cta {
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          border: none;
          border-radius: 50px;
          color: #0f1419;
          padding: 16px 32px;
          font-weight: 700;
          font-size: 1.1rem;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3);
        }

        /* Section CTA Mobile Fixes */
        @media screen and (max-width: 768px) {
          .section-cta {
            width: 100% !important;
            max-width: 300px !important;
            margin: 0 auto !important;
            display: block !important;
            text-align: center !important;
            padding: 14px 28px !important;
            font-size: 1rem !important;
          }
        }

        @media screen and (max-width: 480px) {
          .section-cta {
            max-width: 280px !important;
            padding: 12px 24px !important;
            font-size: 0.95rem !important;
          }
        }

        .section-cta:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 40px rgba(255, 193, 7, 0.5);
        }

        .calculator-header {
          text-align: center;
          margin-bottom: 60px;
        }

        /* Calculator Section */
        .calculator-section {
          background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
          padding: 100px 0;
          text-align: center;
        }

        .calculator-section .section-title {
          margin-bottom: 24px;
        }

        .calculator-section .section-description {
          margin-bottom: 60px;
          max-width: 800px;
          margin-left: auto;
          margin-right: auto;
        }

        .calculator-section .section-cta {
          margin-top: 60px;
        }

        .calculator-intro, .calculator-assumptions, .calculator-tool, .example-scenarios {
          padding: 0px !important;
        }

        .calculator-intro {
          margin-bottom: 0px;
        }

        .intro-content {
          max-width: 800px;
          margin: 0 auto;
          text-align: center;
        }

        .intro-features {
          display: flex;
          justify-content: center;
          gap: 20px !important;
          margin-top: 5px !important;
          flex-wrap: wrap;
          margin-bottom: 15px !important;
        }

        .feature-point {
          display: flex;
          align-items: center;
          gap: 12px;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
        }

        .feature-point .feature-icon {
          font-size: 1.2rem;
        }

        /* Premium Calculator */
        .premium-calculator {
          max-width: 900px;
          margin: 0 auto;
          background: rgba(255, 255, 255, 0.02);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 32px;
          transition: all 0.2s ease;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        /* Mobile-first responsive design for premium calculator */
        @media screen and (max-width: 768px) {
          .premium-calculator {
            padding: 24px 20px !important;
            margin: 0 10px !important;
            max-width: calc(100% - 20px) !important;
          }
        }

        @media screen and (max-width: 640px) {
          .premium-calculator {
            padding: 22px 18px !important;
            margin: 0 8px !important;
            max-width: calc(100% - 16px) !important;
          }
        }

        @media screen and (max-width: 480px) {
          .premium-calculator {
            padding: 20px 16px !important;
            margin: 0 5px !important;
            max-width: calc(100% - 10px) !important;
          }
        }

        .premium-calculator:hover {
          background: rgba(255, 255, 255, 0.04);
          border-color: rgba(255, 255, 255, 0.15);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .calc-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 24px;
          padding-bottom: 16px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .calc-icon {
          font-size: 1.5rem;
          width: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #ffc107;
          border-radius: 8px;
          color: #000;
        }

        .calc-title h3 {
          font-size: 1.3rem;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.95);
          margin: 0 0 4px 0;
        }

        .calc-title p {
          color: rgba(255, 255, 255, 0.6);
          margin: 0;
          font-size: 0.85rem;
        }

        .calc-inputs {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 24px;
          margin-bottom: 40px;
        }

        /* Mobile-first responsive design for calculator */
        @media screen and (max-width: 768px) {
          .calc-inputs {
            grid-template-columns: 1fr !important;
            gap: 20px !important;
          }
        }

        @media screen and (max-width: 640px) {
          .calc-inputs {
            grid-template-columns: 1fr !important;
            gap: 18px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .calc-inputs {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
          }
        }

        .input-section {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .input-section label {
          color: #ffc107;
          font-weight: 600;
          font-size: 0.95rem;
          margin-bottom: 8px;
        }

        .input-wrapper {
          position: relative;
          display: flex;
          align-items: center;
        }

        .premium-input {
          flex: 1;
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid rgba(255, 193, 7, 0.3);
          border-radius: 12px;
          padding: 14px 16px;
          color: white;
          font-size: 1.1rem;
          font-weight: 600;
          transition: all 0.3s ease;
        }

        .premium-input:focus {
          outline: none;
          border-color: #ffc107;
          background: rgba(255, 255, 255, 0.12);
          box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
        }

        .input-suffix {
          position: absolute;
          right: 16px;
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.9rem;
          pointer-events: none;
        }

        .premium-select {
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid rgba(255, 193, 7, 0.3);
          border-radius: 12px;
          padding: 14px 16px;
          color: white;
          font-size: 1rem;
          font-weight: 500;
          transition: all 0.3s ease;
          cursor: pointer;
        }

        .premium-select:focus {
          outline: none;
          border-color: #ffc107;
          background: rgba(255, 255, 255, 0.12);
          box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
        }

        .premium-select option {
          background: #1a1f2e;
          color: white;
          padding: 8px;
        }

        .input-info {
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.85rem;
          margin-top: 4px;
        }

        /* Mobile styles for input sections */
        @media screen and (max-width: 768px) {
          .input-section {
            margin-bottom: 8px;
          }

          .premium-input,
          .premium-select {
            font-size: 1rem !important;
            padding: 12px 14px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .premium-input,
          .premium-select {
            font-size: 0.95rem !important;
            padding: 10px 12px !important;
          }
        }

        .calc-results {
          margin-top: 32px;
        }

        .result-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;
          margin-bottom: 24px;
        }

        /* Mobile-first responsive design for result grid */
        @media screen and (max-width: 768px) {
          .result-grid {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
          }
        }

        @media screen and (max-width: 640px) {
          .result-grid {
            grid-template-columns: 1fr !important;
            gap: 14px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .result-grid {
            grid-template-columns: 1fr !important;
            gap: 12px !important;
          }
        }

        .result-card {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 193, 7, 0.2);
          border-radius: 16px;
          padding: 24px;
          text-align: center;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .result-card:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 193, 7, 0.4);
          transform: translateY(-2px);
        }

        .result-card.primary {
          background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 143, 0, 0.1));
          border-color: rgba(255, 193, 7, 0.4);
        }

        .result-card.primary:hover {
          background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 143, 0, 0.15));
          border-color: rgba(255, 193, 7, 0.6);
        }

        .result-icon {
          font-size: 1.8rem;
          margin-bottom: 12px;
          display: block;
        }

        .result-content {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .result-value {
          font-size: 1.8rem;
          font-weight: 900;
          color: #ffc107;
          line-height: 1;
          margin-bottom: 8px;
        }

        .result-card.primary .result-value {
          font-size: 2.2rem;
          color: #fff;
          text-shadow: 0 2px 8px rgba(255, 193, 7, 0.5);
        }

        .result-label {
          color: rgba(255, 255, 255, 0.9);
          font-weight: 600;
          font-size: 0.95rem;
          margin-bottom: 4px;
        }

        .result-sub {
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.85rem;
          font-weight: 500;
        }

        /* Mobile styles for result cards */
        @media screen and (max-width: 768px) {
          .result-card {
            padding: 20px 16px !important;
            margin-bottom: 12px;
          }

          .result-value {
            font-size: 1.6rem !important;
          }

          .result-card.primary .result-value {
            font-size: 1.9rem !important;
          }
        }

        @media screen and (max-width: 480px) {
          .result-card {
            padding: 18px 14px !important;
          }

          .result-value {
            font-size: 1.4rem !important;
          }

          .result-card.primary .result-value {
            font-size: 1.7rem !important;
          }

          .result-icon {
            font-size: 1.5rem !important;
          }
        }

        .calc-disclaimer {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 16px 20px;
          background: rgba(255, 193, 7, 0.05);
          border: 1px solid rgba(255, 193, 7, 0.2);
          border-radius: 12px;
          margin-top: 24px;
        }

        .disclaimer-icon {
          font-size: 1.2rem;
          flex-shrink: 0;
          margin-top: 2px;
        }

        .calc-disclaimer p {
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.85rem;
          line-height: 1.5;
          margin: 0;
        }

        /* CRITICAL MOBILE OVERRIDES - High Specificity */
        @media screen and (max-width: 768px) {
          .premium-calculator .calc-inputs {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 20px !important;
          }

          .premium-calculator .result-grid {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 16px !important;
          }

          /* Force mobile layout */
          .calc-inputs {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 20px !important;
          }

          .result-grid {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 16px !important;
          }
        }

        @media screen and (max-width: 640px) {
          .premium-calculator .calc-inputs {
            grid-template-columns: 1fr !important;
            gap: 18px !important;
          }

          .premium-calculator .result-grid {
            grid-template-columns: 1fr !important;
            gap: 14px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .premium-calculator .calc-inputs {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
          }

          .premium-calculator .result-grid {
            grid-template-columns: 1fr !important;
            gap: 12px !important;
          }
        }

        /* Sponsor Profile Card */
        .sponsor-profile {
          background: rgba(255, 255, 255, 0.03);
          border: 1px solid rgba(255, 193, 7, 0.2);
          border-radius: 24px;
          padding: 40px;
          backdrop-filter: blur(10px);
          text-align: center;
          transition: all 0.3s ease;
          height: fit-content;
        }

        .sponsor-profile:hover {
          background: rgba(255, 255, 255, 0.05);
          border-color: rgba(255, 193, 7, 0.4);
          transform: translateY(-4px);
        }

        .sponsor-avatar {
          position: relative;
          display: inline-block;
          margin-bottom: 20px;
        }

        .avatar-image {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          border: 3px solid #ffc107;
          object-fit: cover;
        }

        .status-indicator {
          position: absolute;
          bottom: 5px;
          right: 5px;
          display: flex;
          align-items: center;
          gap: 4px;
          background: rgba(76, 175, 80, 0.9);
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.75rem;
          color: white;
          font-weight: 600;
        }

        .status-dot {
          width: 6px;
          height: 6px;
          background: white;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }

        .sponsor-info h3 {
          font-size: 1.5rem;
          font-weight: 700;
          color: #ffc107;
          margin: 0 0 8px 0;
        }

        .sponsor-title {
          color: rgba(255, 255, 255, 0.8);
          font-size: 1rem;
          margin: 0 0 4px 0;
        }

        .sponsor-username {
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.9rem;
          margin: 0 0 20px 0;
        }

        .sponsor-stats {
          display: grid;
          grid-template-columns: 1fr;
          gap: 12px;
          margin-bottom: 20px;
        }

        .sponsor-stats .stat-item {
          padding: 12px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          border: 1px solid rgba(255, 193, 7, 0.1);
        }

        .sponsor-stats .stat-number {
          display: block;
          font-size: 1.2rem;
          font-weight: 700;
          color: #ffc107;
          margin-bottom: 4px;
        }

        .sponsor-stats .stat-label {
          font-size: 0.85rem;
          color: rgba(255, 255, 255, 0.7);
        }

        .contact-sponsor-btn {
          display: inline-block;
          background: transparent;
          border: 2px solid rgba(255, 193, 7, 0.5);
          border-radius: 50px;
          color: #ffc107;
          padding: 12px 24px;
          font-weight: 600;
          text-decoration: none;
          transition: all 0.3s ease;
        }

        .contact-sponsor-btn:hover {
          background: rgba(255, 193, 7, 0.1);
          border-color: #ffc107;
          transform: translateY(-2px);
        }

        .calculator-features {
          display: flex;
          flex-direction: column;
          gap: 20px;
          margin-bottom: 32px;
        }

        .feature-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          background: rgba(255, 255, 255, 0.03);
          border-radius: 12px;
          border: 1px solid rgba(255, 193, 7, 0.1);
          transition: all 0.3s ease;
        }

        .feature-item:hover {
          background: rgba(255, 255, 255, 0.05);
          border-color: rgba(255, 193, 7, 0.3);
          transform: translateX(8px);
        }

        .feature-icon {
          font-size: 1.5rem;
          width: 50px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 193, 7, 0.1);
          border-radius: 10px;
          flex-shrink: 0;
        }

        .feature-text h4 {
          font-size: 1.1rem;
          font-weight: 700;
          color: #ffc107;
          margin: 0 0 6px 0;
        }

        .feature-text p {
          color: rgba(255, 255, 255, 0.8);
          margin: 0;
          line-height: 1.4;
          font-size: 0.95rem;
        }

        /* Profile Section */
        .profile-section {
          padding: 100px 0;
          background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
        }

        .profile-intro {
          margin-bottom: 40px;
        }

        .sponsor-benefits {
          display: flex;
          flex-direction: column;
          gap: 24px;
          margin-bottom: 40px;
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          background: rgba(255, 255, 255, 0.03);
          border-radius: 16px;
          border: 1px solid rgba(255, 193, 7, 0.1);
          transition: all 0.3s ease;
        }

        .benefit-item:hover {
          background: rgba(255, 255, 255, 0.05);
          border-color: rgba(255, 193, 7, 0.3);
          transform: translateX(8px);
        }

        .benefit-icon {
          font-size: 2rem;
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 193, 7, 0.1);
          border-radius: 12px;
          flex-shrink: 0;
        }

        .benefit-text h4 {
          font-size: 1.2rem;
          font-weight: 700;
          color: #ffc107;
          margin: 0 0 8px 0;
        }

        .benefit-text p {
          color: rgba(255, 255, 255, 0.8);
          margin: 0;
          line-height: 1.5;
        }

        .profile-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 193, 7, 0.2);
          border-radius: 24px;
          padding: 40px;
          transition: all 0.3s ease;
          height: fit-content;
        }

        .profile-card:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 193, 7, 0.4);
          transform: translateY(-4px);
        }

        .profile-header {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 1px solid rgba(255, 193, 7, 0.2);
        }

        .profile-image {
          position: relative;
          flex-shrink: 0;
        }

        .avatar {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          border: 3px solid #ffc107;
          object-fit: cover;
        }

        .profile-status {
          position: absolute;
          bottom: -5px;
          right: -5px;
          display: flex;
          align-items: center;
          gap: 4px;
          background: rgba(76, 175, 80, 0.9);
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.75rem;
          color: white;
          font-weight: 600;
        }

        .status-dot {
          width: 6px;
          height: 6px;
          background: white;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }

        .profile-info h3 {
          font-size: 1.5rem;
          font-weight: 900;
          color: #ffc107;
          margin: 0 0 8px 0;
        }

        .profile-title {
          font-size: 1rem;
          color: rgba(255, 255, 255, 0.8);
          margin: 0 0 4px 0;
        }

        .profile-username {
          font-size: 0.9rem;
          color: rgba(255, 255, 255, 0.6);
          font-weight: 500;
          margin: 0;
        }

        .profile-stats {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;
          margin-bottom: 24px;
        }

        .profile-stats .stat-item {
          text-align: center;
          padding: 16px;
          background: rgba(255, 255, 255, 0.03);
          border-radius: 12px;
          border: 1px solid rgba(255, 193, 7, 0.1);
        }

        .profile-stats .stat-value {
          font-size: 1.4rem;
          font-weight: 900;
          color: #ffc107;
          line-height: 1;
          display: block;
        }

        .profile-stats .stat-label {
          font-size: 0.8rem;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-top: 6px;
          display: block;
        }

        .profile-description {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 16px;
          width: 100% !important;
          max-width: 100%;
          display: block;
          margin-bottom: 24px;
        }

        .profile-description p {
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.6;
          margin: 0;
        }

        .profile-actions {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .profile-cta {
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          border: none;
          border-radius: 50px;
          color: #0f1419;
          padding: 14px 28px;
          font-weight: 700;
          font-size: 1rem;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
          width: 100%;
        }

        .profile-cta:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(255, 193, 7, 0.5);
        }

        .contact-sponsor {
          background: transparent;
          border: 2px solid rgba(255, 193, 7, 0.5);
          border-radius: 50px;
          color: #ffc107;
          padding: 12px 26px;
          font-weight: 600;
          font-size: 1rem;
          text-decoration: none;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
        }

        .contact-sponsor:hover {
          background: rgba(255, 193, 7, 0.1);
          border-color: #ffc107;
          transform: translateY(-2px);
        }

        /* Gallery Styles */
        .gallery-intro {
          text-align: center;
          margin-bottom: 10px !important;
          padding: 5px !important;
        }

        .gallery-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 30px;
          margin-bottom: 60px;
        }

        .gallery-item {
          position: relative;
          border-radius: 16px;
          overflow: hidden;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.3s ease;
          cursor: pointer;
        }

        .gallery-item:hover {
          transform: translateY(-8px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
          border-color: rgba(255, 215, 0, 0.3);
        }

        .gallery-image {
          width: 100%;
          height: 250px;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .gallery-item:hover .gallery-image {
          transform: scale(1.05);
        }

        .gallery-item {
          cursor: pointer;
          transition: transform 0.3s ease;
        }

        .gallery-item:hover {
          transform: translateY(-5px);
        }

        /* Lightbox Styles */
        .lightbox-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.95);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          backdrop-filter: blur(5px);
        }

        .lightbox-content {
          position: relative;
          max-width: 90vw;
          max-height: 90vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .lightbox-image-container {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .lightbox-image {
          max-width: 100%;
          max-height: 80vh;
          object-fit: contain;
          border-radius: 8px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }

        .lightbox-caption {
          margin-top: 20px;
          text-align: center;
          color: white;
        }

        .lightbox-caption h3 {
          font-size: 1.5rem;
          font-weight: 600;
          color: #ffd700;
          margin: 0 0 8px 0;
        }

        .lightbox-caption p {
          font-size: 1rem;
          opacity: 0.8;
          margin: 0;
        }

        .lightbox-close {
          position: absolute;
          top: 20px;
          right: 20px;
          background: rgba(255, 255, 255, 0.1);
          border: none;
          color: white;
          font-size: 2rem;
          width: 50px;
          height: 50px;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }

        .lightbox-close:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.1);
        }

        .lightbox-prev,
        .lightbox-next {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(255, 255, 255, 0.1);
          border: none;
          color: white;
          font-size: 2rem;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }

        .lightbox-prev {
          left: 20px;
        }

        .lightbox-next {
          right: 20px;
        }

        .lightbox-prev:hover,
        .lightbox-next:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-50%) scale(1.1);
        }

        .gallery-note {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          padding: 20px;
          background: rgba(255, 215, 0, 0.1);
          border: 1px solid rgba(255, 215, 0, 0.2);
          border-radius: 12px;
          text-align: center;
        }

        .note-icon {
          font-size: 1.5rem;
        }

        .gallery-note p {
          margin: 0;
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.95rem;
        }

        /* Clean Footer */
        .footer-section {
          background: linear-gradient(180deg, #2d1f02 0%, #000000 100%) !important;
          padding: 40px 0;
          border-top: 0;
        }

        .footer-content {
          display: grid;
          grid-template-columns: 1fr 2fr 1fr;
          gap: 40px;
          align-items: start;
          max-width: 1200px;
          margin: 0 auto;
        }

        /* Footer Content Mobile Fixes */
        @media screen and (max-width: 968px) {
          .footer-content {
            grid-template-columns: 1fr !important;
            gap: 30px !important;
            text-align: center !important;
            padding: 0 20px !important;
          }
        }

        @media screen and (max-width: 768px) {
          .footer-content {
            gap: 25px !important;
            padding: 0 15px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .footer-content {
            gap: 20px !important;
            padding: 0 10px !important;
          }
        }

        /* Footer Left Column */
        .footer-left {
          display: flex;
          flex-direction: column;
        }

        /* Footer Left Mobile Fixes */
        @media screen and (max-width: 968px) {
          .footer-left {
            align-items: center !important;
            text-align: center !important;
          }
        }

        .footer-brand {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
        }

        /* Footer Brand Mobile Fixes */
        @media screen and (max-width: 968px) {
          .footer-brand {
            align-items: center !important;
            text-align: center !important;
          }
        }

        .footer-logo {
          height: 80px;
          width: auto;
          margin: 0 auto !important;
          margin-bottom: 10px;
        }

        .footer-company-name {
          color: #ffc107;
          font-size: 1.5rem !important;
          font-weight: 700;
          margin: 0 0 5px 0;
          line-height: inherit !important;
        }

        /* Footer Company Name Mobile Fixes */
        @media screen and (max-width: 768px) {
          .footer-company-name {
            font-size: 1.3rem !important;
            text-align: center !important;
          }
        }

        @media screen and (max-width: 480px) {
          .footer-company-name {
            font-size: 1.2rem !important;
          }
        }

        .footer-tagline {
          color: rgba(255, 255, 255, 0.9);
          font-size: 0.94rem !important;
          font-weight: 500;
          margin: 0;
          margin-bottom: 0px !important;
        }

        .footer-description {
          color: rgba(255, 255, 255, 0.7);
          line-height: inherit;
          font-size: 0.9rem;
          margin: 0;
          text-align: center !important;
          max-width: 100% !important;
        }

        /* Footer Center Column */
        .footer-center {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
        }

        .footer-legal {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .footer-legal p {
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.85rem;
          margin: 0;
          line-height: 1.4;
        }

        /* Footer Legal Mobile Fixes */
        @media screen and (max-width: 968px) {
          .footer-legal p {
            text-align: center !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
          }
        }

        @media screen and (max-width: 768px) {
          .footer-legal p {
            font-size: 0.8rem !important;
            padding: 0 10px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .footer-legal p {
            font-size: 0.75rem !important;
            padding: 0 5px !important;
          }
        }

        .risk-warning {
          color: rgba(255, 255, 255, 0.7) !important;
          font-size: 0.75rem !important;
          line-height: 1.3 !important;
          margin-top: 10px !important;
        }

        /* Risk Warning Mobile Fixes */
        @media screen and (max-width: 968px) {
          .risk-warning {
            text-align: center !important;
            padding: 0 15px !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
          }
        }

        @media screen and (max-width: 768px) {
          .risk-warning {
            font-size: 0.7rem !important;
            line-height: 1.4 !important;
            padding: 0 10px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .risk-warning {
            font-size: 0.65rem !important;
            padding: 0 5px !important;
          }
        }

        /* Footer Right Column */
        .footer-right {
          display: flex;
          justify-content: flex-end;
        }

        .sponsor-card {
          background: inherit !important;
          border: 0px !important;
          border-radius: 12px;
          padding: 0px !important;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          max-width: 200px;
        }

        /* Sponsor Card Mobile Fixes */
        @media screen and (max-width: 968px) {
          .sponsor-card {
            max-width: 100% !important;
            margin: 0 auto !important;
          }
        }

        @media screen and (max-width: 768px) {
          .sponsor-card {
            padding: 0 15px !important;
          }
        }

        @media screen and (max-width: 480px) {
          .sponsor-card {
            padding: 0 10px !important;
          }
        }

        .sponsor-avatar {
          margin-bottom: 10px;
        }

        .sponsor-avatar img {
          width: 90px !important;
          height: 90px !important;
          border-radius: 50%;
          border: 2px solid #ffc107;
          object-fit: cover;
        }

        .sponsor-name {
          color: #ffc107;
          font-weight: 700;
          font-size: 1rem;
          margin: 0 0 5px 0;
        }

        /* Sponsor Name Mobile Fixes */
        @media screen and (max-width: 768px) {
          .sponsor-name {
            font-size: 0.95rem !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
          }
        }

        @media screen and (max-width: 480px) {
          .sponsor-name {
            font-size: 0.9rem !important;
          }
        }

        .sponsor-phone {
          margin: 0 0 8px 0;
        }

        .sponsor-phone a {
          color: rgba(255, 255, 255, 0.9);
          text-decoration: none;
          font-weight: 600;
          font-size: 0.9rem;
        }

        .sponsor-phone a:hover {
          color: #ffc107;
        }

        .sponsor-note {
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.75rem;
          font-style: italic;
          margin: 0;
          line-height: 1.3;
        }

        /* Sponsor Note Mobile Fixes */
        @media screen and (max-width: 768px) {
          .sponsor-note {
            font-size: 0.7rem !important;
            text-align: center !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
          }
        }

        @media screen and (max-width: 480px) {
          .sponsor-note {
            font-size: 0.65rem !important;
          }
        }

        .footer-heading {
          color: #ffc107;
          font-size: 2.2rem;
          font-weight: 700;
          margin: 0;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .footer-tagline {
          color: rgba(255, 255, 255, 0.9);
          font-size: 1.1rem;
          font-weight: 500;
          margin: 0;
          margin-bottom: 8px;
        }

        .footer-description {
          color: rgba(255, 255, 255, 0.7);
          line-height: inherit;
          font-size: 0.7rem;
          margin: 0;
        }

        .brand-text h3 {
          font-size: 1.4rem;
          font-weight: 700;
          color: #ffc107;
          margin: 0;
          line-height: 1.2;
        }

        .brand-text p {
          color: rgba(255, 255, 255, 0.7);
          margin: 4px 0 0 0;
          font-size: 0.9rem;
          font-weight: 500;
        }

        .brand-description {
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.6;
          font-size: 1rem;
          margin: 0;
        }

        .footer-nav-streamlined {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 60px;
          max-width: 1000px;
          width: 100%;
        }

        .nav-group {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .nav-group h4 {
          color: #ffc107;
          font-size: 1rem;
          font-weight: 600;
          margin: 0 0 8px 0;
        }

        .nav-group a {
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          font-size: 0.9rem;
          transition: color 0.3s ease;
        }

        .nav-group a:hover {
          color: #ffc107;
        }

        .sponsor-name {
          color: rgba(255, 255, 255, 0.9);
          font-weight: 600;
          margin: 0;
        }

        .sponsor-email {
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.85rem;
          margin: 0;
        }

        .sponsor-contact {
          color: rgba(255, 255, 255, 0.5);
          font-size: 0.8rem;
          margin: 4px 0 0 0;
          font-style: italic;
        }

        .link-column {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .link-column h4 {
          font-size: 1.1rem;
          font-weight: 700;
          color: #ffc107;
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          border-bottom: 2px solid rgba(255, 193, 7, 0.2);
        }

        .link-column a {
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          font-size: 0.95rem;
          transition: all 0.3s ease;
          padding: 4px 0;
        }

        .link-column a:hover {
          color: #ffc107;
          transform: translateX(4px);
        }

        .sponsor-info {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .sponsor-avatar {
          flex-shrink: 0;
        }

        .sponsor-avatar img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: 2px solid #ffc107;
          object-fit: cover;
        }

        .sponsor-details {
          flex: 1;
        }

        .sponsor-name {
          font-weight: 700;
          color: #ffc107;
          margin: 0 0 4px 0;
          font-size: 0.95rem;
        }

        .sponsor-contact {
          margin: 0 0 4px 0;
        }

        .sponsor-contact {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .whatsapp-icon {
          font-size: 1.2rem;
        }

        .whatsapp-number {
          color: rgba(255, 255, 255, 0.9);
          text-decoration: none;
          font-size: 1.1rem;
          font-weight: 600;
          transition: all 0.3s ease;
        }

        .whatsapp-number:hover {
          color: #25D366;
          text-decoration: none;
          transform: translateX(2px);
        }

        .sponsor-note {
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.8rem;
          margin: 0;
          font-style: italic;
        }

        .footer-bottom {
          padding-top: 30px;
          border-top: 0px !important;
          width: 500px;
        }

        .footer-legal {
          text-align: center;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .footer-legal p {
          color: rgba(255, 255, 255, 0.6);
          font-size: 1.05rem !important;
          margin: 0;
          position: relative;
          line-height: 1.4;
        }

        /* Back to Top Button */
        .back-to-top-btn {
          position: fixed;
          bottom: 30px;
          right: 30px;
          width: 50px;
          height: 50px;
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          border: none;
          border-radius: 50%;
          color: #000;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 20px rgba(255, 193, 7, 0.3);
          transition: all 0.3s ease;
          z-index: 1000;
        }

        .back-to-top-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 25px rgba(255, 193, 7, 0.4);
          background: linear-gradient(135deg, #ff8f00 0%, #ffc107 100%);
        }

        .back-to-top-btn:active {
          transform: translateY(0);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
          .section-content {
            grid-template-columns: 1fr !important;
            gap: 40px !important;
            text-align: center;
          }

          .section-content.reverse .section-visual {
            order: 0;
          }

          .profile-header {
            flex-direction: column;
            text-align: center;
            gap: 16px;
          }

          .profile-stats {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .sponsor-benefits {
            gap: 16px;
          }

          .benefit-item {
            flex-direction: column;
            text-align: center;
            gap: 12px;
          }

          .benefit-item:hover {
            transform: translateY(-4px);
          }

          .calculator-features {
            gap: 16px;
          }

          .feature-item {
            flex-direction: column;
            text-align: center;
            gap: 12px;
          }

          .feature-item:hover {
            transform: translateY(-4px);
          }

          /* Calculator Mobile Fixes */
          .calc-inputs {
            grid-template-columns: 1fr !important;
            gap: 20px !important;
          }

          .result-grid {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
          }

          .premium-calculator {
            padding: 24px 20px !important;
            margin: 0 10px !important;
          }

          .footer-nav-streamlined {
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
          }

          .footer-heading {
            font-size: 1.8rem;
          }

          .footer-logo-large {
            height: 80px;
          }

          .brand-logo {
            flex-direction: column;
            text-align: center;
            gap: 12px;
          }

          .sponsor-info {
            flex-direction: column;
            text-align: center;
            gap: 12px;
          }
        }

        @media (max-width: 480px) {
          .footer-nav-streamlined {
            grid-template-columns: 1fr;
            gap: 20px;
          }

          .footer-heading {
            font-size: 1.5rem;
          }

          .footer-logo-large {
            height: 60px;
          }

          .back-to-top-btn {
            width: 45px;
            height: 45px;
            bottom: 20px;
            right: 20px;
          }

          .profile-section {
            padding: 60px 0;
          }

          .calculator-section {
            padding: 60px 0;
          }

          .footer-section {
            padding: 60px 0 30px;
          }

          /* Additional Calculator Mobile Fixes for Small Screens */
          .premium-calculator {
            padding: 20px 16px !important;
            margin: 0 5px !important;
          }

          .calc-inputs {
            gap: 16px !important;
          }

          .result-grid {
            gap: 12px !important;
          }

          .result-card {
            padding: 20px 16px !important;
          }

          .result-value {
            font-size: 1.5rem !important;
          }

          .result-card.primary .result-value {
            font-size: 1.8rem !important;
          }
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          backdrop-filter: blur(10px);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2000;
          animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        .modal-content {
          background: linear-gradient(135deg, #1a1f2e 0%, #0f1419 100%);
          border: 1px solid rgba(255, 193, 7, 0.3);
          border-radius: 20px;
          padding: 40px;
          max-width: 500px;
          width: 90%;
          max-height: 90vh;
          overflow-y: auto;
          position: relative;
          animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
          from { transform: translateY(50px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }

        .contact-modal {
          max-width: 600px;
        }

        .contact-form {
          margin-top: 20px;
        }

        .contact-form p {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 20px;
          font-size: 0.9rem;
        }

        .contact-textarea {
          width: 100%;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 16px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 1rem;
          font-family: inherit;
          resize: vertical;
          min-height: 120px;
          margin-bottom: 20px;
        }

        .contact-textarea:focus {
          outline: none;
          border-color: #ffc107;
          background: rgba(255, 255, 255, 0.08);
        }

        .contact-textarea::placeholder {
          color: rgba(255, 255, 255, 0.4);
        }

        .contact-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
        }

        .cancel-btn {
          background: transparent;
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.7);
          padding: 12px 24px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .cancel-btn:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.05);
          border-color: rgba(255, 255, 255, 0.3);
        }

        .send-btn {
          background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
          border: none;
          color: #000;
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .send-btn:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        }

        .send-btn:disabled,
        .cancel-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 1px solid rgba(255, 193, 7, 0.2);
        }

        .modal-header h2 {
          font-size: 1.8rem;
          font-weight: 700;
          color: #ffc107;
          margin: 0;
        }

        .close-button {
          background: none;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          font-size: 2rem;
          cursor: pointer;
          padding: 0;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.3s ease;
        }

        .close-button:hover {
          background: rgba(255, 255, 255, 0.1);
          color: #ffc107;
        }

        /* Success Modal Styles */
        .success-modal {
          max-width: 400px;
          text-align: center;
        }

        .success-content {
          padding: 20px 0;
        }

        .success-icon {
          font-size: 4rem;
          color: #28a745;
          margin-bottom: 20px;
          animation: successPulse 0.6s ease;
        }

        @keyframes successPulse {
          0% { transform: scale(0); opacity: 0; }
          50% { transform: scale(1.1); opacity: 1; }
          100% { transform: scale(1); opacity: 1; }
        }

        .success-content h2 {
          font-size: 1.8rem;
          font-weight: 700;
          color: #ffc107;
          margin-bottom: 15px;
        }

        .success-content p {
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 25px;
          font-size: 1rem;
        }

        .loading-indicator {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          color: rgba(255, 255, 255, 0.7);
        }

        .spinner {
          width: 20px;
          height: 20px;
          border: 2px solid rgba(255, 193, 7, 0.3);
          border-top: 2px solid #ffc107;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        /* Loading & Error States */
        .loading-screen,
        .error-screen {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #0f1419;
        }

        .loading-content,
        .error-content {
          text-align: center;
          color: #ffffff;
        }

        .loading-spinner {
          width: 50px;
          height: 50px;
          border: 3px solid rgba(255, 193, 7, 0.3);
          border-top: 3px solid #ffc107;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
          .hero-container,
          .section-container {
            padding: 0 30px;
          }

          .hero-content,
          .section-content {
            gap: 40px;
          }

          .hero-title {
            font-size: 3rem;
          }
        }

        @media (max-width: 968px) {
          .nav-menu {
            display: none;
          }

          .nav-content {
            gap: 20px;
          }

          .nav-actions {
            gap: 12px;
          }

          .login-btn {
            padding: 6px 16px;
            font-size: 0.9rem;
          }
        }

        @media (max-width: 768px) {
          .hero-content,
          .section-content {
            grid-template-columns: 1fr;
            gap: 40px;
            text-align: center;
          }

          .section-content.reverse .section-visual {
            order: 0;
          }

          .hero-title,
          .section-title {
            font-size: 2.5rem;
          }

          .hero-metrics {
            justify-content: center;
          }

          .hero-actions {
            justify-content: center;
            flex-wrap: wrap;
          }

          .visual-container {
            width: 300px;
            height: 300px;
          }

          .hero-logo {
            width: 240px;
            height: 240px;
          }

          .stats-grid {
            grid-template-columns: 1fr 1fr;
            gap: 16px;
          }

          .operations-video {
            height: 300px;
          }

          .about-section,
          .operations-section,
          .calculator-section,
          .gallery-section,
          .meetings-section {
            padding: 80px 0;
          }

          .meetings-grid {
            grid-template-columns: 1fr;
            gap: 20px;
          }

          .contact-details {
            justify-content: center;
          }

          .profile-image-container {
            width: 300px;
            height: 300px;
          }

          .gallery-grid {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
          }

          /* Calculator Mobile Fixes for 968px */
          .calc-inputs {
            grid-template-columns: 1fr !important;
            gap: 20px !important;
          }

          .result-grid {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
          }

          .premium-calculator {
            padding: 28px 24px !important;
            margin: 0 15px !important;
          }
        }

        @media (max-width: 640px) {
          .floating-nav {
            top: 10px;
            left: 10px;
            right: 10px;
            transform: none;
            border-radius: 20px;
            padding: 12px 20px;
          }

          .nav-content {
            justify-content: space-between;
          }

          .brand-text {
            display: none;
          }

          .hero-section {
            padding: 100px 0 60px;
          }

          .hero-container {
            padding: 0 20px;
          }

          .profile-section {
            padding: 80px 0 !important;
          }

          .profile-hero {
            padding: 40px 20px;
          }

          .profile-content {
            grid-template-columns: 1fr !important;
            gap: 40px !important;
            text-align: center !important;
          }

          .profile-badge {
            justify-content: center;
            flex-direction: column;
            gap: 16px;
          }

          .profile-name {
            font-size: 2rem;
          }

          .profile-actions {
            flex-direction: column;
            gap: 16px;
            align-items: center;
          }

          .contact-options {
            flex-direction: column;
            gap: 12px;
            width: 100%;
          }

          .contact-btn {
            width: 100%;
            justify-content: center;
          }



          .metric-number {
            font-size: 1.5rem;
          }

          .video-container {
            max-width: 100%;
          }

          .video-wrapper {
            height: 200px;
          }

          .video-overlay {
            opacity: 1;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
          }

          .hero-title,
          .section-title {
            font-size: 2rem;
          }

          .hero-description,
          .section-description {
            font-size: 1.1rem;
          }

          .hero-metrics {
            flex-direction: column;
            gap: 20px;
            margin-bottom: 40px;
          }

          .metric-number {
            font-size: 2rem;
          }

          .hero-actions {
            flex-direction: column;
            gap: 16px;
          }

          .primary-cta,
          .secondary-cta,
          .section-cta {
            width: 100%;
            padding: 16px;
            text-align: center;
          }

          .section-container {
            padding: 0 20px;
          }

          .about-section,
          .operations-section,
          .calculator-section,
          .gallery-section,
          .meetings-section {
            padding: 60px 0;
          }

          .contact-details {
            flex-direction: column;
            align-items: center;
            gap: 12px;
          }

          .profile-image-container {
            width: 250px;
            height: 250px;
          }

          .meetings-grid {
            grid-template-columns: 1fr;
          }

          .meeting-card {
            padding: 20px;
          }

          .gallery-grid {
            grid-template-columns: 1fr;
            gap: 20px;
          }

          .gallery-item {
            margin: 0 auto;
            max-width: 400px;
          }

          .stats-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .stat-card {
            padding: 24px 20px;
          }

          .stat-number {
            font-size: 2rem;
          }

          .feature-list {
            gap: 16px;
          }

          .feature-item {
            padding: 16px;
          }

          .feature-icon {
            width: 50px;
            height: 50px;
            font-size: 1.5rem;
          }

          .operations-video {
            height: 250px;
          }

          .play-button {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
          }

          .calculator-wrapper {
            padding: 24px 20px;
          }

          /* Calculator Mobile Fixes for 640px */
          .calc-inputs {
            grid-template-columns: 1fr !important;
            gap: 18px !important;
          }

          .result-grid {
            grid-template-columns: 1fr !important;
            gap: 14px !important;
          }

          .premium-calculator {
            padding: 22px 18px !important;
            margin: 0 8px !important;
          }

          .calc-header {
            flex-direction: column;
            text-align: center;
            gap: 16px;
          }

          .calc-icon {
            margin: 0 auto;
          }

          .modal-content {
            padding: 30px 20px;
            margin: 20px;
          }
        }

        /* TABLET/IPAD OPTIMIZATIONS - 769px to 1024px */
        @media only screen and (min-width: 769px) and (max-width: 1024px) {

          /* Force iPad layouts to override mobile rules */
          /* Container and Section Optimizations */
          .section-container {
            padding: 0 50px !important;
            max-width: 100% !important;
          }

          .section-content {
            gap: 50px !important;
            padding: 0 !important;
          }

          /* Hero Section Tablet Optimization */
          .hero-section {
            padding: 80px 0 !important;
          }

          .hero-content {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 60px !important;
            align-items: center !important;
            padding: 0 !important;
            max-width: 1000px !important;
            margin: 0 auto !important;
            text-align: left !important;
          }

          .hero-text {
            text-align: left !important;
          }

          .hero-visual {
            text-align: center !important;
          }

          .hero-title {
            font-size: 3.5rem !important;
            line-height: 1.1 !important;
            margin-bottom: 25px !important;
          }

          .hero-description {
            font-size: 1.3rem !important;
            line-height: 1.6 !important;
            max-width: 700px !important;
            margin: 0 auto 40px auto !important;
          }

          .hero-metrics {
            display: grid !important;
            grid-template-columns: repeat(4, 1fr) !important;
            gap: 20px !important;
            max-width: 100% !important;
            margin: 40px 0 !important;
            grid-column: 1 / -1 !important;
            justify-content: stretch !important;
            flex-direction: unset !important;
            flex-wrap: unset !important;
          }

          /* Force override any conflicting flex rules with maximum specificity */
          .hero-section .hero-content .hero-metrics {
            display: grid !important;
            grid-template-columns: repeat(4, 1fr) !important;
            flex-direction: unset !important;
            flex-wrap: nowrap !important;
            justify-content: stretch !important;
          }

          /* Additional override for metric items */
          .hero-section .hero-content .hero-metrics .metric-item {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
          }

          .metric-item {
            padding: 25px 20px !important;
            background: rgba(255, 255, 255, 0.05) !important;
            border: 1px solid rgba(255, 193, 7, 0.3) !important;
            border-radius: 12px !important;
            text-align: center !important;
            transition: all 0.3s ease !important;
          }

          .metric-item:hover {
            background: rgba(255, 255, 255, 0.08) !important;
            border-color: rgba(255, 193, 7, 0.5) !important;
            transform: translateY(-3px) !important;
          }

          .metric-number,
          .metric-value {
            font-size: 2.2rem !important;
            font-weight: 800 !important;
            color: #ffc107 !important;
            display: block !important;
            margin-bottom: 8px !important;
          }

          .metric-label {
            font-size: 1rem !important;
            color: rgba(255, 255, 255, 0.8) !important;
            font-weight: 500 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
          }

          .hero-actions {
            flex-direction: row !important;
            gap: 25px !important;
            justify-content: flex-start !important;
            align-items: center !important;
            max-width: 100% !important;
            margin: 0 !important;
          }

          .primary-cta,
          .secondary-cta {
            flex: 1 !important;
            max-width: 280px !important;
            padding: 18px 36px !important;
            font-size: 1.2rem !important;
          }

          /* Calculator Section Complete Overhaul */
          .calculator-section {
            padding: 80px 0 !important;
          }

          .premium-calculator {
            max-width: 900px !important;
            margin: 0 auto !important;
            padding: 50px 40px !important;
            background: rgba(255, 255, 255, 0.03) !important;
            border: 1px solid rgba(255, 193, 7, 0.2) !important;
            border-radius: 20px !important;
          }

          .calc-header {
            margin-bottom: 40px !important;
            text-align: center !important;
          }

          .calc-title h3 {
            font-size: 2.2rem !important;
            margin-bottom: 10px !important;
          }

          .calc-title p {
            font-size: 1.1rem !important;
          }

          .calc-inputs {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 30px !important;
            margin-bottom: 50px !important;
          }

          .input-section {
            background: rgba(255, 255, 255, 0.02) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-radius: 12px !important;
            padding: 25px !important;
          }

          .input-section label {
            font-size: 1.1rem !important;
            font-weight: 600 !important;
            margin-bottom: 12px !important;
            display: block !important;
          }

          .premium-input,
          .premium-select {
            width: 100% !important;
            padding: 18px !important;
            font-size: 1.2rem !important;
            background: rgba(255, 255, 255, 0.08) !important;
            border: 2px solid rgba(255, 193, 7, 0.4) !important;
            border-radius: 10px !important;
            color: white !important;
            transition: all 0.3s ease !important;
            font-weight: 500 !important;
          }

          .premium-input:focus,
          .premium-select:focus {
            outline: none !important;
            border-color: #ffc107 !important;
            background: rgba(255, 255, 255, 0.12) !important;
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2) !important;
          }

          .premium-input:hover,
          .premium-select:hover {
            border-color: rgba(255, 193, 7, 0.6) !important;
            background: rgba(255, 255, 255, 0.1) !important;
          }

          .result-grid {
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 25px !important;
          }

          .result-item {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 193, 7, 0.05) 100%) !important;
            border: 2px solid rgba(255, 193, 7, 0.4) !important;
            border-radius: 16px !important;
            padding: 35px 25px !important;
            text-align: center !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: hidden !important;
          }

          .result-item:hover {
            transform: translateY(-5px) !important;
            border-color: #ffc107 !important;
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%) !important;
            box-shadow: 0 10px 30px rgba(255, 193, 7, 0.3) !important;
          }

          .result-item::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 3px !important;
            background: linear-gradient(90deg, #ffc107 0%, #ff8f00 100%) !important;
          }

          .result-value {
            font-size: 2.4rem !important;
            font-weight: 800 !important;
            color: #ffc107 !important;
            margin-bottom: 10px !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
          }

          .result-label {
            font-size: 1.1rem !important;
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500 !important;
            letter-spacing: 0.5px !important;
          }

          /* Calculator Tablet Optimization */
          .premium-calculator {
            max-width: 700px !important;
            margin: 0 auto !important;
            padding: 40px 30px !important;
          }

          .calc-inputs {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 25px !important;
          }

          .result-grid {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 20px !important;
          }

          .calc-input-group label {
            font-size: 1rem !important;
          }

          .calc-input-group input,
          .calc-input-group select {
            font-size: 1.1rem !important;
            padding: 14px !important;
          }

          .result-item {
            padding: 20px !important;
          }

          .result-value {
            font-size: 1.8rem !important;
          }

          .result-label {
            font-size: 1rem !important;
          }

          /* Profile Section Tablet Optimization */
          .profile-section {
            padding: 80px 0 !important;
          }

          .profile-content {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 60px !important;
            align-items: center !important;
            max-width: 1000px !important;
            margin: 0 auto !important;
            text-align: left !important;
          }

          .profile-text {
            text-align: left !important;
          }

          .profile-card-container {
            text-align: center !important;
          }

          .sponsor-profile {
            max-width: 650px !important;
            margin: 0 auto !important;
            padding: 60px 50px !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%) !important;
            border: 2px solid rgba(255, 193, 7, 0.3) !important;
            border-radius: 24px !important;
            position: relative !important;
            overflow: hidden !important;
            transition: all 0.3s ease !important;
          }

          .sponsor-profile:hover {
            transform: translateY(-5px) !important;
            border-color: rgba(255, 193, 7, 0.6) !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) !important;
            box-shadow: 0 20px 60px rgba(255, 193, 7, 0.2) !important;
          }

          .sponsor-profile::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 4px !important;
            background: linear-gradient(90deg, #ffc107 0%, #ff8f00 100%) !important;
          }

          .avatar-image {
            width: 150px !important;
            height: 150px !important;
            margin-bottom: 30px !important;
            border: 4px solid #ffc107 !important;
            transition: all 0.3s ease !important;
          }

          .sponsor-profile:hover .avatar-image {
            transform: scale(1.05) !important;
            box-shadow: 0 10px 30px rgba(255, 193, 7, 0.4) !important;
          }

          .sponsor-info h3 {
            font-size: 2.2rem !important;
            margin-bottom: 15px !important;
            color: #ffc107 !important;
            font-weight: 800 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
          }

          .sponsor-title {
            font-size: 1.3rem !important;
            margin-bottom: 10px !important;
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 600 !important;
          }

          .sponsor-username {
            font-size: 1.1rem !important;
            margin-bottom: 35px !important;
            color: rgba(255, 255, 255, 0.7) !important;
          }

          .contact-sponsor-btn {
            padding: 18px 36px !important;
            font-size: 1.2rem !important;
            font-weight: 700 !important;
            min-width: 280px !important;
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
            border: none !important;
            border-radius: 50px !important;
            color: #0f1419 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3) !important;
          }

          .contact-sponsor-btn:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 12px 40px rgba(255, 193, 7, 0.5) !important;
            color: #0f1419 !important;
          }

          /* Gallery Tablet Optimization */
          .gallery-section {
            padding: 80px 0 !important;
          }

          .gallery-grid {
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 25px !important;
            max-width: 900px !important;
            margin: 0 auto !important;
          }

          .gallery-item {
            aspect-ratio: 1 !important;
            border-radius: 16px !important;
            overflow: hidden !important;
            position: relative !important;
            cursor: pointer !important;
            transition: all 0.4s ease !important;
            border: 2px solid rgba(255, 193, 7, 0.2) !important;
          }

          .gallery-item:hover {
            transform: translateY(-8px) scale(1.02) !important;
            border-color: rgba(255, 193, 7, 0.6) !important;
            box-shadow: 0 15px 40px rgba(255, 193, 7, 0.3) !important;
          }

          .gallery-item img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            transition: all 0.4s ease !important;
          }

          .gallery-item:hover img {
            transform: scale(1.1) !important;
          }

          .gallery-item::after {
            content: '🔍' !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            font-size: 2rem !important;
            background: rgba(0, 0, 0, 0.7) !important;
            color: #ffc107 !important;
            width: 60px !important;
            height: 60px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            opacity: 0 !important;
            transition: all 0.3s ease !important;
          }

          .gallery-item:hover::after {
            opacity: 1 !important;
          }

          /* Section Titles and Content Optimization */
          .section-title {
            font-size: 3.2rem !important;
            line-height: 1.1 !important;
            margin-bottom: 25px !important;
            text-align: center !important;
          }

          .section-description {
            font-size: 1.25rem !important;
            line-height: 1.6 !important;
            max-width: 700px !important;
            margin: 0 auto 40px auto !important;
            text-align: center !important;
          }

          .section-cta {
            max-width: 380px !important;
            margin: 0 auto !important;
            padding: 20px 40px !important;
            font-size: 1.2rem !important;
            font-weight: 700 !important;
            display: block !important;
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
            border: none !important;
            border-radius: 50px !important;
            color: #0f1419 !important;
            text-decoration: none !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3) !important;
            position: relative !important;
            overflow: hidden !important;
          }

          .section-cta:hover {
            transform: translateY(-4px) !important;
            box-shadow: 0 15px 45px rgba(255, 193, 7, 0.5) !important;
            color: #0f1419 !important;
          }

          .section-cta::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: -100% !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent) !important;
            transition: left 0.6s ease !important;
          }

          .section-cta:hover::before {
            left: 100% !important;
          }

          /* Stats Grid Tablet Optimization */
          .stats-grid {
            grid-template-columns: repeat(4, 1fr) !important;
            gap: 25px !important;
            max-width: 1000px !important;
            margin: 0 auto !important;
          }

          .stat-item {
            padding: 25px !important;
            text-align: center !important;
          }

          .stat-value {
            font-size: 2rem !important;
          }

          .stat-label {
            font-size: 1.1rem !important;
          }

          /* Footer Tablet Optimization */
          .footer-section {
            padding: 60px 0 !important;
          }

          .footer-content {
            grid-template-columns: 1fr 1fr 1fr !important;
            gap: 50px !important;
            align-items: start !important;
            max-width: 1000px !important;
            margin: 0 auto !important;
            text-align: left !important;
          }

          .footer-company-name {
            font-size: 1.6rem !important;
          }

          .footer-tagline {
            font-size: 1rem !important;
          }

          .footer-legal p {
            font-size: 0.9rem !important;
            line-height: 1.5 !important;
          }

          .risk-warning {
            font-size: 0.8rem !important;
            line-height: 1.4 !important;
            text-align: center !important;
          }

          .sponsor-card {
            max-width: 250px !important;
            margin: 0 auto !important;
          }

          .sponsor-avatar img {
            width: 100px !important;
            height: 100px !important;
          }

          .sponsor-name {
            font-size: 1.1rem !important;
          }

          /* Navigation Tablet Optimization */
          .floating-nav {
            left: 40px !important;
            right: 40px !important;
            transform: none !important;
            padding: 18px 36px !important;
            backdrop-filter: blur(25px) !important;
            background: rgba(15, 20, 25, 0.9) !important;
            border: 1px solid rgba(255, 193, 7, 0.3) !important;
            border-radius: 50px !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
          }

          .nav-links {
            gap: 35px !important;
          }

          .nav-link {
            font-size: 1.05rem !important;
            padding: 10px 18px !important;
            border-radius: 25px !important;
            transition: all 0.3s ease !important;
            font-weight: 500 !important;
          }

          .nav-link:hover {
            background: rgba(255, 193, 7, 0.1) !important;
            color: #ffc107 !important;
            transform: translateY(-2px) !important;
          }

          .nav-cta {
            padding: 12px 24px !important;
            font-size: 1.05rem !important;
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
            border: none !important;
            border-radius: 25px !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
          }

          .nav-cta:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4) !important;
          }

          /* Meetings Section Tablet Optimization */
          .meetings-section {
            padding: 80px 0 !important;
          }

          .meetings-grid {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 30px !important;
            max-width: 800px !important;
            margin: 0 auto !important;
          }

          .meeting-card {
            padding: 35px !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%) !important;
            border: 2px solid rgba(255, 193, 7, 0.3) !important;
            border-radius: 20px !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: hidden !important;
          }

          .meeting-card:hover {
            transform: translateY(-5px) !important;
            border-color: rgba(255, 193, 7, 0.6) !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) !important;
            box-shadow: 0 15px 40px rgba(255, 193, 7, 0.2) !important;
          }

          .meeting-card::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 4px !important;
            background: linear-gradient(90deg, #ffc107 0%, #ff8f00 100%) !important;
          }

          .meeting-title {
            font-size: 1.5rem !important;
            margin-bottom: 15px !important;
            color: #ffc107 !important;
            font-weight: 700 !important;
          }

          .meeting-time {
            font-size: 1.1rem !important;
            color: rgba(255, 255, 255, 0.8) !important;
            font-weight: 500 !important;
          }

          .meeting-icon {
            font-size: 2.5rem !important;
            margin-bottom: 15px !important;
          }

          /* Operations Section Tablet Optimization */
          .operations-section {
            padding: 80px 0 !important;
          }

          .operations-content,
          .operations-section .section-content {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 60px !important;
            align-items: center !important;
            max-width: 1000px !important;
            margin: 0 auto !important;
            text-align: left !important;
          }

          .operations-video {
            height: 350px !important;
            width: 100% !important;
            border-radius: 16px !important;
          }

          .video-container {
            width: 100% !important;
          }

          .operation-metrics {
            width: 100% !important;
            margin: 0 !important;
          }

          .operations-text {
            text-align: left !important;
          }

          .metric-row {
            padding: 15px 0 !important;
          }

          .metric-label {
            font-size: 1rem !important;
          }

          .metric-value {
            font-size: 1.2rem !important;
          }

          /* Feature Lists Tablet Optimization */
          .feature-list {
            max-width: 800px !important;
            margin: 0 auto !important;
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 25px !important;
          }

          .feature-item {
            padding: 30px !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.01) 100%) !important;
            border: 2px solid rgba(255, 193, 7, 0.2) !important;
            border-radius: 16px !important;
            text-align: center !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: hidden !important;
          }

          .feature-item:hover {
            transform: translateY(-8px) !important;
            border-color: rgba(255, 193, 7, 0.5) !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%) !important;
            box-shadow: 0 15px 40px rgba(255, 193, 7, 0.2) !important;
          }

          .feature-item::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 3px !important;
            background: linear-gradient(90deg, #ffc107 0%, #ff8f00 100%) !important;
            transform: scaleX(0) !important;
            transition: transform 0.3s ease !important;
          }

          .feature-item:hover::before {
            transform: scaleX(1) !important;
          }

          .feature-icon {
            font-size: 3rem !important;
            margin-bottom: 20px !important;
            display: block !important;
            transition: all 0.3s ease !important;
          }

          .feature-item:hover .feature-icon {
            transform: scale(1.1) rotate(5deg) !important;
          }

          .feature-text h4 {
            font-size: 1.4rem !important;
            margin-bottom: 12px !important;
            color: #ffc107 !important;
            font-weight: 700 !important;
          }

          .feature-text p {
            font-size: 1.05rem !important;
            line-height: 1.6 !important;
            color: rgba(255, 255, 255, 0.8) !important;
          }

          /* About Section Tablet Optimization */
          .about-section {
            padding: 80px 0 !important;
          }

          .about-content {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 60px !important;
            align-items: center !important;
            max-width: 1000px !important;
            margin: 0 auto !important;
          }

          .about-text {
            text-align: left !important;
          }

          .about-visual {
            text-align: center !important;
          }

          .about-section .section-text {
            max-width: 100% !important;
            margin: 0 !important;
            text-align: left !important;
          }

          /* General Section Spacing */
          .section {
            padding: 80px 0 !important;
          }

          .section-container {
            max-width: 1000px !important;
            margin: 0 auto !important;
          }

          /* Section Content 2-Column Layout - FORCE OVERRIDE */
          .section-content {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 60px !important;
            align-items: center !important;
            text-align: left !important;
          }

          .section-content .section-text {
            text-align: left !important;
          }

          .section-content .section-visual {
            text-align: center !important;
          }

          .section-content.single-column {
            grid-template-columns: 1fr !important;
            text-align: center !important;
          }

          .section-content.three-column {
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 40px !important;
          }

          .section-content.four-column {
            grid-template-columns: repeat(4, 1fr) !important;
            gap: 30px !important;
          }

          /* Operations Video Tablet Optimization */
          .operations-video {
            height: 350px !important;
            max-width: 500px !important;
            margin: 0 auto !important;
          }

          .video-container {
            max-width: 500px !important;
            margin: 0 auto !important;
          }

          /* FINAL OVERRIDE - Highest Priority for Hero Metrics Grid */
          .hero-metrics,
          div.hero-metrics,
          .hero-section .hero-metrics,
          .hero-content .hero-metrics {
            display: grid !important;
            grid-template-columns: repeat(4, 1fr) !important;
            gap: 20px !important;
            flex-wrap: nowrap !important;
            justify-content: stretch !important;
            align-items: stretch !important;
            width: 100% !important;
            flex-direction: unset !important;
          }
        }

        /* IPAD PRO OPTIMIZATIONS - 820px width and above */
        @media only screen and (min-width: 820px) and (max-width: 1024px) {


          /* Enhanced spacing for larger iPad Pro */
          .section-container {
            padding: 0 60px !important;
            max-width: 1100px !important;
          }

          .section {
            padding: 100px 0 !important;
          }

          /* Hero Section iPad Pro */
          .hero-content {
            gap: 80px !important;
            max-width: 1100px !important;
          }

          .hero-title {
            font-size: 4rem !important;
          }

          .hero-description {
            font-size: 1.4rem !important;
            max-width: 800px !important;
          }

          /* Calculator iPad Pro - 3 Column Layout - MAXIMUM SPECIFICITY OVERRIDE */
          .premium-calculator {
            max-width: 1000px !important;
            padding: 60px 50px !important;
          }

          .aureus-experience .premium-calculator .calc-inputs,
          .premium-calculator .calc-inputs,
          .calc-inputs {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 40px !important;
            width: 100% !important;
            margin-bottom: 40px !important;
          }

          .aureus-experience .premium-calculator .result-grid,
          .premium-calculator .result-grid,
          .result-grid {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 30px !important;
            width: 100% !important;
            margin-bottom: 40px !important;
          }

          /* Gallery iPad Pro - 3 Column Layout - FORCE OVERRIDE */
          .gallery-grid {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 30px !important;
            max-width: 1100px !important;
            margin: 0 auto !important;
          }

          /* Profile Section iPad Pro */
          .sponsor-profile {
            max-width: 700px !important;
            padding: 70px 60px !important;
          }

          .avatar-image {
            width: 160px !important;
            height: 160px !important;
          }

          /* Enhanced Typography */
          .section-title {
            font-size: 3.5rem !important;
          }

          .section-description {
            font-size: 1.3rem !important;
            max-width: 800px !important;
          }

          /* Stats Grid iPad Pro - 4 Column */
          .stats-grid {
            grid-template-columns: repeat(4, 1fr) !important;
            gap: 35px !important;
            max-width: 1100px !important;
          }

          /* Meetings Grid iPad Pro - 3 Column */
          .meetings-grid {
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 35px !important;
            max-width: 1000px !important;
          }

          /* Feature Lists iPad Pro - 3 Column */
          .feature-list {
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 35px !important;
            max-width: 1100px !important;
          }

          /* Operations Section iPad Pro */
          .operations-video {
            height: 450px !important;
            max-width: 700px !important;
          }

          /* Navigation iPad Pro */
          .floating-nav {
            padding: 20px 40px !important;
            left: 60px !important;
            right: 60px !important;
          }

          .nav-links {
            gap: 40px !important;
          }

          .nav-link {
            font-size: 1.1rem !important;
            padding: 12px 20px !important;
          }

          .nav-cta {
            padding: 14px 28px !important;
            font-size: 1.1rem !important;
          }

          /* Hero Metrics iPad Pro - Enhanced Styling */
          .hero-metrics {
            gap: 30px !important;
            margin: 60px 0 !important;
            max-width: 1000px !important;
          }

          .metric-item {
            padding: 35px 25px !important;
            border-radius: 16px !important;
          }

          .metric-number {
            font-size: 2.8rem !important;
            margin-bottom: 12px !important;
          }

          .metric-label {
            font-size: 1.2rem !important;
          }

          /* Hero Actions iPad Pro */
          .hero-actions {
            gap: 25px !important;
            margin-top: 50px !important;
          }

          .primary-cta,
          .secondary-cta {
            padding: 18px 36px !important;
            font-size: 1.2rem !important;
            min-width: 200px !important;
          }

          /* Section CTAs iPad Pro */
          .section-cta {
            padding: 18px 36px !important;
            font-size: 1.2rem !important;
            min-width: 220px !important;
          }

          /* Footer iPad Pro */
          .footer-content {
            padding: 0 60px !important;
            gap: 50px !important;
          }

          .sponsor-card {
            padding: 0 40px !important;
          }
        }

        /* MOBILE OPTIMIZATIONS - 768px and below */
        @media only screen and (max-width: 768px) {
          /* Calculator Fixes */
          .aureus-experience .premium-calculator .calc-inputs,
          .calc-inputs {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 20px !important;
            width: 100% !important;
          }

          .aureus-experience .premium-calculator .result-grid,
          .result-grid {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 16px !important;
            width: 100% !important;
          }

          .aureus-experience .premium-calculator,
          .premium-calculator {
            padding: 24px 20px !important;
            margin: 0 10px !important;
            max-width: calc(100% - 20px) !important;
            width: calc(100% - 20px) !important;
          }

          /* Layout Fixes */
          .hero-content,
          .profile-content,
          .section-content {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 30px !important;
            text-align: center !important;
          }

          /* Operations Section Specific Fixes */
          .operations-section .section-title {
            font-size: 1.8rem !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            line-height: 1.2 !important;
          }

          .operations-section .section-text {
            padding: 0 20px !important;
            max-width: 100% !important;
          }

          .operations-section .operation-metrics {
            text-align: center !important;
            padding: 0 15px !important;
          }

          .operations-section .metric-row {
            flex-direction: column !important;
            gap: 4px !important;
            text-align: center !important;
          }

          /* Grid Fixes */
          .stats-grid,
          .meetings-grid,
          .gallery-grid {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
          }

          /* Button Fixes */
          .primary-cta,
          .secondary-cta,
          .contact-btn {
            width: 100% !important;
            max-width: 300px !important;
            justify-content: center !important;
          }

          /* Navigation Fixes */
          .floating-nav {
            left: 10px !important;
            right: 10px !important;
            transform: none !important;
          }

          /* Footer Fixes */
          .footer-content {
            grid-template-columns: 1fr !important;
            gap: 25px !important;
            text-align: center !important;
          }

          .footer-left,
          .footer-center,
          .footer-right {
            align-items: center !important;
            text-align: center !important;
          }

          .risk-warning {
            text-align: center !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            padding: 0 15px !important;
          }

          .sponsor-card {
            max-width: 100% !important;
            margin: 0 auto !important;
          }
        }

        @media only screen and (max-width: 480px) {
          /* Calculator Fixes */
          .aureus-experience .premium-calculator .calc-inputs,
          .calc-inputs {
            gap: 16px !important;
          }

          .aureus-experience .premium-calculator .result-grid,
          .result-grid {
            gap: 12px !important;
          }

          .aureus-experience .premium-calculator,
          .premium-calculator {
            padding: 20px 16px !important;
            margin: 0 5px !important;
            max-width: calc(100% - 10px) !important;
            width: calc(100% - 10px) !important;
          }

          /* Layout Fixes */
          .hero-content,
          .profile-content,
          .section-content {
            gap: 25px !important;
            padding: 0 10px !important;
          }

          /* Grid Fixes */
          .stats-grid,
          .meetings-grid,
          .gallery-grid {
            gap: 12px !important;
          }

          /* Button Fixes */
          .primary-cta,
          .secondary-cta,
          .contact-btn {
            max-width: 280px !important;
            padding: 12px 20px !important;
            font-size: 0.95rem !important;
          }

          /* Navigation Fixes */
          .floating-nav {
            left: 8px !important;
            right: 8px !important;
            padding: 8px 12px !important;
          }
        }
      `}</style>


    </div>
  );
};

export default FlowingLandingPage;
