import React, { useState, useEffect, useCallback } from 'react';
import CryptoPaymentStep from './CryptoPaymentStep';
import BankTransferStep from './BankTransferStep';
import { supabase } from '../lib/supabase';
import { LegalDocumentService } from '../lib/legalDocumentService';
import styles from '../styles/SharePurchaseFlow.module.css';

interface CurrentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
}

interface PaymentNetwork {
  id: string;
  name: string;
  technical: string;
  icon: string;
  wallet_address: string;
}

interface SavedPaymentMethod {
  id: string;
  type: 'crypto' | 'bank_transfer';
  name: string;
  network?: PaymentNetwork;
  wallet_address?: string;
  bank_details?: any;
  is_default: boolean;
}

type PurchaseStep = 'amount' | 'payment_method' | 'crypto_network' | 'crypto_payment' | 'bank_transfer' | 'confirmation' | 'pending';

interface SharePurchaseFlowProps {
  user?: any;
  onClose?: () => void;
}

const SharePurchaseFlow: React.FC<SharePurchaseFlowProps> = ({ user, onClose }) => {
  const [step, setStep] = useState<PurchaseStep>('amount');
  const [amount, setAmount] = useState<string>('');
  const [currentPhase, setCurrentPhase] = useState<CurrentPhase | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'crypto' | 'bank_transfer' | null>(null);
  const [selectedNetwork, setSelectedNetwork] = useState<PaymentNetwork | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [selectedCurrency, setSelectedCurrency] = useState<'USD' | 'ZAR'>('USD');

  // Legal document agreement checking
  const [legalCheckComplete, setLegalCheckComplete] = useState(false);
  const [canPurchaseShares, setCanPurchaseShares] = useState(false);
  const [missingAgreements, setMissingAgreements] = useState<string[]>([]);
  const [legalCheckMessage, setLegalCheckMessage] = useState<string>('');

  // Enhanced state for new features
  const [savedPaymentMethods, setSavedPaymentMethods] = useState<SavedPaymentMethod[]>([]);
  const [selectedSavedMethod, setSelectedSavedMethod] = useState<SavedPaymentMethod | null>(null);
  const [realTimePrice, setRealTimePrice] = useState<number>(0);
  const [priceUpdateInterval, setPriceUpdateInterval] = useState<NodeJS.Timeout | null>(null);
  const [quickAmounts] = useState<number[]>([5, 50, 100, 250, 500, 1000, 2500, 5000]);
  const [isOneClickMode, setIsOneClickMode] = useState(false);

  // Payment networks - will be loaded from database
  const [paymentNetworks, setPaymentNetworks] = useState<PaymentNetwork[]>([]);

  // Fetch current phase on component mount and initialize enhanced features
  useEffect(() => {
    fetchCurrentPhase();
    loadSavedPaymentMethods();
    loadPaymentNetworks(); // Load wallet addresses from database
    startRealTimePriceUpdates();

    // Add body class for mobile scrolling fix
    document.body.classList.add('purchase-modal-open');

    return () => {
      if (priceUpdateInterval) {
        clearInterval(priceUpdateInterval);
      }
      // Remove body class when component unmounts
      document.body.classList.remove('purchase-modal-open');
    };
  }, []);

  // Check legal document agreements before allowing purchase
  useEffect(() => {
    const checkLegalAgreements = async () => {
      if (!user?.id) {
        setLegalCheckComplete(true);
        setCanPurchaseShares(false);
        setLegalCheckMessage('User not authenticated');
        return;
      }

      try {
        const result = await LegalDocumentService.canUserPurchaseShares(user.id);
        setCanPurchaseShares(result.canPurchase);
        setMissingAgreements(result.missingAgreements);
        setLegalCheckMessage(result.message);
        setLegalCheckComplete(true);
      } catch (error) {
        console.error('Error checking legal agreements:', error);
        setCanPurchaseShares(false);
        setMissingAgreements(['Terms & Conditions', 'Privacy Policy', 'Legal Disclaimer']);
        setLegalCheckMessage('Unable to verify legal document agreements. Please ensure all documents are agreed to.');
        setLegalCheckComplete(true);
      }
    };

    checkLegalAgreements();
  }, [user?.id]);

  // Real-time price updates
  const startRealTimePriceUpdates = useCallback(() => {
    const interval = setInterval(async () => {
      if (currentPhase) {
        // Simulate real-time price updates (in production, this would fetch from API)
        const priceVariation = (Math.random() - 0.5) * 0.02; // ±1% variation
        const newPrice = currentPhase.price_per_share * (1 + priceVariation);
        setRealTimePrice(newPrice);
      }
    }, 5000); // Update every 5 seconds

    setPriceUpdateInterval(interval);
  }, [currentPhase]);

  // Load saved payment methods
  const loadSavedPaymentMethods = async () => {
    if (!user?.database_user?.id) return;

    try {
      const { data, error } = await supabase
        .from('user_payment_methods')
        .select('*')
        .eq('user_id', user.database_user.id)
        .eq('is_active', true);

      if (!error && data) {
        setSavedPaymentMethods(data);
      }
    } catch (error) {
      console.error('Error loading saved payment methods:', error);
    }
  };

  const loadPaymentNetworks = async () => {
    try {
      console.log('🔄 Loading payment networks from company_wallets table...');

      const { data, error } = await supabase
        .from('company_wallets')
        .select('*')
        .eq('is_active', true)
        .order('network');

      if (error) {
        console.error('❌ Error loading payment networks:', error);
        console.error('❌ This might be due to RLS policy - users need read access to company_wallets');

        // For now, provide a fallback message
        console.warn('⚠️ Using fallback: No payment networks available due to access restrictions');
        setPaymentNetworks([]);
        return;
      }

      if (!data || data.length === 0) {
        console.warn('⚠️ No active payment networks found in company_wallets table');
        console.warn('⚠️ Please add wallet addresses via Admin Panel > Wallet Manager');

        // Set empty array to prevent showing hardcoded addresses
        setPaymentNetworks([]);
        return;
      }

      // Map database records to PaymentNetwork format
      const networks: PaymentNetwork[] = data.map(wallet => {
        // Map network names to display format
        const networkConfig = getNetworkConfig(wallet.network);

        return {
          id: wallet.network,
          name: networkConfig.name,
          technical: networkConfig.technical,
          icon: networkConfig.icon,
          wallet_address: wallet.wallet_address
        };
      });

      console.log('✅ Loaded payment networks:', networks);
      setPaymentNetworks(networks);
    } catch (error) {
      console.error('❌ Error loading payment networks:', error);
    }
  };

  // Helper function to get network configuration
  const getNetworkConfig = (network: string) => {
    const configs: Record<string, { name: string; technical: string; icon: string }> = {
      'BSC': {
        name: 'Binance Smart Chain',
        technical: 'BEP-20',
        icon: '🟡'
      },
      'ETH': {
        name: 'Ethereum Network',
        technical: 'ERC-20',
        icon: '🔵'
      },
      'POL': {
        name: 'Polygon Network',
        technical: 'Polygon',
        icon: '🟣'
      },
      'POLYGON': {
        name: 'Polygon Network',
        technical: 'Polygon',
        icon: '🟣'
      },
      'TRON': {
        name: 'TRON Network',
        technical: 'TRC-20',
        icon: '🔴'
      }
    };

    return configs[network] || {
      name: network,
      technical: network,
      icon: '💰'
    };
  };

  const fetchCurrentPhase = async () => {
    try {
      setLoading(true);

      // Check if a specific phase was selected from the Investment Phases page
      const selectedPhaseNumber = localStorage.getItem('aureus_selected_phase');

      if (selectedPhaseNumber !== null) {
        // Fetch the specific selected phase
        const phaseNum = parseInt(selectedPhaseNumber);
        const { data: selectedPhase, error: selectedError } = await supabase
          .from('investment_phases')
          .select('*')
          .eq('phase_number', phaseNum)
          .single();

        if (!selectedError && selectedPhase) {
          setCurrentPhase(selectedPhase);
          // Clear the selection after using it
          localStorage.removeItem('aureus_selected_phase');
          setLoading(false);
          return;
        }
      }

      // Fallback: Fetch current active phase from Supabase
      const { data: phase, error } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error fetching current phase:', error);
        // Fallback to Pre Sale phase if no active phase found
        const { data: preSalePhase, error: preSaleError } = await supabase
          .from('investment_phases')
          .select('*')
          .eq('phase_number', 0)
          .single();

        if (preSaleError) {
          throw new Error('Failed to fetch phase information');
        }

        setCurrentPhase(preSalePhase);
      } else {
        setCurrentPhase(phase);
      }
    } catch (err) {
      setError('Failed to load current phase information');
      console.error('Error fetching current phase:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateShares = (amountValue: string): number => {
    if (!currentPhase || !amountValue) return 0;
    const numAmount = parseFloat(amountValue);
    return Math.floor(numAmount / currentPhase.price_per_share);
  };

  const validateAmount = (amountValue: string): string | null => {
    const numAmount = parseFloat(amountValue);
    if (isNaN(numAmount)) return 'Please enter a valid amount';
    if (!currentPhase) return 'Phase information not available';

    const minAmount = currentPhase.price_per_share; // Minimum is 1 share
    if (numAmount < minAmount) return `Minimum purchase: $${minAmount.toFixed(2)} for 1 share`;
    if (numAmount > 50000) return 'Maximum purchase amount is $50,000';

    const shares = calculateShares(amountValue);
    if (shares < 1) return `Minimum purchase: $${currentPhase.price_per_share.toFixed(2)} for 1 share`;

    // Check if enough shares are available
    const remainingShares = currentPhase.total_shares_available - currentPhase.shares_sold;
    if (shares > remainingShares) {
      return `Only ${remainingShares.toLocaleString()} shares remaining in this phase`;
    }

    return null;
  };

  const handleAmountSubmit = () => {
    const validationError = validateAmount(amount);
    if (validationError) {
      setError(validationError);
      return;
    }
    setError('');

    // Check if user is from South Africa, Namibia, or Eswatini
    const userCountry = user?.database_user?.country_of_residence || user?.country_of_residence || user?.country_code;
    const isSouthernAfricaUser = ['ZAF', 'NAM', 'SWZ', 'ZA'].includes(userCountry);

    if (isSouthernAfricaUser) {
      // For Southern Africa users, automatically select payment method based on currency
      if (selectedCurrency === 'ZAR') {
        setPaymentMethod('bank_transfer');
        setStep('bank_transfer');
      } else {
        setPaymentMethod('crypto');
        setStep('crypto_network');
      }
    } else {
      // For other users, show payment method selection
      setStep('payment_method');
    }
  };

  const handlePaymentMethodSelect = (method: 'crypto' | 'bank_transfer') => {
    setPaymentMethod(method);
    if (method === 'crypto') {
      setStep('crypto_network');
    } else {
      setStep('bank_transfer');
    }
  };

  const handleNetworkSelect = (network: PaymentNetwork) => {
    setSelectedNetwork(network);
    setStep('crypto_payment');
  };

  // One-click purchase handler
  const handleOneClickPurchase = async (savedMethod: SavedPaymentMethod) => {
    const validationError = validateAmount(amount);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError('');
    setSelectedSavedMethod(savedMethod);
    setIsOneClickMode(true);

    if (savedMethod.type === 'crypto' && savedMethod.network) {
      setPaymentMethod('crypto');
      setSelectedNetwork(savedMethod.network);
      setStep('crypto_payment');
    } else if (savedMethod.type === 'bank_transfer') {
      setPaymentMethod('bank_transfer');
      setStep('bank_transfer');
    }
  };

  const handleBack = () => {
    switch (step) {
      case 'payment_method':
        setStep('amount');
        break;
      case 'crypto_network':
        setStep('payment_method');
        break;
      case 'crypto_payment':
        setStep('crypto_network');
        break;
      case 'bank_transfer':
        setStep('payment_method');
        break;
      default:
        setStep('amount');
    }
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1f2937 0%, #111827 50%, #1f2937 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '3px solid #374151',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px auto'
          }}></div>
          <p style={{ color: '#9ca3af' }}>Loading share purchase...</p>
        </div>
      </div>
    );
  }

  // Show legal document check screen if not completed or not allowed to purchase
  if (!legalCheckComplete || !canPurchaseShares) {
    return (
      <div className={`${styles.purchaseFlowContainer} premium-purchase-flow`} style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
        padding: '20px',
        overflowY: 'auto'
      }}>
        <div className={`${styles.purchaseFlowModal} premium-purchase-modal`} style={{
          padding: '40px',
          backgroundColor: 'var(--surface)',
          color: 'var(--text)',
          position: 'relative',
          border: '1px solid var(--border)',
          boxShadow: 'var(--shadow), var(--glow)',
          minHeight: 'auto',
          height: 'auto',
          maxWidth: '600px',
          width: '100%',
          maxHeight: '90vh',
          overflowY: 'auto',
          borderRadius: '16px'
        }}>
          {/* Close Button */}
          <button
            onClick={onClose}
            style={{
              position: 'absolute',
              top: '20px',
              right: '20px',
              background: 'none',
              border: 'none',
              color: '#9ca3af',
              fontSize: '24px',
              cursor: 'pointer',
              padding: '5px',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            ×
          </button>

          {!legalCheckComplete ? (
            // Loading legal document check
            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
              <div style={{
                width: '50px',
                height: '50px',
                border: '3px solid #374151',
                borderTop: '3px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto 20px auto'
              }}></div>
              <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '16px' }}>
                📋 Checking Legal Document Status
              </h2>
              <p style={{ color: '#9ca3af' }}>
                Verifying your agreement to legal documents...
              </p>
            </div>
          ) : (
            // Legal documents not agreed to
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <div style={{
                width: '80px',
                height: '80px',
                backgroundColor: 'rgba(239, 68, 68, 0.2)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 24px auto'
              }}>
                <svg width="40" height="40" fill="none" stroke="#ef4444" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>

              <h2 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '16px', color: '#ef4444' }}>
                ⚠️ Legal Documents Required
              </h2>

              <p style={{ color: '#9ca3af', marginBottom: '24px', fontSize: '16px', lineHeight: '1.6' }}>
                You must read and agree to all legal documents before purchasing shares. This ensures you understand the terms and risks involved.
              </p>

              <div style={{
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                border: '1px solid rgba(239, 68, 68, 0.3)',
                borderRadius: '12px',
                padding: '20px',
                marginBottom: '24px',
                textAlign: 'left'
              }}>
                <h3 style={{ color: '#ef4444', fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>
                  📋 Missing Agreements:
                </h3>
                <ul style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6', margin: 0, paddingLeft: '20px' }}>
                  {missingAgreements.map((doc, index) => (
                    <li key={index} style={{ marginBottom: '4px' }}>
                      <strong>{doc}</strong>
                    </li>
                  ))}
                </ul>
              </div>

              <div style={{
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                border: '1px solid rgba(59, 130, 246, 0.3)',
                borderRadius: '12px',
                padding: '20px',
                marginBottom: '24px',
                textAlign: 'left'
              }}>
                <h3 style={{ color: '#3b82f6', fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>
                  📖 What You Need to Do:
                </h3>
                <ol style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6', margin: 0, paddingLeft: '20px' }}>
                  <li style={{ marginBottom: '8px' }}>Go to your <strong>Dashboard → Legal Documents</strong></li>
                  <li style={{ marginBottom: '8px' }}>Read each document carefully</li>
                  <li style={{ marginBottom: '8px' }}>Click "View" and then "Agree" for each document</li>
                  <li style={{ marginBottom: '8px' }}>Return here to complete your share purchase</li>
                </ol>
              </div>

              <div style={{
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                border: '1px solid rgba(245, 158, 11, 0.3)',
                borderRadius: '12px',
                padding: '16px',
                marginBottom: '24px'
              }}>
                <p style={{ color: '#f59e0b', fontSize: '14px', fontWeight: '600', margin: 0 }}>
                  💡 These documents contain important information about mining risks, dividend disclaimers,
                  and your rights as a shareholder. Please read them carefully.
                </p>
              </div>

              <button
                onClick={onClose}
                style={{
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#3b82f6'}
              >
                Go to Legal Documents
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`${styles.purchaseFlowContainer} premium-purchase-flow`} style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px',
      overflowY: 'auto'
    }}>
      <div className={`${styles.purchaseFlowModal} premium-purchase-modal`} style={{
        padding: '40px',
        backgroundColor: 'var(--surface)',
        color: 'var(--text)',
        position: 'relative',
        border: '1px solid var(--border)',
        boxShadow: 'var(--shadow), var(--glow)',
        minHeight: 'auto',
        height: 'auto',
        maxWidth: '600px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto',
        borderRadius: '16px'
      }}>
        {/* Scrollable Content Container */}
        <div style={{
          maxHeight: '100%',
          overflowY: 'auto',
          paddingRight: '8px',
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.1)'
        }}>
        {/* Close Button */}
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '16px',
            right: '16px',
            background: 'rgba(255, 255, 255, 0.1)',
            border: 'none',
            borderRadius: '8px',
            width: '32px',
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            color: '#ffffff',
            fontSize: '18px',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
          }}
        >
          ×
        </button>

        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <h1 style={{ fontSize: '28px', fontWeight: 'bold', margin: '0 0 10px 0' }}>
            💰 Purchase Gold Shares
          </h1>
          {currentPhase && (
            <div style={{
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              border: '1px solid rgba(59, 130, 246, 0.3)',
              borderRadius: '12px',
              padding: '16px',
              marginTop: '20px'
            }}>
              <p style={{ margin: '0 0 8px 0', color: '#60a5fa', fontWeight: 'bold', fontSize: '16px' }}>
                Current Phase: {currentPhase.phase_name}
              </p>
              <p style={{ margin: '0 0 8px 0', color: '#ffffff', fontSize: '18px', fontWeight: 'bold' }}>
                Share Price: ${currentPhase.price_per_share.toFixed(2)} USD
              </p>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '12px' }}>
                <div style={{ textAlign: 'left' }}>
                  <p style={{ margin: '0 0 4px 0', color: '#9ca3af', fontSize: '14px' }}>Total Available</p>
                  <p style={{ margin: 0, color: '#10b981', fontWeight: 'bold' }}>
                    {currentPhase.total_shares_available.toLocaleString()} shares
                  </p>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <p style={{ margin: '0 0 4px 0', color: '#9ca3af', fontSize: '14px' }}>Remaining</p>
                  <p style={{ margin: 0, color: '#f59e0b', fontWeight: 'bold' }}>
                    {(currentPhase.total_shares_available - currentPhase.shares_sold).toLocaleString()} shares
                  </p>
                </div>
              </div>
              <div style={{ marginTop: '8px', textAlign: 'center' }}>
                <p style={{ margin: 0, color: '#9ca3af', fontSize: '12px' }}>
                  {currentPhase.shares_sold.toLocaleString()} shares sold ({((currentPhase.shares_sold / currentPhase.total_shares_available) * 100).toFixed(1)}% complete)
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div style={{
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            border: '1px solid rgba(239, 68, 68, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            marginBottom: '20px',
            color: '#f87171'
          }}>
            ❌ {error}
          </div>
        )}

        {/* Step Content */}
        {step === 'amount' && (
          <EnhancedAmountStep
            amount={amount}
            setAmount={setAmount}
            currentPhase={currentPhase}
            calculateShares={calculateShares}
            onSubmit={handleAmountSubmit}
            quickAmounts={quickAmounts}
            realTimePrice={realTimePrice || currentPhase?.price_per_share || 0}
            savedPaymentMethods={savedPaymentMethods}
            onOneClickPurchase={handleOneClickPurchase}
            user={user}
            selectedCurrency={selectedCurrency}
            setSelectedCurrency={setSelectedCurrency}
          />
        )}

        {step === 'payment_method' && (
          <PaymentMethodStep
            amount={amount}
            shares={calculateShares(amount)}
            currentPhase={currentPhase}
            onSelect={handlePaymentMethodSelect}
            onBack={handleBack}
          />
        )}

        {step === 'crypto_network' && (
          <CryptoNetworkStep
            networks={paymentNetworks}
            onSelect={handleNetworkSelect}
            onBack={handleBack}
          />
        )}

        {step === 'crypto_payment' && selectedNetwork && (
          <CryptoPaymentStep
            amount={amount}
            shares={calculateShares(amount)}
            network={selectedNetwork}
            currentPhase={currentPhase}
            user={user}
            onBack={handleBack}
            onComplete={() => setStep('pending')}
          />
        )}

        {step === 'bank_transfer' && (
          <BankTransferStep
            amount={amount}
            shares={calculateShares(amount)}
            currentPhase={currentPhase}
            user={user}
            onBack={handleBack}
            onComplete={() => setStep('pending')}
          />
        )}

        {step === 'pending' && (
          <PendingStep onClose={onClose} />
        )}
        </div> {/* End Scrollable Content Container */}
      </div>
    </div>
  );
};

// Amount Entry Step Component
const AmountStep: React.FC<{
  amount: string;
  setAmount: (value: string) => void;
  currentPhase: CurrentPhase | null;
  calculateShares: (amount: string) => number;
  onSubmit: () => void;
}> = ({ amount, setAmount, currentPhase, calculateShares, onSubmit }) => {
  const shares = calculateShares(amount);
  const totalCost = shares * (currentPhase?.price_per_share || 0);

  return (
    <div>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px', textAlign: 'center' }}>
        💰 Enter Share Purchase Amount
      </h2>

      <p style={{ color: '#9ca3af', marginBottom: '20px', textAlign: 'center' }}>
        Enter your desired share purchase amount between ${currentPhase?.price_per_share.toFixed(2) || '5.00'} and $50,000:
      </p>

      {/* Payment Details Box */}
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#fbbf24', fontWeight: 'bold', margin: '0 0 12px 0' }}>
          📋 PAYMENT DETAILS:
        </h3>
        <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
          <p style={{ margin: '0 0 4px 0' }}>• Minimum: ${currentPhase?.price_per_share.toFixed(2) || '5.00'} USD (1 share)</p>
          <p style={{ margin: '0 0 4px 0' }}>• Maximum: $50,000 USD</p>
          <p style={{ margin: '0 0 4px 0' }}>• Currency: USD (United States Dollar)</p>
          {currentPhase && (
            <p style={{ margin: '0 0 4px 0' }}>• Current Price: ${currentPhase.price_per_share.toFixed(2)} per share</p>
          )}
          <p style={{ margin: 0 }}>• No additional fees</p>
        </div>
      </div>

      {/* Amount Input */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
          Purchase Amount (USD)
        </label>
        <div style={{ position: 'relative' }}>
          <span style={{
            position: 'absolute',
            left: '12px',
            top: '50%',
            transform: 'translateY(-50%)',
            color: '#9ca3af',
            fontSize: '18px'
          }}>$</span>
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder={currentPhase?.price_per_share.toFixed(2) || "5.00"}
            min={currentPhase?.price_per_share.toString() || "5"}
            max="50000"
            step="0.01"
            style={{
              width: '100%',
              padding: '16px 16px 16px 32px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              border: '2px solid #374151',
              borderRadius: '12px',
              color: 'white',
              fontSize: '18px',
              fontWeight: 'bold'
            }}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                onSubmit();
              }
            }}
          />
        </div>
      </div>

      {/* Calculation Display */}
      {amount && currentPhase && (
        <div style={{
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          border: '1px solid rgba(34, 197, 94, 0.3)',
          borderRadius: '12px',
          padding: '16px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#10b981', fontWeight: 'bold', margin: '0 0 12px 0' }}>
            💡 CALCULATION:
          </h3>
          <div style={{ color: '#d1d5db', fontSize: '14px' }}>
            <p style={{ margin: '0 0 8px 0' }}>
              ${parseFloat(amount || '0').toFixed(2)} USD ÷ ${currentPhase.price_per_share.toFixed(2)} = {shares} shares
            </p>
            <p style={{ margin: '0 0 8px 0' }}>
              Total Cost: ${totalCost.toFixed(2)} USD
            </p>
            {parseFloat(amount || '0') !== totalCost && (
              <p style={{ margin: 0, color: '#fbbf24' }}>
                Note: You'll pay ${totalCost.toFixed(2)} for {shares} complete shares
              </p>
            )}
          </div>
        </div>
      )}

      {/* Continue Button */}
      <button
        onClick={onSubmit}
        disabled={!amount || parseFloat(amount) < 5}
        style={{
          width: '100%',
          padding: '16px',
          backgroundColor: amount && parseFloat(amount) >= 5 ? '#3b82f6' : '#374151',
          border: 'none',
          borderRadius: '12px',
          color: 'white',
          fontSize: '18px',
          fontWeight: 'bold',
          cursor: amount && parseFloat(amount) >= 5 ? 'pointer' : 'not-allowed',
          transition: 'all 0.2s ease'
        }}
      >
        Continue to Payment Method →
      </button>
    </div>
  );
};

// Payment Method Selection Step
const PaymentMethodStep: React.FC<{
  amount: string;
  shares: number;
  currentPhase: CurrentPhase | null;
  onSelect: (method: 'crypto' | 'bank_transfer') => void;
  onBack: () => void;
}> = ({ amount, shares, currentPhase, onSelect, onBack }) => {
  const totalCost = shares * (currentPhase?.price_per_share || 0);

  return (
    <div>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px', textAlign: 'center' }}>
        💳 Select Payment Method
      </h2>

      {/* Purchase Summary */}
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '30px'
      }}>
        <h3 style={{ color: '#fbbf24', fontWeight: 'bold', margin: '0 0 12px 0' }}>
          📋 PURCHASE SUMMARY:
        </h3>
        <div style={{ color: '#d1d5db', fontSize: '14px' }}>
          <p style={{ margin: '0 0 8px 0' }}>Amount: ${totalCost.toFixed(2)} USD</p>
          <p style={{ margin: '0 0 8px 0' }}>Shares: {shares}</p>
          <p style={{ margin: '0 0 8px 0' }}>Price per Share: ${currentPhase?.price_per_share.toFixed(2)}</p>
          <p style={{ margin: 0 }}>Phase: {currentPhase?.phase_name}</p>
        </div>
      </div>

      {/* Payment Options */}
      <div style={{ marginBottom: '30px' }}>
        {/* USDT Crypto Payment */}
        <button
          onClick={() => onSelect('crypto')}
          style={{
            width: '100%',
            padding: '20px',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            border: '2px solid rgba(59, 130, 246, 0.3)',
            borderRadius: '12px',
            color: 'white',
            marginBottom: '16px',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ textAlign: 'left' }}>
              <h3 style={{ margin: '0 0 8px 0', fontSize: '18px', fontWeight: 'bold' }}>
                💎 USDT Payment (Recommended)
              </h3>
              <p style={{ margin: 0, color: '#9ca3af', fontSize: '14px' }}>
                Fast processing • Multiple networks • Instant confirmation
              </p>
            </div>
            <div style={{ fontSize: '24px' }}>→</div>
          </div>
        </button>

        {/* Bank Transfer */}
        <button
          onClick={() => onSelect('bank_transfer')}
          style={{
            width: '100%',
            padding: '20px',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            border: '2px solid rgba(34, 197, 94, 0.3)',
            borderRadius: '12px',
            color: 'white',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ textAlign: 'left' }}>
              <h3 style={{ margin: '0 0 8px 0', fontSize: '18px', fontWeight: 'bold' }}>
                🏦 Bank Transfer (ZAR)
              </h3>
              <p style={{ margin: 0, color: '#9ca3af', fontSize: '14px' }}>
                South Africa, Eswatini, Namibia • 1-3 business days
              </p>
            </div>
            <div style={{ fontSize: '24px' }}>→</div>
          </div>
        </button>
      </div>

      {/* Back Button */}
      <button
        onClick={onBack}
        style={{
          width: '100%',
          padding: '12px',
          backgroundColor: 'transparent',
          border: '2px solid #374151',
          borderRadius: '8px',
          color: '#9ca3af',
          fontSize: '16px',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
      >
        ← Back to Amount
      </button>
    </div>
  );
};

// Crypto Network Selection Step
const CryptoNetworkStep: React.FC<{
  networks: PaymentNetwork[];
  onSelect: (network: PaymentNetwork) => void;
  onBack: () => void;
}> = ({ networks, onSelect, onBack }) => {
  return (
    <div>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px', textAlign: 'center' }}>
        🌐 Select USDT Network
      </h2>

      <p style={{ color: '#9ca3af', marginBottom: '30px', textAlign: 'center' }}>
        Choose your preferred USDT network for payment:
      </p>

      {/* Network Options */}
      <div style={{ marginBottom: '30px' }}>
        {networks.length === 0 ? (
          <div style={{
            padding: '40px 20px',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            border: '2px solid rgba(239, 68, 68, 0.3)',
            borderRadius: '12px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚠️</div>
            <h3 style={{ color: '#ef4444', fontSize: '18px', fontWeight: 'bold', margin: '0 0 12px 0' }}>
              No Payment Networks Available
            </h3>
            <p style={{ color: '#9ca3af', fontSize: '14px', margin: '0 0 16px 0' }}>
              Payment networks are currently being configured. Please contact support or try again later.
            </p>
            <p style={{ color: '#6b7280', fontSize: '12px', margin: 0 }}>
              Admin: Add wallet addresses via Admin Panel → Wallet Manager
            </p>
          </div>
        ) : (
          networks.map((network) => (
            <button
              key={network.id}
              onClick={() => onSelect(network)}
              style={{
                width: '100%',
                padding: '20px',
                backgroundColor: 'rgba(31, 41, 55, 0.8)',
                border: '2px solid #374151',
                borderRadius: '12px',
                color: 'white',
                marginBottom: '16px',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  <span style={{ fontSize: '24px' }}>{network.icon}</span>
                  <div style={{ textAlign: 'left' }}>
                    <h3 style={{ margin: '0 0 4px 0', fontSize: '16px', fontWeight: 'bold' }}>
                      {network.name}
                    </h3>
                    <p style={{ margin: 0, color: '#9ca3af', fontSize: '14px' }}>
                      USDT-{network.technical}
                    </p>
                  </div>
                </div>
                <div style={{ fontSize: '20px' }}>→</div>
              </div>
            </button>
          ))
        )}
      </div>

      {/* Network Information */}
      <div style={{
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        border: '1px solid rgba(245, 158, 11, 0.3)',
        borderRadius: '12px',
        padding: '16px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#fbbf24', fontWeight: 'bold', margin: '0 0 12px 0' }}>
          ⚠️ IMPORTANT:
        </h3>
        <p style={{ color: '#d1d5db', fontSize: '14px', margin: 0 }}>
          Make sure to send USDT on the correct network only. Sending on the wrong network will result in lost funds.
        </p>
      </div>

      {/* Back Button */}
      <button
        onClick={onBack}
        style={{
          width: '100%',
          padding: '12px',
          backgroundColor: 'transparent',
          border: '2px solid #374151',
          borderRadius: '8px',
          color: '#9ca3af',
          fontSize: '16px',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
      >
        ← Back to Payment Method
      </button>
    </div>
  );
};

// Pending Payment Step
const PendingStep: React.FC<{ onClose?: () => void }> = ({ onClose }) => {
  return (
    <div style={{ textAlign: 'center' }}>
      <div style={{
        width: '80px',
        height: '80px',
        backgroundColor: 'rgba(245, 158, 11, 0.2)',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        margin: '0 auto 24px auto'
      }}>
        <svg width="40" height="40" fill="none" stroke="#f59e0b" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>

      <h2 style={{ fontSize: '24px', fontWeight: 'bold', margin: '0 0 16px 0' }}>
        🎉 Payment Submitted Successfully!
      </h2>

      <p style={{ color: '#9ca3af', marginBottom: '24px', fontSize: '16px' }}>
        Your payment has been submitted for admin review. You will receive a confirmation once approved.
      </p>

      <div style={{
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        border: '1px solid rgba(245, 158, 11, 0.3)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '24px'
      }}>
        <h3 style={{ color: '#fbbf24', fontWeight: 'bold', margin: '0 0 12px 0' }}>
          ⏱️ PROCESSING TIMES:
        </h3>
        <div style={{ color: '#d1d5db', fontSize: '14px', textAlign: 'left' }}>
          <p style={{ margin: '0 0 8px 0' }}>• Crypto payments: 1-24 hours</p>
          <p style={{ margin: 0 }}>• Bank transfers: 1-3 business days</p>
        </div>
      </div>

      <div style={{
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        border: '1px solid rgba(59, 130, 246, 0.3)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '24px'
      }}>
        <h3 style={{ color: '#60a5fa', fontWeight: 'bold', margin: '0 0 12px 0' }}>
          📧 WHAT'S NEXT:
        </h3>
        <div style={{ color: '#d1d5db', fontSize: '14px', textAlign: 'left' }}>
          <p style={{ margin: '0 0 8px 0' }}>• Admin will review your payment proof</p>
          <p style={{ margin: '0 0 8px 0' }}>• You'll receive email confirmation when approved</p>
          <p style={{ margin: '0 0 8px 0' }}>• Shares will be added to your account</p>
          <p style={{ margin: 0 }}>• You can make additional purchases anytime</p>
        </div>
      </div>

      <button
        onClick={onClose}
        style={{
          width: '100%',
          padding: '16px',
          backgroundColor: '#3b82f6',
          border: 'none',
          borderRadius: '12px',
          color: 'white',
          fontSize: '18px',
          fontWeight: 'bold',
          cursor: 'pointer',
          marginBottom: '12px'
        }}
      >
        Return to Dashboard
      </button>

      <button
        onClick={onClose}
        style={{
          width: '100%',
          padding: '12px',
          backgroundColor: 'transparent',
          border: '2px solid #374151',
          borderRadius: '8px',
          color: '#9ca3af',
          fontSize: '16px',
          cursor: 'pointer'
        }}
      >
        Close
      </button>
    </div>
  );
};

// Enhanced Amount Step Component with Quick Amounts and One-Click Purchase
const EnhancedAmountStep: React.FC<{
  amount: string;
  setAmount: (value: string) => void;
  currentPhase: CurrentPhase | null;
  calculateShares: (amount: string) => number;
  onSubmit: () => void;
  quickAmounts: number[];
  realTimePrice: number;
  savedPaymentMethods: SavedPaymentMethod[];
  onOneClickPurchase: (method: SavedPaymentMethod) => void;
  user?: any;
  selectedCurrency: 'USD' | 'ZAR';
  setSelectedCurrency: (currency: 'USD' | 'ZAR') => void;
}> = ({
  amount,
  setAmount,
  currentPhase,
  calculateShares,
  onSubmit,
  quickAmounts,
  realTimePrice,
  savedPaymentMethods,
  onOneClickPurchase,
  user,
  selectedCurrency,
  setSelectedCurrency
}) => {
  const shares = calculateShares(amount);
  const totalCost = shares * (realTimePrice || currentPhase?.price_per_share || 0);
  const [showCalculator, setShowCalculator] = useState(false);

  // Bank transfer configuration (same as BankTransferStep)
  const bankTransferConfig = {
    exchangeRate: 18, // USD to ZAR
    transactionFee: 0.10 // 10% transaction fee
  };

  // Check if user is from South Africa, Namibia, or Eswatini
  const userCountry = user?.database_user?.country_of_residence || user?.country_of_residence || user?.country_code;
  const isSouthernAfricaUser = ['ZAF', 'NAM', 'SWZ', 'ZA'].includes(userCountry);

  // Debug logging
  console.log('🌍 User country detection:', {
    user: user,
    userCountry: userCountry,
    isSouthernAfricaUser: isSouthernAfricaUser,
    database_user: user?.database_user,
    country_of_residence: user?.database_user?.country_of_residence
  });

  // Calculate ZAR amounts for bank transfer
  const zarAmount = totalCost * bankTransferConfig.exchangeRate;
  const feeAmount = zarAmount * bankTransferConfig.transactionFee;
  const totalZarAmount = zarAmount + feeAmount;

  return (
    <div>
      <h2 style={{
        fontSize: '24px',
        fontWeight: 'bold',
        marginBottom: '8px',
        textAlign: 'center',
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent'
      }}>
        🚀 Purchase Shares
      </h2>

      <p style={{ color: '#9ca3af', marginBottom: '24px', textAlign: 'center', fontSize: '16px' }}>
        Choose your share purchase amount with our streamlined interface
      </p>

      {/* Payment Method Selection for Southern Africa Users */}
      {isSouthernAfricaUser && (
        <div style={{ marginBottom: '24px' }}>
          <label style={{
            display: 'block',
            color: '#f3f4f6',
            marginBottom: '12px',
            fontSize: '16px',
            fontWeight: '600',
            textAlign: 'center'
          }}>
            💳 Payment Method & Currency
          </label>
          <div style={{
            display: 'flex',
            gap: '12px',
            justifyContent: 'center',
            marginBottom: '16px'
          }}>
            <button
              onClick={() => setSelectedCurrency('USD')}
              style={{
                padding: '12px 24px',
                borderRadius: '8px',
                border: selectedCurrency === 'USD' ? '2px solid #f59e0b' : '2px solid #374151',
                backgroundColor: selectedCurrency === 'USD' ? '#1f2937' : '#111827',
                color: selectedCurrency === 'USD' ? '#f59e0b' : '#9ca3af',
                fontWeight: selectedCurrency === 'USD' ? 'bold' : 'normal',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
            >
              💰 USDT Payment (USD)
            </button>
            <button
              onClick={() => setSelectedCurrency('ZAR')}
              style={{
                padding: '12px 24px',
                borderRadius: '8px',
                border: selectedCurrency === 'ZAR' ? '2px solid #f59e0b' : '2px solid #374151',
                backgroundColor: selectedCurrency === 'ZAR' ? '#1f2937' : '#111827',
                color: selectedCurrency === 'ZAR' ? '#f59e0b' : '#9ca3af',
                fontWeight: selectedCurrency === 'ZAR' ? 'bold' : 'normal',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
            >
              🏦 Bank Transfer (ZAR)
            </button>
          </div>
          {selectedCurrency === 'ZAR' && (
            <div style={{
              backgroundColor: '#1f2937',
              border: '1px solid #374151',
              borderRadius: '8px',
              padding: '12px',
              textAlign: 'center'
            }}>
              <div style={{ color: '#f59e0b', fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
                💱 Exchange Rate: R{bankTransferConfig.exchangeRate} per $1 USD + 10% fee
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>
                All amounts will be converted to ZAR for bank transfer
              </div>
            </div>
          )}
        </div>
      )}

      {/* Real-time Price Display */}
      <div className={styles.priceDisplay} style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
          <span style={{ fontSize: '20px' }}>📈</span>
          <div>
            <div style={{ color: '#10b981', fontSize: '18px', fontWeight: 'bold' }}>
              {selectedCurrency === 'ZAR' ?
                `R${(realTimePrice * bankTransferConfig.exchangeRate * (1 + bankTransferConfig.transactionFee)).toFixed(2)} per share` :
                `$${realTimePrice.toFixed(2)} per share`
              }
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>
              {currentPhase?.phase_name} • Live Price
              {selectedCurrency === 'ZAR' && ' (incl. exchange rate + 10% fee)'}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Amount Buttons */}
      <div style={{ marginBottom: '24px' }}>
        <label style={{
          display: 'block',
          color: '#f3f4f6',
          marginBottom: '12px',
          fontSize: '16px',
          fontWeight: '600'
        }}>
          Quick Select Amount
        </label>
        <div className={styles.quickAmountGrid}>
          {quickAmounts.map(quickAmount => {
            // For ZAR, convert USD amount to ZAR equivalent for display
            const displayAmount = selectedCurrency === 'ZAR' ?
              quickAmount * bankTransferConfig.exchangeRate * (1 + bankTransferConfig.transactionFee) :
              quickAmount;
            const currencySymbol = selectedCurrency === 'ZAR' ? 'R' : '$';

            return (
              <button
                key={quickAmount}
                onClick={() => {
                  console.log('🔢 Quick amount clicked:', quickAmount);
                  console.log('📊 Current amount string:', amount);
                  const currentAmount = parseFloat(amount) || 0;
                  console.log('📊 Current amount parsed:', currentAmount);
                  const newAmount = currentAmount + quickAmount;
                  console.log('📊 New amount calculated:', newAmount);
                  setAmount(newAmount.toString());
                  console.log('✅ Amount set to:', newAmount.toString());
                }}
                className={styles.quickAmountButton}
              >
                +{currencySymbol}{displayAmount.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                {selectedCurrency === 'ZAR' && (
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginTop: '2px' }}>
                    (${quickAmount} USD)
                  </div>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Custom Amount Input */}
      <div style={{ marginBottom: '24px' }}>
        <label style={{
          display: 'block',
          color: '#f3f4f6',
          marginBottom: '8px',
          fontSize: '16px',
          fontWeight: '600'
        }}>
          Custom Amount (USD)
          {selectedCurrency === 'ZAR' && (
            <span style={{ color: '#f59e0b', fontSize: '14px', marginLeft: '8px' }}>
              • Will be converted to ZAR
            </span>
          )}
        </label>
        <div style={{ position: 'relative' }}>
          <span style={{
            position: 'absolute',
            left: '16px',
            top: '50%',
            transform: 'translateY(-50%)',
            color: '#9ca3af',
            fontSize: '18px',
            fontWeight: 'bold'
          }}>$</span>
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="Enter amount in USD"
            className={styles.customAmountInput}
          />
        </div>
        {selectedCurrency === 'ZAR' && amount && parseFloat(amount) > 0 && (
          <div style={{
            marginTop: '8px',
            padding: '8px 12px',
            backgroundColor: '#1f2937',
            border: '1px solid #374151',
            borderRadius: '6px',
            fontSize: '14px',
            color: '#f59e0b'
          }}>
            💱 ZAR Equivalent: R{(parseFloat(amount) * bankTransferConfig.exchangeRate * (1 + bankTransferConfig.transactionFee)).toLocaleString(undefined, { maximumFractionDigits: 2 })}
          </div>
        )}
      </div>

      {/* Purchase Preview */}
      {amount && parseFloat(amount) > 0 && (
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '24px'
        }}>
          <h3 style={{
            color: '#fbbf24',
            fontWeight: 'bold',
            margin: '0 0 16px 0',
            fontSize: '18px'
          }}>
            📋 Purchase Preview
          </h3>
          <div className={styles.purchasePreviewGrid}>
            <div className={styles.previewCard}>
              <div style={{ color: '#3b82f6', fontSize: '24px', fontWeight: 'bold' }}>
                {shares.toLocaleString()}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '14px' }}>Shares</div>
            </div>
            <div className={styles.previewCard}>
              <div style={{ color: '#10b981', fontSize: '24px', fontWeight: 'bold' }}>
                {selectedCurrency === 'ZAR' ?
                  `R${totalZarAmount.toLocaleString(undefined, { maximumFractionDigits: 2 })}` :
                  `$${totalCost.toFixed(2)}`
                }
              </div>
              <div style={{ color: '#9ca3af', fontSize: '14px' }}>
                Total Cost
                {selectedCurrency === 'ZAR' && (
                  <div style={{ fontSize: '11px', color: '#f59e0b' }}>
                    (${totalCost.toFixed(2)} USD + fees)
                  </div>
                )}
              </div>
            </div>
            <div className={styles.previewCard}>
              <div style={{ color: '#f59e0b', fontSize: '24px', fontWeight: 'bold' }}>
                {selectedCurrency === 'ZAR' ?
                  `R${(realTimePrice * bankTransferConfig.exchangeRate * (1 + bankTransferConfig.transactionFee)).toFixed(2)}` :
                  `$${realTimePrice.toFixed(2)}`
                }
              </div>
              <div style={{ color: '#9ca3af', fontSize: '14px' }}>
                Price per Share
                {selectedCurrency === 'ZAR' && (
                  <div style={{ fontSize: '11px', color: '#f59e0b' }}>
                    (${realTimePrice.toFixed(2)} USD + fees)
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* One-Click Purchase Options */}
      {savedPaymentMethods.length > 0 && amount && parseFloat(amount) > 0 && (
        <div style={{ marginBottom: '24px' }}>
          <h3 style={{
            color: '#f3f4f6',
            fontSize: '16px',
            fontWeight: '600',
            marginBottom: '12px'
          }}>
            ⚡ One-Click Purchase
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {savedPaymentMethods.slice(0, 2).map(method => (
              <button
                key={method.id}
                onClick={() => onOneClickPurchase(method)}
                className={styles.oneClickButton}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <span style={{ fontSize: '20px' }}>
                    {method.type === 'crypto' ? '💳' : '🏦'}
                  </span>
                  <div style={{ textAlign: 'left' }}>
                    <div>{method.name}</div>
                    <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                      {method.type === 'crypto' ? method.network?.name : 'Bank Transfer'}
                    </div>
                  </div>
                </div>
                <span style={{ fontSize: '16px' }}>⚡</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Continue Button */}
      <button
        onClick={onSubmit}
        disabled={!amount || parseFloat(amount) <= 0}
        className={styles.continueButton}
      >
        Continue to Payment Method →
      </button>
    </div>
  );
};

export default SharePurchaseFlow;
